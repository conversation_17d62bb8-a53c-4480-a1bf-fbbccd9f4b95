<?php

namespace App\Observers;

use App\Http\Helpers\Common;
use App\Models\Log;
use App\Models\Properties;
use App\Services\EventTracking\EventTrackingConstants;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class PropertiesObserver
{
    /**
     * Handle the Properties "created" event.
     *
     * @param  \App\Models\Properties  $properties
     * @return void
     */
    public function created(Properties $properties)
    {
        $letters = Str::random(3); // Generate 3 random letters
            $number = str_pad(rand(0, 999), 3, '0', STR_PAD_LEFT); // Generate a random 3-digit number
            // $prefix = 'Da' . '-'; // Use the product type as prefix
            $code = Str::upper($letters) . '-' . $number; // Concatenate the letters, number, and prefix

            // Check if any other products have the same code
            $existingProduct = Properties::where('property_code', $code)->first();
            while ($existingProduct !== null) {
                $letters = Str::random(3);
                $number = str_pad(rand(0, 999), 3, '0', STR_PAD_LEFT);
                $code = Str::upper($letters) . '-' . $number;
                $existingProduct = Properties::where('property_code', $code)->first();
            }
            $helpers = new Common();
            $properties->property_code = $code;
            if($properties->platform_id == 6){
                $properties->slug = $helpers->pretty_url($properties->name, $code);
            }
            $properties->save();

            // Track host_property_listing_started event across all platforms
            if ($properties->host_id) {
                \App\Jobs\EventTrackingJob::track(
                    EventTrackingConstants::HOST_PROPERTY_LISTING_STARTED,
                    [
                        'host_id' => $properties->host_id,
                        'property_id' => $properties->id,
                        'host_listing_progress_status' => 'started'
                    ],
                    $properties->host_id
                );
            }

    }

    /**
     * Handle the Properties "updated" event.
     *
     * @param  \App\Models\Properties  $properties
     * @return void
     */
    public function updated(Properties $properties)
    {
        // Track property status changes for MoEngage events
        if ($properties->isDirty('status')) {
            $newStatus = $properties->status;

            // Property listing completed
            if ($newStatus === 'Listed') {
                \App\Jobs\EventTrackingJob::trackHostPropertyListingCompleted(
                    $properties->host_id,
                    $properties->id
                );
            }

            // Property approved by Darent
            if ($newStatus === 'Listed' && $properties->getOriginal('status') !== 'Listed') {
                \App\Jobs\EventTrackingJob::trackHostPropertyApproved(
                    $properties->host_id,
                    $properties->id,
                    'approved'
                );
            }

            // Property rejected by Darent
            if ($newStatus === 'Rejected') {
                \App\Jobs\EventTrackingJob::trackHostPropertyRejected(
                    $properties->host_id,
                    $properties->id,
                    'rejected_by_admin'
                );
            }
        }

        //
            // $oldData = $properties->getOriginal();
            // $newData = $properties->getAttributes();
            // $changedData = [];

            // foreach ($newData as $key => $value) {
            //     if ($oldData[$key] != $value) {
            //         $changedData[$key] = $value;
            //     }
            // }
            // $user_id = auth()->guard($guard)->id();
            // $user_name = auth()->guard($guard)->user()->first_name.' '.auth()->guard($guard)->user()->last_name;
            // // dd($user_name);
            // $log = $properties->logs()->create([
            //     'user_id' => $user_id,
            //     'change_by' => $user_name,
            //     'old' => json_encode($oldData),
            //     'new' => json_encode($newData),
            //     'change_values' => json_encode($changedData)
            // ]);
    }

    /**
     * Handle the Properties "deleted" event.
     *
     * @param  \App\Models\Properties  $properties
     * @return void
     */
    public function deleted(Properties $properties)
    {
        //
        $user = Auth::guard('admin')->user();
        if($user){
            // Log the deletion
            Log::create([
                'user_id' => $user->id,
                'loggable_id' => $properties->id,
                'loggable_type' => Properties::class,
                'action' => 'delete',
                'created_at' => now(),
            ]);
        }
    }

    /**
     * Handle the Properties "restored" event.
     *
     * @param  \App\Models\Properties  $properties
     * @return void
     */
    public function restored(Properties $properties)
    {
        //
    }

    /**
     * Handle the Properties "force deleted" event.
     *
     * @param  \App\Models\Properties  $properties
     * @return void
     */
    public function forceDeleted(Properties $properties)
    {
        //
    }
}
