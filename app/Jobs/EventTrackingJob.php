<?php

namespace App\Jobs;

use App\Services\EventTracking\EventTrackingConstants;
use App\Services\EventTracking\EventTrackingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

/**
 * Job class for unified event tracking
 *
 * This class provides a queued job to track events across multiple platforms
 * and serves as a direct interface to the EventTrackingService.
 */
class EventTrackingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The event name to track
     *
     * @var string
     */
    protected $eventName;

    /**
     * The attributes for the event
     *
     * @var array
     */
    protected $attributes;

    /**
     * The user ID for the event
     *
     * @var string|int|null
     */
    protected $userId;

    /**
     * The platform for the event
     *
     * @var string
     */
    protected $platform;

    /**
     * Create a new job instance.
     *
     * @param string $eventName
     * @param array $attributes
     * @param string|int|null $userId
     * @param string $platform
     * @return void
     */
    public function __construct($eventName, $attributes = [], $userId = null, $platform = 'WEB')
    {
        $this->eventName = $eventName;
        $this->attributes = $attributes;
        $this->userId = $userId;
        $this->platform = $platform;

        // Use the default queue driver
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Skip if event tracking is disabled
        if (!config('event-tracking.enable')) {
            return;
        }

        // Skip if no user ID is available
        if (!$this->userId) {
            return;
        }

        try {
            // Track the event using EventTrackingService
            /** @var EventTrackingService $trackingService */
            $trackingService = app(EventTrackingService::class);
            $trackingService->trackEvent($this->eventName, $this->attributes, $this->userId, $this->platform);
        } catch (\Exception $e) {
            // Log the error but don't break the application flow
            Log::error('Event Tracking Job Error', [
                'event' => $this->eventName,
                'attributes' => $this->attributes,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Track an event across all enabled platforms
     *
     * @param string $eventName
     * @param array $attributes
     * @param string|int|null $userId
     * @param string $platform
     * @return void
     */
    public static function track($eventName, $attributes = [], $userId = null, $platform = 'WEB')
    {
        // Skip if event tracking is disabled
        if (!config('event-tracking.enable')) {
            return;
        }

        // Get user ID from auth if not provided
        if (!$userId && Auth::check()) {

            $userId = Auth::id();
        }

        // Skip if no user ID is available
        if (!$userId) {
            return;
        }

        // Dispatch the job to track the event
        self::dispatch($eventName, $attributes, $userId, $platform);
    }

    /**
     * Track booking accepted event
     *
     * @param int $bookingId
     * @param int $guestId
     * @param int $hostId
     * @param int $propertyId
     * @param string $checkInDate
     * @param string $checkOutDate
     * @return void
     */
    public static function trackBookingAccepted($bookingId, $guestId, $hostId, $propertyId, $checkInDate, $checkOutDate)
    {
        self::track(
            EventTrackingConstants::BOOKING_ACCEPTED,
            [
                'booking_id' => $bookingId,
                'guest_id' => $guestId,
                'host_id' => $hostId,
                'property_id' => $propertyId,
                'check_in_date' => $checkInDate,
                'check_out_date' => $checkOutDate
            ],
            $guestId
        );
    }

    /**
     * Track booking declined event
     *
     * @param int $bookingId
     * @param int $guestId
     * @param int $hostId
     * @param int $propertyId
     * @param string $reason
     * @return void
     */
    public static function trackBookingDeclined($bookingId, $guestId, $hostId, $propertyId, $reason = null)
    {
        self::track(
            EventTrackingConstants::BOOKING_DECLINED,
            [
                'booking_id' => $bookingId,
                'guest_id' => $guestId,
                'host_id' => $hostId,
                'property_id' => $propertyId,
                'declined_reason' => $reason
            ],
            $guestId
        );
    }

    /**
     * Track add to wishlist event
     *
     * @param int $userId
     * @param int $propertyId
     * @param string $propertyType
     * @param string $city
     * @param float $priceRange
     * @return void
     */
    public static function trackAddToWishlist($userId, $propertyId, $propertyType = null, $city = null, $priceRange = null)
    {
        self::track(
            EventTrackingConstants::ADD_TO_WISHLIST,
            [
                'guest_id' => $userId,
                'property_id' => $propertyId,
                'property_type' => $propertyType,
                'city' => $city,
                'price_range' => $priceRange
            ],
            $userId
        );
    }

    /**
     * Track booking cancelled by guest event
     *
     * @param int $bookingId
     * @param int $guestId
     * @param int $hostId
     * @param int $propertyId
     * @param string $reason
     * @param string $host_cancellation_policy
     * @return void
     */
    public static function trackBookingCancelledByGuest($bookingId, $guestId, $hostId, $propertyId, $reason = null, $host_cancellation_policy = null)
    {
        self::track(
            EventTrackingConstants::BOOKING_CANCELLED_BY_GUEST,
            [
                'booking_id' => $bookingId,
                'guest_id' => $guestId,
                'host_id' => $hostId,
                'property_id' => $propertyId,
                'cancellation_reason' => $reason,
                'host_cancellation_policy' => $host_cancellation_policy
            ],
            $guestId
        );
    }

    /**
     * Track booking started event
     *
     * @param int $guestId
     * @param int $propertyId
     * @param string $bookingStage
     * @param string $sessionTimestamp
     * @param float $price
     * @param string $city
     * @param string $deviceLanguage
     * @return void
     */
    public static function trackBookingStarted($guestId, $propertyId, $bookingStage, $sessionTimestamp, $price, $city, $deviceLanguage)
    {
        self::track(
            EventTrackingConstants::BOOKING_STARTED,
            [
                'guest_id' => $guestId,
                'property_id' => $propertyId,
                'booking_stage' => $bookingStage,
                'session_timestamp' => $sessionTimestamp,
                'price' => $price,
                'city' => $city,
                'device_language' => $deviceLanguage
            ],
            $guestId
        );
    }

    /**
     * Track booking completed event
     *
     * @param int $bookingId
     * @param int $guestId
     * @param int $hostId
     * @param int $propertyId
     * @param string $checkInDate
     * @param string $checkOutDate
     * @param string $paymentStatus
     * @param float $amountPaid
     * @return void
     */
    public static function trackBookingCompleted($bookingId, $guestId, $hostId, $propertyId, $checkInDate, $checkOutDate, $paymentStatus, $amountPaid)
    {
        self::track(
            EventTrackingConstants::BOOKING_COMPLETED,
            [
                'booking_id' => $bookingId,
                'guest_id' => $guestId,
                'host_id' => $hostId,
                'property_id' => $propertyId,
                'check_in_date' => $checkInDate,
                'check_out_date' => $checkOutDate,
                'payment_status' => $paymentStatus,
                'amount_paid' => $amountPaid
            ],
            $guestId
        );
    }

    /**
     * Track review published event
     *
     * @param int $bookingId
     * @param int $guestId
     * @param int $hostId
     * @param int $reviewScore
     * @param string $reviewText
     * @return void
     */
    public static function trackReviewPublished($bookingId, $guestId, $hostId, $reviewScore, $reviewText = null)
    {
        self::track(
            EventTrackingConstants::REVIEW_PUBLISHED,
            [
                'booking_id' => $bookingId,
                'guest_id' => $guestId,
                'host_id' => $hostId,
                'review_score' => $reviewScore,
                'review_text' => $reviewText
            ],
            $guestId
        );
    }

    /**
     * Track host guest review published event
     *
     * @param int $hostId
     * @param int $guestId
     * @param int $guestReviewScore
     * @param string $guestReviewText
     * @return void
     */
    public static function trackHostGuestReviewPublished($hostId, $guestId, $guestReviewScore, $guestReviewText = null)
    {
        self::track(
            EventTrackingConstants::HOST_GUEST_REVIEW_PUBLISHED,
            [
                'host_id' => $hostId,
                'guest_id' => $guestId,
                'guest_review_score' => $guestReviewScore,
                'guest_review_text' => $guestReviewText
            ],
            $hostId
        );
    }

    /**
     * Track guest property viewed event
     *
     * @param int $guestId
     * @param int $propertyId
     * @param string $city
     * @param string $priceRange
     * @param string $propertyType
     * @return void
     */
    public static function trackGuestPropertyViewed($guestId, $propertyId, $city = null, $priceRange = null, $propertyType = null)
    {
        self::track(
            EventTrackingConstants::GUEST_PROPERTY_VIEWED,
            [
                'guest_id' => $guestId,
                'property_id' => $propertyId,
                'city' => $city,
                'price_range' => $priceRange,
                'property_type' => $propertyType
            ],
            $guestId
        );
    }

    /**
     * Track host dashboard opened event
     *
     * @param int $hostId
     * @param string $sessionTimestamp
     * @param string $platform
     * @param string $appVersion
     * @return void
     */
    public static function trackHostDashboardOpened($hostId, $sessionTimestamp, $platform, $appVersion = null)
    {
        self::track(
            EventTrackingConstants::HOST_DASHBOARD_OPENED,
            [
                'host_id' => $hostId,
                'session_timestamp' => $sessionTimestamp,
                'platform' => $platform,
                'app_version' => $appVersion
            ],
            $hostId
        );
    }

    /**
     * Track host property listing completed event
     *
     * @param int $hostId
     * @param int $propertyId
     * @return void
     */
    public static function trackHostPropertyListingCompleted($hostId, $propertyId)
    {
        self::track(
            EventTrackingConstants::HOST_PROPERTY_LISTING_COMPLETED,
            [
                'host_id' => $hostId,
                'property_id' => $propertyId
            ],
            $hostId
        );
    }

    /**
     * Track host property approved event
     *
     * @param int $hostId
     * @param int $propertyId
     * @param string $hostApprovalStatus
     * @return void
     */
    public static function trackHostPropertyApproved($hostId, $propertyId, $hostApprovalStatus)
    {
        self::track(
            EventTrackingConstants::HOST_PROPERTY_APPROVED,
            [
                'host_id' => $hostId,
                'property_id' => $propertyId,
                'host_approval_status' => $hostApprovalStatus
            ],
            $hostId
        );
    }

    /**
     * Track host payout successful event
     *
     * @param int $hostId
     * @param int $bookingId
     * @param float $hostPayoutAmount
     * @param string $hostPayoutStatus
     * @return void
     */
    public static function trackHostPayoutSuccessful($hostId, $bookingId, $hostPayoutAmount, $hostPayoutStatus)
    {
        self::track(
            EventTrackingConstants::HOST_PAYOUT_SUCCESSFUL,
            [
                'host_id' => $hostId,
                'booking_id' => $bookingId,
                'host_payout_amount' => $hostPayoutAmount,
                'host_payout_status' => $hostPayoutStatus
            ],
            $hostId
        );
    }

    /**
     * Track host calendar not updated event
     *
     * @param int $hostId
     * @param int $propertyId
     * @param int $daysSinceLastUpdate
     * @param string $calendarSyncStatus
     * @return void
     */
    public static function trackHostCalendarNotUpdated($hostId, $propertyId, $daysSinceLastUpdate, $calendarSyncStatus = 'not_synced')
    {
        self::track(
            EventTrackingConstants::HOST_CALENDAR_NOT_UPDATED,
            [
                'host_id' => $hostId,
                'property_id' => $propertyId,
                'host_days_since_last_update' => $daysSinceLastUpdate,
                'calendar_sync_status' => $calendarSyncStatus
            ],
            $hostId
        );
    }

    /**
     * Track host license expiry notification event
     *
     * @param int $hostId
     * @param string $hostLicenseId
     * @param string $hostLicenseExpiryDate
     * @param int $daysToExpiry
     * @return void
     */
    public static function trackHostLicenseExpiryNotification($hostId, $hostLicenseId, $hostLicenseExpiryDate, $daysToExpiry)
    {
        self::track(
            EventTrackingConstants::HOST_LICENSE_EXPIRY_NOTIFICATION,
            [
                'host_id' => $hostId,
                'host_license_id' => $hostLicenseId,
                'host_license_expiry_date' => $hostLicenseExpiryDate,
                'days_to_expiry' => $daysToExpiry
            ],
            $hostId
        );
    }

    /**
     * Track host property rejected event
     *
     * @param int $hostId
     * @param int $propertyId
     * @param string $hostRejectionReason
     * @return void
     */
    public static function trackHostPropertyRejected($hostId, $propertyId, $hostRejectionReason)
    {
        self::track(
            EventTrackingConstants::HOST_PROPERTY_REJECTED,
            [
                'host_id' => $hostId,
                'property_id' => $propertyId,
                'host_rejection_reason' => $hostRejectionReason
            ],
            $hostId
        );
    }

    /**
     * Track host property listing started event
     *
     * @param int $hostId
     * @param int $propertyId
     * @param string $hostListingProgressStatus
     * @return void
     */
    public static function trackHostPropertyListingStarted($hostId, $propertyId, $hostListingProgressStatus)
    {
        self::track(
            EventTrackingConstants::HOST_PROPERTY_LISTING_STARTED,
            [
                'host_id' => $hostId,
                'property_id' => $propertyId,
                'host_listing_progress_status' => $hostListingProgressStatus
            ],
            $hostId
        );
    }

    /**
     * Track guest registered event
     *
     * @param int $guestId
     * @param string $registrationMethod
     * @param string $deviceType
     * @return void
     */
    public static function trackGuestRegistered($guestId, $registrationMethod, $deviceType)
    {
        self::track(
            EventTrackingConstants::GUEST_REGISTERED,
            [
                'guest_id' => $guestId,
                'registration_method' => $registrationMethod,
                'device_type' => $deviceType
            ],
            $guestId
        );
    }

    /**
     * Track guest app installed event
     *
     * @param int $guestId
     * @param string $deviceType
     * @param string $appVersion
     * @return void
     */
    public static function trackGuestAppInstalled($guestId, $deviceType, $appVersion)
    {
        self::track(
            EventTrackingConstants::GUEST_APP_INSTALLED,
            [
                'guest_id' => $guestId,
                'device_type' => $deviceType,
                'app_version' => $appVersion
            ],
            $guestId
        );
    }

    /**
     * Track guest app opened event
     *
     * @param int $guestId
     * @param string $deviceType
     * @param string $appVersion
     * @param string $sessionTimestamp
     * @return void
     */
    public static function trackGuestAppOpened($guestId, $deviceType, $appVersion, $sessionTimestamp)
    {
        self::track(
            EventTrackingConstants::GUEST_APP_OPENED,
            [
                'guest_id' => $guestId,
                'device_type' => $deviceType,
                'app_version' => $appVersion,
                'session_timestamp' => $sessionTimestamp
            ],
            $guestId
        );
    }

    /**
     * Track guest referral sent event
     *
     * @param int $guestId
     * @param string $referralMethod
     * @param string $referralTimestamp
     * @return void
     */
    public static function trackGuestReferralSent($guestId, $referralMethod, $referralTimestamp)
    {
        self::track(
            EventTrackingConstants::GUEST_REFERRAL_SENT,
            [
                'guest_id' => $guestId,
                'referral_method' => $referralMethod,
                'referral_timestamp' => $referralTimestamp
            ],
            $guestId
        );
    }

    /**
     * Track guest referral success event
     *
     * @param int $guestId
     * @param int $referredGuestId
     * @param float $referralReward
     * @return void
     */
    public static function trackGuestReferralSuccess($guestId, $referredGuestId, $referralReward)
    {
        self::track(
            EventTrackingConstants::GUEST_REFERRAL_SUCCESS,
            [
                'guest_id' => $guestId,
                'referred_guest_id' => $referredGuestId,
                'referral_reward' => $referralReward
            ],
            $guestId
        );
    }

    /**
     * Track guest session end event
     *
     * @param int $guestId
     * @param int $sessionDuration
     * @param string $lastInteractionType
     * @param string $sessionEndTimestamp
     * @return void
     */
    public static function trackGuestSessionEnd($guestId, $sessionDuration, $lastInteractionType, $sessionEndTimestamp)
    {
        self::track(
            EventTrackingConstants::GUEST_SESSION_END,
            [
                'guest_id' => $guestId,
                'guest_session_duration' => $sessionDuration,
                'guest_last_interaction_type' => $lastInteractionType,
                'session_end_timestamp' => $sessionEndTimestamp
            ],
            $guestId
        );
    }

    /**
     * Track guest push clicked event
     *
     * @param int $guestId
     * @param string $pushNotificationId
     * @param string $pushNotificationType
     * @param string $pushClickTimestamp
     * @return void
     */
    public static function trackGuestPushClicked($guestId, $pushNotificationId, $pushNotificationType, $pushClickTimestamp)
    {
        self::track(
            EventTrackingConstants::GUEST_PUSH_CLICKED,
            [
                'guest_id' => $guestId,
                'push_notification_id' => $pushNotificationId,
                'push_notification_type' => $pushNotificationType,
                'push_click_timestamp' => $pushClickTimestamp
            ],
            $guestId
        );
    }

    /**
     * Track guest inactive event
     *
     * @param int $guestId
     * @param int $daysSinceLastActivity
     * @param string $lastActivityType
     * @return void
     */
    public static function trackGuestInactive($guestId, $daysSinceLastActivity, $lastActivityType)
    {
        self::track(
            EventTrackingConstants::GUEST_INACTIVE,
            [
                'guest_id' => $guestId,
                'days_since_last_activity' => $daysSinceLastActivity,
                'last_activity_type' => $lastActivityType
            ],
            $guestId
        );
    }

    /**
     * Track host low booking activity event
     *
     * @param int $hostId
     * @param int $daysSinceLastBooking
     * @param int $propertyId
     * @return void
     */
    public static function trackHostLowBookingActivity($hostId, $daysSinceLastBooking, $propertyId)
    {
        self::track(
            EventTrackingConstants::HOST_LOW_BOOKING_ACTIVITY,
            [
                'host_id' => $hostId,
                'days_since_last_booking' => $daysSinceLastBooking,
                'property_id' => $propertyId
            ],
            $hostId
        );
    }
}
