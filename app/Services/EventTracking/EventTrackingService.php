<?php

namespace App\Services\EventTracking;

use Illuminate\Support\Facades\Log;

class EventTrackingService
{
    /**
     * Array of event tracking adapters
     *
     * @var array
     */
    protected $adapters = [];

    /**
     * Is event tracking enabled
     *
     * @var bool
     */
    protected $enabled;

    /**
     * Debug mode
     *
     * @var bool
     */
    protected $debug;

    /**
     * Create a new event tracking service instance.
     */
    public function __construct()
    {
        $this->enabled = config('event-tracking.enable');
        $this->debug = config('event-tracking.debug');

        // Initialize adapters
        $this->initializeAdapters();
    }

    /**
     * Initialize the event tracking adapters
     *
     * @return void
     */
    protected function initializeAdapters(): void
    {
        // Add MoEngage adapter
        $this->adapters[] = app(MoEngageAdapter::class);

        // Add AppsFlyer adapter
        $this->adapters[] = app(AppsFlyerAdapter::class);

        // Additional adapters can be added here
    }

    /**
     * Track an event across all enabled platforms
     *
     * @param string $eventName Generic event name
     * @param array $attributes Event attributes
     * @param string|int|null $userId User ID
     * @param string $platform Platform (e.g., WEB, ANDROID, IOS)
     * @return array Results from each platform
     */
    public function trackEvent(string $eventName, array $attributes = [], $userId = null, string $platform = 'WEB'): array
    {
        if (!$this->enabled) {
            return ['success' => false, 'message' => 'Event tracking is disabled'];
        }

        if (!$userId) {
            return ['success' => false, 'message' => 'User ID is required'];
        }

        $results = [];
        $userId = (string) $userId; // Ensure userId is a string

        foreach ($this->adapters as $adapter) {
            if ($adapter->isEnabled()) {

                try {
                    $platformName = $adapter->getPlatformName();
                    $result = $adapter->trackEvent($userId, $eventName, $attributes, $platform);

                    $results[$platformName] = $result;

                    if ($this->debug) {
                        Log::debug("Event tracked on {$platformName}", [
                            'event' => $eventName,
                            'attributes' => $attributes,
                            'result' => $result
                        ]);
                    }
                } catch (\Exception $e) {
                    $platformName = $adapter->getPlatformName();
                    $results[$platformName] = [
                        'success' => false,
                        'error' => $e->getMessage()
                    ];

                    Log::error("Error tracking event on {$platformName}", [
                        'event' => $eventName,
                        'attributes' => $attributes,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        return [
            'success' => !empty($results),
            'results' => $results
        ];
    }

    /**
     * Get all available adapters
     *
     * @return array
     */
    public function getAdapters(): array
    {
        return $this->adapters;
    }

    /**
     * Get enabled adapters
     *
     * @return array
     */
    public function getEnabledAdapters(): array
    {
        return array_filter($this->adapters, function ($adapter) {
            return $adapter->isEnabled();
        });
    }

    /**
     * Check if event tracking is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->enabled;
    }
}
