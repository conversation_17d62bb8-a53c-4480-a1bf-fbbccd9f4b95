<?php

namespace App\Services\EventTracking;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MoEngageAdapter implements EventTrackingAdapterInterface
{
    /**
     * MoEngage App ID
     *
     * @var string
     */
    protected $appId;

    /**
     * MoEngage Campaign Events Key
     *
     * @var string
     */
    protected $campaignEventsKey;

    /**
     * MoEngage API Base URL
     *
     * @var string
     */
    protected $baseUrl;

    /**
     * Debug mode
     *
     * @var bool
     */
    protected $debug;

    /**
     * Is the adapter enabled
     *
     * @var bool
     */
    protected $enabled;


    /**
     * Create a new MoEngage adapter instance.
     */
    public function __construct()
    {
        $this->appId = config('event-tracking.platforms.moengage.app_id');
        $this->campaignEventsKey = config('event-tracking.platforms.moengage.campaign_events_key');
        $this->baseUrl = config('event-tracking.platforms.moengage.base_url');
        $this->debug = config('event-tracking.debug');
        $this->enabled = config('event-tracking.enable') && config('event-tracking.platforms.moengage.enable');
    }

    /**
     * Track an event
     *
     * @param string $userId User ID
     * @param string $eventName Event name
     * @param array $attributes Event attributes
     * @param string $platform Platform (e.g., WEB, ANDROID, IOS)
     * @return array|null Response from the tracking platform
     */
    public function trackEvent(string $userId, string $eventName, array $attributes = [], string $platform = 'WEB'): ?array
    {

        if (!$this->isEnabled()) {
            return null;
        }
        // Map the event name if needed
        $mappedEventName = $this->mapEventName($eventName);

        // Create the event payload according to MoEngage API format
        $event = [
            'type' => 'event',
            'customer_id' => $userId,
            'actions' => [
                [
                    'action' => $mappedEventName,
                    'attributes' => $attributes,
                    'platform' => $platform
                ]
            ]
        ];

        return $this->sendEvents($event);
    }

    /**
     * Send events to MoEngage API.
     *
     * @param array $events
     * @return array|null
     */
    protected function sendEvents($events): ?array
    {
        $endpoint = "{$this->baseUrl}/v1/event/{$this->appId}";

        $payload = $events;
        try {
            // Generate Basic Auth header using App ID as username and Campaign Events Key as password
            $authHeader = "Basic " . base64_encode("{$this->appId}:{$this->campaignEventsKey}");
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => $authHeader
            ])->post($endpoint, $payload);

            if ($response->successful()) {
                if ($this->debug) {
                    Log::debug('MoEngage Event Tracked', [
                        'event' => $events,
                        'response' => $response->json()
                    ]);
                }

                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            } else {
                Log::error('MoEngage API Error (Events)', [
                    'status' => $response->status(),
                    'response' => $response->json(),
                    'payload' => $payload
                ]);

                return [
                    'success' => false,
                    'error' => $response->json() ?? 'Unknown error occurred',
                    'status' => $response->status()
                ];
            }
        } catch (\Exception $e) {
            Log::error('MoEngage API Exception', [
                'message' => $e->getMessage(),
                'payload' => $payload
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Check if the adapter is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    /**
     * Get the platform name
     *
     * @return string
     */
    public function getPlatformName(): string
    {
        return 'moengage';
    }

    /**
     * Map a generic event name to a MoEngage-specific event name
     *
     * @param string $genericEventName
     * @return string
     */
    public function mapEventName(string $genericEventName): string
    {
        return $genericEventName;
    }
}
