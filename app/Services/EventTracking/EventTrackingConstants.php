<?php

namespace App\Services\EventTracking;

/**
 * Constants for event tracking
 *
 * This class provides constants for all event names used in the event tracking system.
 * Using constants instead of string literals helps prevent typos and makes it easier
 * to maintain the code.
 *
 * This class also provides mappings from our internal event names to platform-specific
 * event names for each tracking platform.
 */
class EventTrackingConstants
{
    // Booking Events
    public const BOOKING_ACCEPTED = 'booking.accepted';
    public const BOOKING_DECLINED = 'booking.declined';
    public const BOOKING_CANCELLED_BY_GUEST = 'booking.cancelled_by_guest';
    public const BOOKING_STARTED = 'booking.started';
    public const BOOKING_COMPLETED = 'booking.completed';

    // Guest Events
    public const GUEST_REGISTERED = 'guest.registered';
    public const GUEST_APP_INSTALLED = 'guest.app_installed';
    public const GUEST_APP_OPENED = 'guest.app_opened';
    public const GUEST_PROPERTY_VIEWED = 'guest.property_viewed';
    public const GUEST_SESSION_END = 'guest.session_end';
    public const GUEST_PUSH_CLICKED = 'guest.push_clicked';
    public const GUEST_INACTIVE = 'guest.inactive';
    public const GUEST_REFERRAL_SENT = 'guest.referral_sent';
    public const GUEST_REFERRAL_SUCCESS = 'guest.referral_success';

    // Host Events
    public const HOST_DASHBOARD_OPENED = 'host.dashboard_opened';
    public const HOST_PROPERTY_LISTING_STARTED = 'host.property_listing_started';
    public const HOST_PROPERTY_LISTING_COMPLETED = 'host.property_listing_completed';
    public const HOST_PROPERTY_APPROVED = 'host.property_approved';
    public const HOST_PROPERTY_REJECTED = 'host.property_rejected';
    public const HOST_GUEST_REVIEW_PUBLISHED = 'host.guest_review_published';
    public const HOST_PAYOUT_SUCCESSFUL = 'host.payout_successful';
    public const HOST_CALENDAR_NOT_UPDATED = 'host.calendar_not_updated';
    public const HOST_LICENSE_EXPIRY_NOTIFICATION = 'host.license_expiry_notification';
    public const HOST_LOW_BOOKING_ACTIVITY = 'host.low_booking_activity';
    // Wishlist Events
    public const ADD_TO_WISHLIST = 'wishlist.add';
    // Review Events
    public const REVIEW_PUBLISHED = 'review.published';

}
