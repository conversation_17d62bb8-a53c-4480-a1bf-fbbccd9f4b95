<?php

namespace App\Services\EventTracking;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AppsFlyerAdapter implements EventTrackingAdapterInterface
{
    /**
     * AppsFlyer Dev Key
     *
     * @var string
     */
    protected $devKey;

    /**
     * AppsFlyer App ID
     *
     * @var string
     */
    protected $appId;

    /**
     * AppsFlyer API Base URL
     *
     * @var string
     */
    protected $baseUrl;

    /**
     * Debug mode
     *
     * @var bool
     */
    protected $debug;

    /**
     * Is the adapter enabled
     *
     * @var bool
     */
    protected $enabled;
    /**
     * Create a new AppsFlyer adapter instance.
     */
    public function __construct()
    {
        $this->devKey = config('event-tracking.platforms.appsflyer.dev_key');
        $this->appId = config('event-tracking.platforms.appsflyer.app_id');
        $this->baseUrl = config('event-tracking.platforms.appsflyer.base_url');
        $this->debug = config('event-tracking.debug');
        $this->enabled = config('event-tracking.enable') && config('event-tracking.platforms.appsflyer.enable');
    }

    /**
     * Track an event
     *
     * @param string $userId User ID
     * @param string $eventName Event name
     * @param array $attributes Event attributes
     * @param string $platform Platform (e.g., WEB, ANDROID, IOS)
     * @return array|null Response from the tracking platform
     */
    public function trackEvent(string $userId, string $eventName, array $attributes = [], string $platform = 'WEB'): ?array
    {
        if (!$this->isEnabled()) {
            return null;
        }

        // Map the event name if needed
        $mappedEventName = $eventName;

        // Determine the platform-specific endpoint
        $platformEndpoint = $this->getPlatformEndpoint($platform);
        if (!$platformEndpoint) {
            Log::error('AppsFlyer: Unsupported platform', ['platform' => $platform]);
            return null;
        }

        // Create the event payload according to AppsFlyer API format
        $event = [
            'appsflyer_id' => $userId,
            'customer_user_id' => $userId,
            'eventName' => $mappedEventName,
            'eventValue' => json_encode($attributes),
            'eventTime' => time(),
            'ip' => request()->ip() ?? '0.0.0.0',
            'eventCurrency' => 'SAR', // Default currency, can be overridden in attributes
        ];

        // Add any additional attributes
        foreach ($attributes as $key => $value) {
            if (!isset($event[$key])) {
                $event[$key] = $value;
            }
        }

        return $this->sendEvent($event, $platformEndpoint);
    }

    /**
     * Send event to AppsFlyer API.
     *
     * @param array $event
     * @param string $platformEndpoint
     * @return array|null
     */
    protected function sendEvent(array $event, string $platformEndpoint): ?array
    {
        $endpoint = "{$this->baseUrl}/v1/{$platformEndpoint}/{$this->appId}";

        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authentication' => $this->devKey
            ])->post($endpoint, $event);

            if ($response->successful()) {
                if ($this->debug) {
                    Log::debug('AppsFlyer Event Tracked', [
                        'event' => $event,
                        'response' => $response->json()
                    ]);
                }

                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            } else {
                Log::error('AppsFlyer API Error', [
                    'status' => $response->status(),
                    'response' => $response->json(),
                    'payload' => $event
                ]);

                return [
                    'success' => false,
                    'error' => $response->json() ?? 'Unknown error occurred',
                    'status' => $response->status()
                ];
            }
        } catch (\Exception $e) {
            Log::error('AppsFlyer API Exception', [
                'message' => $e->getMessage(),
                'payload' => $event
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get the platform-specific endpoint
     *
     * @param string $platform
     * @return string|null
     */
    protected function getPlatformEndpoint(string $platform): ?string
    {
        $platform = strtoupper($platform);

        switch ($platform) {
            case 'ANDROID':
                return 'android';
            case 'IOS':
                return 'ios';
            case 'WEB':
                return 'web';
            default:
                return null;
        }
    }

    /**
     * Check if the adapter is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    /**
     * Get the platform name
     *
     * @return string
     */
    public function getPlatformName(): string
    {
        return 'appsflyer';
    }

    /**
     * Map a generic event name to an AppsFlyer-specific event name
     *
     * @param string $genericEventName
     * @return string
     */
    public function mapEventName(string $genericEventName): string
    {
        // Check if there's a mapping for this event
        if (isset($this->eventMapping[$genericEventName]) &&
            isset($this->eventMapping[$genericEventName]['appsflyer'])) {
            return $this->eventMapping[$genericEventName]['appsflyer'];
        }

        // If no mapping exists, return the original event name
        return $genericEventName;
    }
}
