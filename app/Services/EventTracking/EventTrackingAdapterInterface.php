<?php

namespace App\Services\EventTracking;

/**
 * Interface for event tracking adapters
 */
interface EventTrackingAdapterInterface
{
    /**
     * Track an event
     *
     * @param string $userId User ID
     * @param string $eventName Event name
     * @param array $attributes Event attributes
     * @param string $platform Platform (e.g., WEB, ANDROID, IOS)
     * @return array|null Response from the tracking platform
     */
    public function trackEvent(string $userId, string $eventName, array $attributes = [], string $platform = 'WEB'): ?array;
    
    /**
     * Check if the adapter is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool;
    
    /**
     * Get the platform name
     *
     * @return string
     */
    public function getPlatformName(): string;
    
    /**
     * Map a generic event name to a platform-specific event name
     *
     * @param string $genericEventName
     * @return string
     */
    public function mapEventName(string $genericEventName): string;
}
