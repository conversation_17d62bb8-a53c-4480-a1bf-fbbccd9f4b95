<?php

namespace App\Console\Commands;

use App\Jobs\EventTrackingJob;
use App\Models\Properties;
use App\Models\PropertyIcalimport;
use App\Services\EventTracking\EventTrackingConstants;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckHostCalendarUpdates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:host-calendar-updates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for hosts who have not updated their calendars recently';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Checking for hosts who have not updated their calendars...');

        // Get the date 30 days ago
        $thirtyDaysAgo = Carbon::now()->subDays(30);

        // Find properties with calendars that haven't been updated in 30 days
        $calendars = PropertyIcalimport::whereDate('icalendar_last_sync', '<', $thirtyDaysAgo)
            ->orWhereNull('icalendar_last_sync')
            ->get();

        $this->info("Found {$calendars->count()} calendars that haven't been updated in 30 days.");

        // Group by property to avoid duplicate notifications
        $propertyIds = $calendars->pluck('property_id')->unique();

        foreach ($propertyIds as $propertyId) {
            try {
                $property = Properties::find($propertyId);

                if ($property) {
                    $this->info("Sending notification for property ID: {$property->id}, Host ID: {$property->host_id}");

                    // Get the oldest calendar sync date for this property
                    $oldestCalendar = PropertyIcalimport::where('property_id', $propertyId)
                        ->orderBy('icalendar_last_sync', 'asc')
                        ->first();

                    $lastSyncDate = $oldestCalendar ? $oldestCalendar->icalendar_last_sync : null;
                    $daysSinceLastUpdate = $lastSyncDate ? Carbon::parse($lastSyncDate)->diffInDays(Carbon::now()) : 30;

                    // Track the event across all platforms
                    EventTrackingJob::trackHostCalendarNotUpdated(
                        $property->host_id,
                        $property->id,
                        $daysSinceLastUpdate,
                        'not_synced'
                    );

                    // Log the notification
                    Log::info("Host calendar not updated notification sent", [
                        'property_id' => $property->id,
                        'host_id' => $property->host_id,
                        'days_since_last_update' => $daysSinceLastUpdate,
                        'last_sync_date' => $lastSyncDate
                    ]);
                }
            } catch (\Exception $e) {
                $this->error("Error processing property ID: {$propertyId}: {$e->getMessage()}");
                Log::error("Error in CheckHostCalendarUpdates command", [
                    'property_id' => $propertyId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->info('Host calendar update check completed.');
        return 0;
    }
}
