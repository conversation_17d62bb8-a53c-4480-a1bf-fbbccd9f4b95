<?php

namespace App\Console\Commands;

use App\Jobs\EventTrackingJob;
use App\Models\Properties;
use App\Services\EventTracking\EventTrackingConstants;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckHostLicenseExpiry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:host-license-expiry';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for host licenses that are about to expire and send notifications';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Checking for host licenses that are about to expire...');

        // Get properties with licenses expiring in the next 30 days
        $today = Carbon::today();
        $thirtyDaysFromNow = $today->copy()->addDays(30);

        // Find properties with licenses expiring in the next 30 days
        $properties = Properties::whereNotNull('license_verified_at')
            ->whereNotNull('license_expiry')
            ->whereDate('license_expiry', '>=', $today)
            ->whereDate('license_expiry', '<=', $thirtyDaysFromNow)
            ->get();

        $this->info("Found {$properties->count()} properties with licenses expiring soon.");

        foreach ($properties as $property) {
            try {
                // Calculate days until expiry
                $expiryDate = Carbon::parse($property->license_expiry);
                $daysToExpiry = $today->diffInDays($expiryDate);

                // Only send notifications for specific thresholds (30, 15, 7, 3, 1 days)
                if (in_array($daysToExpiry, [30, 15, 7, 3, 1])) {
                    $this->info("Sending notification for property ID: {$property->id}, License: {$property->license_no}, Days to expiry: {$daysToExpiry}");

                    // Track the event across all platforms
                    EventTrackingJob::trackHostLicenseExpiryNotification(
                        $property->host_id,
                        $property->license_no,
                        $property->license_expiry,
                        $daysToExpiry
                    );

                    // Log the notification
                    Log::info("Host license expiry notification sent", [
                        'property_id' => $property->id,
                        'host_id' => $property->host_id,
                        'license_no' => $property->license_no,
                        'expiry_date' => $property->license_expiry,
                        'days_to_expiry' => $daysToExpiry
                    ]);
                }
            } catch (\Exception $e) {
                $this->error("Error processing property ID: {$property->id}: {$e->getMessage()}");
                Log::error("Error in CheckHostLicenseExpiry command", [
                    'property_id' => $property->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->info('Host license expiry check completed.');
        return 0;
    }
}
