<?php

namespace App\Console;

use App\Http\Services\BookingService;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;


class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */

    protected $commands = [
        \App\Console\Commands\CheckExtendBookingExpiration::class,
        \App\Console\Commands\DeleteHoldBookingDates::class,
        \App\Console\Commands\IncompleteProperty::class,
        \App\Console\Commands\CertificateActivate::class,
        \App\Console\Commands\GuestReviewScoreCalculator::class,
        \App\Console\Commands\UpdateProperty::class,
        \App\Console\Commands\CashbackExpiry::class,
        \App\Console\Commands\GetPropertyScoreCommand::class,
        \App\Console\Commands\GenerateMasterAvailabilityCommand::class,
        \App\Console\Commands\CleanBookingsTable::class,
        \App\Console\Commands\CheckHostLicenseExpiry::class,
        \App\Console\Commands\CheckHostCalendarUpdates::class
    ];


    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();
        // $schedule->command('check:extend-booking-expiration')->everyMinute();
        // $schedule->command('host:wallet')->dailyAt('12:00');
        $schedule->command('send:checkin-notification')->everyMinute();

        //for hyperbill
        $schedule->command('cron:hyperbill-check-payment-status')->everyTenMinutes();

        $schedule->command('send:pushNotification')->dailyAt('12:00');
        // $schedule->command('accepted:bookings-work')->everyMinute();
        $schedule->command('cron:check-hold-bookings')->everyMinute();
        $schedule->command('cron:check-incomplete-bookings')->everyMinute(); // This get triggered when the the Host has left the property incomplete for half an hour
        if (app()->environment('prod')) $schedule->command('calendars:sync')->hourly();
        $schedule->command('cron:certificate-activation')->everyThreeHours();
        // ->everyThreeHours();
        $schedule->command('cron:guest-review-score')->dailyAt('12:00');
        $schedule->command('cron:host-performance-score')->dailyAt('12:00');
        if (app()->environment('prod'))   $schedule->command('cron:listing-quality-score')->dailyAt('12:00');
        if (app()->environment('prod'))  $schedule->command('rental-rates:queue-fetch')->hourly();
        $schedule->command('send:notification-on-checkout')->dailyAt('00:00');

        // MoEngage event tracking commands
        $schedule->command('check:host-license-expiry')->daily();
        $schedule->command('check:host-calendar-updates')->weekly();

        $schedule->call(function () {
            $bookingService = new BookingService(); // Assuming you've wrapped this in a service class
            $bookingService->processBookingExpirations();
            $bookingService->processCancelledBookings();
        })->hourly();
        if (app()->environment('local')) {
            $schedule->command('mabaat:check-availability')->hourly();
        }
        // $schedule->command('cron:license-verification-warning-notification')->daily();

        if (app()->environment('local')) $schedule->command('payments:verify-pending')->everyTwoMinutes();

        // For update properties to unlisted and visility 0 when license = NULL
        // if (app()->environment('prod')) $schedule->command('license-based:update-properties')->everyThirtyMinutes(); commenting this because we have made license optional in all property types

        // For cashback wallet if user have not used till night
        if (app()->environment('prod')) $schedule->command('cashback:expire')->dailyAt('00:00');

        // $schedule->command('cache:clear-logger')->hourly();

        // Calculate property scores weekly
        $schedule->command('property:calculate-scores')->daily();

        // Generate master availability data daily
        $schedule->command('availability:generate --queue --chunk=100')
        ->timezone('Asia/Riyadh')
        ->dailyAt('02:00');

        // Clean bookings table hourly
        $schedule->command('bookings:clean-table')->hourly();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
