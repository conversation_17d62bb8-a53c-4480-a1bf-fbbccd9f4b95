<?php

namespace App\Providers;

use App\Models\Bookings;
use App\Models\CustomerSupport;
use App\Models\Properties;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Models\Reviews;
use App\Models\User;
use App\Observers\{AvgRatingObserver, BookingObserver, CustomerSupportObserver, PropertiesObserver, UserObserver};


class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        \App\Events\BookingPayoutSuccess::class => [
            \App\Listeners\TrackHostPayoutSuccessful::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        //
        Reviews::observe(AvgRatingObserver::class);
        Properties::observe(PropertiesObserver::class);
        User::observe(UserObserver::class);
        Bookings::observe(BookingObserver::class);
        CustomerSupport::observe(CustomerSupportObserver::class);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     *
     * @return bool
     */
    public function shouldDiscoverEvents()
    {
        return false;
    }
}
