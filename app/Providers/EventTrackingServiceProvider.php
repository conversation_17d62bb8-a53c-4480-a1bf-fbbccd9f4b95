<?php

namespace App\Providers;

use App\Services\EventTracking\AppsFlyerAdapter;
use App\Services\EventTracking\EventTrackingService;
use App\Services\EventTracking\MoEngageAdapter;
use Illuminate\Support\ServiceProvider;

class EventTrackingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // Register the MoEngage adapter
        $this->app->singleton(MoEngageAdapter::class, function ($app) {
            return new MoEngageAdapter();
        });

        // Register the AppsFlyer adapter
        $this->app->singleton(AppsFlyerAdapter::class, function ($app) {
            return new AppsFlyerAdapter();
        });

        // Register the event tracking service
        $this->app->singleton(EventTrackingService::class, function ($app) {
            return new EventTrackingService();
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Publish the configuration file
        $this->publishes([
            __DIR__.'/../../config/event-tracking.php' => config_path('event-tracking.php'),
        ], 'event-tracking-config');
    }
}
