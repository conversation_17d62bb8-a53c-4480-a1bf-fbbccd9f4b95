<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ReCaptchaV3 implements Rule
{
    protected $errorMessage;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(
        private ?string $action = null,
        private ?float $minScore = null
    ) {
        $this->errorMessage = '';
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        // Skip validation if reCAPTCHA is disabled globally or by permission setting
        if (config('services.recaptcha_v3.enable') == false || config('services.recaptcha_v3.permission') == false) {
            return true;
        }
        // Send a POST request to the google siteverify service to validate the
        $siteVerify = Http::asForm()
            ->post('https://www.google.com/recaptcha/api/siteverify', [
                'secret' => config('services.recaptcha_v3.secretKey'),
                'response' => $value,
            ]);

        $body = $siteVerify->json();
        Log::info($body);

        // This happens if google denied our request with an error
        if ($siteVerify->failed()) {
            $this->errorMessage = 'Google reCAPTCHA was not able to verify the form, please try again.';
            return false;
        }

        // This means Google successfully processed our POST request. We still need to check the results!
        if ($siteVerify->successful()) {
            $body = $siteVerify->json();
            // When this fails it means the browser didn't send a correct code. This means it's very likely a bot we should block
            if ($body['success'] !== true) {
                $this->errorMessage = 'Your form submission failed the Google reCAPTCHA verification, please try again.';
                return false;
            }

            // When this fails it means the action didn't match the one set in the button's data-action.
            // Either a bot or a code mistake. Compare form data-action and value passed to $action (should be equal).
            if (!is_null($this->action) && $this->action != $body['action']) {
                $this->errorMessage = 'The action found in the form didn\'t match the Google reCAPTCHA action, please try again.';
                return false;
            }

            // If we set a minScore treshold, verify that the spam score didn't go below it
            // More info can be found at: https://developers.google.com/recaptcha/docs/v3#interpreting_the_score
            if (!is_null($this->minScore) && $this->minScore > $body['score']) {
                $this->errorMessage = 'The Google reCAPTCHA verification score was too low, please try again.';
                return false;
            }

            return true;
        }
    }

    public function message()
    {
        return $this->errorMessage;
    }
}
