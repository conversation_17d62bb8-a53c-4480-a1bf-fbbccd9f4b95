<?php

namespace App\Listeners;

use App\Events\BookingPayoutSuccess;
use App\Jobs\EventTrackingJob;
use App\Services\EventTracking\EventTrackingConstants;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class TrackHostPayoutSuccessful implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  BookingPayoutSuccess  $event
     * @return void
     */
    public function handle(BookingPayoutSuccess $event)
    {
        try {
            $bookingPaymentDetail = $event->bookingPaymentDetail;

            // Track the host payout successful event across all platforms
            EventTrackingJob::trackHostPayoutSuccessful(
                $bookingPaymentDetail->host_id,
                $bookingPaymentDetail->booking_id,
                $bookingPaymentDetail->host_pay,
                'success'
            );

            Log::info('Host payout successful event tracked', [
                'host_id' => $bookingPaymentDetail->host_id,
                'booking_id' => $bookingPaymentDetail->booking_id,
                'amount' => $bookingPaymentDetail->host_pay
            ]);
        } catch (\Exception $e) {
            Log::error('Error tracking host payout successful event in MoEngage', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
