<?php

namespace App\Http\Controllers;

use App\Jobs\EventTrackingJob;
use App\Services\EventTracking\EventTrackingConstants;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TrackingController extends Controller
{
    /**
     * Track booking started event
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function trackBookingStarted(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'property_id' => 'required',
                'price' => 'required',
                'city' => 'nullable',
            ]);

            // Track the event
            \App\Jobs\EventTrackingJob::trackBookingStarted(
                auth()->id(),
                $request->property_id,
                'initial', // booking stage
                now()->toIso8601String(), // session timestamp
                $request->price,
                $request->city ?? '',
                $request->header('Accept-Language') ?? 'en' // device language
            );

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            Log::error('Error tracking booking_started event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }
}
