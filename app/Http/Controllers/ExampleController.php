<?php

namespace App\Http\Controllers;

use App\Jobs\EventTrackingJob;
use Illuminate\Http\Request;

class ExampleController extends Controller
{
    /**
     * Example of tracking a booking accepted event
     */
    public function trackBookingAccepted(Request $request, $bookingId)
    {
        // Get the booking details from your database
        $booking = \App\Models\Bookings::find($bookingId);
        
        if (!$booking) {
            return response()->json(['error' => 'Booking not found'], 404);
        }
        
        // Track the booking accepted event
        EventTrackingJob::trackBookingAccepted(
            $booking->id,
            $booking->user_id,
            $booking->host_id,
            $booking->property_id,
            $booking->start_date,
            $booking->end_date
        );
        
        return response()->json(['message' => 'Event tracked successfully']);
    }
    
    /**
     * Example of tracking a custom event
     */
    public function trackCustomEvent(Request $request)
    {
        // Validate the request
        $request->validate([
            'event_name' => 'required|string',
            'user_id' => 'required',
        ]);
        
        // Track the custom event
        EventTrackingJob::track(
            $request->event_name,
            $request->input('attributes', []),
            $request->user_id,
            $request->input('platform', 'WEB')
        );
        
        return response()->json(['message' => 'Custom event tracked successfully']);
    }
}
