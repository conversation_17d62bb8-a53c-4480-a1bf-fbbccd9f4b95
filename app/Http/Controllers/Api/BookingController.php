<?php

namespace App\Http\Controllers\Api;

// use App\Http\Controllers\Controller;
// use App\Http\Helpers\Common;
// use App\Models\Bookings;
// use App\Models\BookingDetails;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use App\Enums\PaymentTypeEnum;
use App\Enums\TransactionActionEnum;
use App\Notifications\UserNotify;
use App\Notifications\UserNotifySms;
use App\Http\Resources\MyBookingCollection;
use App\Http\Resources\HostReservationResource;
use App\Http\Resources\GuestSingleBookingDetailResource;
use App\Http\Resources\MyBookingVersionTwoCollection;


use DateTime;
use App\Http\{
    Helpers\Common,
    Controllers\Controller,
    Controllers\EmailController,
    Requests
};
use App\Jobs\EventTrackingJob;
use App\Services\EventTracking\EventTrackingConstants;
use App\Http\Services\FatoorahService;
use App\Http\Services\HyperpayService;
use App\Http\Services\HyperpaySdkService;
use App\Http\Services\MoyasarService;
use App\Models\{
    Bank,
    Bookings,
    BookingDetails,
    Messages,
    Penalty,
    Payouts,
    Properties,
    PayoutPenalties,
    PropertyDates,
    PropertyFees,
    Settings,
    Currency,
    UserCards,
    Country,
    User,
    PromoCodeUsage,
    PromoCode,
    BookingStatusLog,
    ElmDocument,
    PropertyChatHead,
    PropertyChat,
    CustomPricing,
    NewUserWallet,
    PaymentMethods,
    Transactions
};
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Validation\ValidationException;

class BookingController extends Controller
{
    public $myfatoorah_apiURL;
    public $myfatoorah_apikey;

    protected $helper;

    public function __construct()
    {
        $this->helper = new Common;
        if (env('MYFATOORAH_MODE') == 'test') {
            $this->myfatoorah_apiURL = 'https://apitest.myfatoorah.com/v2/';
            $this->myfatoorah_apikey = env('MYFATOORAH_API_KEY_TEST'); //Test token value to be placed here: https://myfatoorah.readme.io/docs/test-token
            $this->curl_ssl = false;
        } else {
            $this->myfatoorah_apiURL = 'https://api-sa.myfatoorah.com/v2/';
            $this->myfatoorah_apikey = env('MYFATOORAH_API_KEY_LIVE'); //Live token value to be placed here: https://myfatoorah.readme.io/docs/live-token
            $this->curl_ssl = true;
        }
    }

    public function index(Request $request)
    {
        try {

            $data['title'] = 'Booking Details';
            $data['booking_id'] = $request->id;
            $data['result'] = Bookings::find($request->id);
            $data['host_id'] = $data['result']->host_id;
            $data['booking_type'] = $data['result']->booking_type;

            if (!$data['result'] || $data['result']->host_id != Auth::user()->id) {

                return response()->json(["message" => "failure"], 404);
                // abort('404');
            }
            $data['price_list'] = json_decode($this->helper->getPrice($data['result']->property_id, $data['result']->start_date, $data['result']->end_date, $data['result']->guest, true));
            $data['symbol'] = $this->helper->getCurrentCurrencySymbol();

            return response()->json(["message" => "Success", "data" => $data], 200);
            // return view('booking.detail', $data);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function myBookings(Request $request)
    {

        // return response()->json(["message"=>"Yes"]);
        // switch ($request->status) {
        //     case 'Expired':
        //         $params  = [['created_at', '<', Carbon::yesterday()], ['status', '!=', 'Accepted']];
        //         break;
        //     case 'Current':
        //         $params  = [['start_date', '<=', date('Y-m-d')], ['end_date', '>=', date('Y-m-d')],['status', 'Accepted']];
        //         break;
        //     case 'Upcoming':
        //         $params  = [['start_date', '>', date('Y-m-d')], ['status', 'Accepted']];
        //         break;
        //     case 'Completed':
        //         $params  = [['end_date', '<', date('Y-m-d')],['status', 'Accepted']];
        //         break;
        //     case 'Pending':
        //         $params           = [['created_at', '>', Carbon::yesterday()], ['status', $request->status]];
        //         break;
        //     default:
        //         $params           = [];
        //         break;
        // }
        if (request()->route()->getPrefix() == "api/v2") {
            $request->validate([
                'size' => 'required|integer|min:1,max:10000',
                'page' => 'required|integer:min:1'
            ]);
            $size = $request->size;
            $data = isHostOrCohostQuery(Bookings::with('users', 'properties', 'property_description'), auth()->id(), selectData: 'bookings.*, pchs.id as chat_head_id', isRaw: true)
                ->leftJoin(
                    'property_chat_heads as pchs',
                    fn($q1) => $q1->on('pchs.property_id', 'bookings.property_id')->on('pchs.guest_id', 'bookings.user_id')
                )->whereIn('bookings.status', ['Processing', 'Pending', 'Unpaid', 'Accepted'])
                ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
                ->orderBy('bookings.id', 'desc')
                ->paginate($size);
            return new MyBookingVersionTwoCollection($data);
        }

        $size = $request->size;
        $data = isHostOrCohostQuery(Bookings::with('users', 'properties', 'property_description'), auth()->id())
            ->whereNotIn('bookings.status', ['Cancelled', 'Expired', 'Declined'])
            ->where('start_date', '>=', now()->format('Y-m-d'))->orderBy('bookings.id', 'desc')
            ->paginate($size);

        return new MyBookingCollection($data);
    }

    public function accept(Request $request, EmailController $email)
    {
        try {
            $booking = Bookings::with('properties')->find($request->id);

            $penalty = Penalty::where('user_id', Auth::user()->id)->where('remaining_penalty', '!=', 0)->get();
            $penalty_result = $this->helper->host_penalty_check($penalty, $booking->host_payout, $booking->currency_code);

            $booking->status = 'Processing';
            $booking->accepted_at = date('Y-m-d H:i:s');
            $booking->save();

            BookingStatusLog::updateOrCreate(
                ['booking_id' => $booking->id],
                ['status' => $booking->status, 'changed_by' => Auth::id()]
            );

            // Track booking_accepted event across all platforms
            \App\Jobs\EventTrackingJob::trackBookingAccepted(
                $booking->id,
                $booking->user_id,
                $booking->host_id,
                $booking->property_id,
                $booking->start_date,
                $booking->end_date
            );

            $payouts = new Payouts;
            $payouts->booking_id = $request->id;
            $payouts->property_id = $booking->property_id;
            $payouts->user_id = $booking->host_id;
            $payouts->user_type = 'host';
            $payouts->amount = $penalty_result['host_amount'];
            $payouts->penalty_amount = $penalty_result['penalty_total'];
            $payouts->currency_code = $booking->currency_code;
            $payouts->status = 'Future';
            $payouts->save();

            $panalty_ids = explode(',', $penalty_result['penalty_ids']);
            $panalty_amounts = explode(',', $penalty_result['panalty_amounts']);

            for ($i = 0; $i < count($panalty_ids); $i++) {
                if ($panalty_ids[$i] != '' && $panalty_amounts[$i] != '') {
                    $payout_penalties = new PayoutPenalties;
                    $payout_penalties->payout_id = $payouts->id;
                    $payout_penalties->penalty_id = $panalty_ids[$i];
                    $payout_penalties->amount = $panalty_amounts[$i];
                    $payout_penalties->save();
                }
            }

            if (!empty($request->message)) {
                $messages = new Messages;
                $messages->property_id = $booking->property_id;
                $messages->booking_id = $booking->id;
                $messages->receiver_id = $booking->user_id;
                $messages->sender_id = Auth::user()->id;
                $messages->message = $request->message;
                $messages->type_id = 5;
                $messages->save();
            }

            $status = 'Processing';
            // $email->bookingAcceptedOrDeclined($request->id, $status);
            (User::find($booking->user_id))->notify(new UserNotify(
                route('guest_reservation_detail', ['code' => $booking->code]),
                route('guest_reservation'),
                data: [
                    'slug' => 'reservation',
                    'tab' => '0',
                    'booking_id' => $booking->code,
                    'is_accepted' => $booking->status == 'Accepted' ? true : false
                ]
            ));
            return response()->json(['message' => 'Success', 'data' => $booking], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function decline(Request $request, EmailController $email)
    {
        try {
            $booking = Bookings::with('properties')->find($request->id);
            $booking->status = 'Declined';
            // $booking->declined_at     = date('Y-m-d H:i:s');
            $booking->declined_at = Carbon::now()->timezone('Asia/Riyadh')->format('Y-m-d H:i:s');
            $booking->cancelled_by = "Host";
            $booking->save();

            $days = $this->helper->get_days($booking->start_date, $booking->end_date);

            for ($j = 0; $j < count($days) - 1; $j++) {
                PropertyDates::where('property_id', $booking->property_id)->where('date', $days[$j])->where('status', 'Not available')->delete();
            }

            BookingStatusLog::updateOrCreate(
                ['booking_id' => $booking->id],
                ['status' => $booking->status, 'changed_by' => Auth::id()]
            );

            $booking_details = new BookingDetails;
            $booking_details->booking_id = $request->id;
            $booking_details->field = 'decline_reason';
            $booking_details->value = ($request->decline_reason == 'other') ? $request->decline_reason_other : $request->decline_reason;
            $booking_details->save();

            // Track booking_declined event across all platforms
            \App\Jobs\EventTrackingJob::trackBookingDeclined(
                $booking->id,
                $booking->user_id,
                $booking->host_id,
                $booking->property_id,
                $booking_details->value
            );

            $payouts = new Payouts;
            $payouts->booking_id = $request->id;
            $payouts->property_id = $booking->property_id;
            $payouts->user_id = $booking->user_id;
            $payouts->user_type = 'guest';
            $payouts->amount = $booking->original_guest_payout;
            $payouts->penalty_amount = 0;
            $payouts->currency_code = $booking->currency_code;
            $payouts->status = 'Future';
            $payouts->save();

            if ($request->block_calendar == 'yes') {
                $days = $this->helper->get_days($booking->start_date, $booking->end_date);

                for ($i = 0; $i < count($days) - 1; $i++) {
                    // This data is for PropertyDates
                    // $property_date = [
                    //     'property_id' => $booking->property_id,
                    //     'booking_id' => $booking->id,
                    //     'date'        => $days[$i],
                    //     'status'      => 'Not available',
                    //     'type'         => "calendar",
                    // ];

                    $property_date = [
                        'property_id' => $booking->property_id,
                        'date' => $days[$i],
                        'status' => 'Not available',
                        'type' => "calendar",
                    ];

                    // PropertyDates::updateOrCreate(['property_id' => $booking->property_id, 'date' => $days[$i]], $property_date);
                    CustomPricing::updateOrCreate(['property_id' => $booking->property_id, 'date' => $days[$i]], $property_date);
                }
            } else {
                $days = $this->helper->get_days($booking->start_date, $booking->end_date);

                for ($i = 0; $i < count($days) - 1; $i++) {
                    // This data is for PropertyDates
                    // $property_date = [
                    //     'property_id' => $booking->property_id,
                    //     'booking_id' => $booking->id,
                    //     'date'        => $days[$i],
                    //     'status'  => 'Available',
                    //     'type'         => "calendar",
                    // ];
                    $property_date = [
                        'property_id' => $booking->property_id,
                        'date' => $days[$i],
                        'status' => 'Available',
                        'type' => "calendar",
                    ];

                    CustomPricing::updateOrCreate(['property_id' => $booking->property_id, 'date' => $days[$i]], $property_date);
                    // PropertyDates::updateOrCreate(['property_id' => $booking->property_id, 'date' => $days[$i]], $property_date);
                }
            }

            if (!empty($request->message)) {
                $messages = new Messages;
                $messages->property_id = $booking->property_id;
                $messages->booking_id = $booking->id;
                $messages->receiver_id = $booking->user_id;
                $messages->sender_id = Auth::user()->id;
                $messages->message = $request->message;
                $messages->type_id = 6;
                $messages->save();
            }

            $status = 'Declined';
            $email->bookingAcceptedOrDeclined($request->id, $status);
            (User::find($booking->user_id))->notify(new UserNotify(
                'booking.request.declined',
                url('/guest/reservation/'.$booking->code.'?booking=cancelled'),
                [':property' => $booking->properties->name],
                ['slug' => 'reservation', 'tab' => '3', 'booking_id' => $booking->code]
            ));
            return response()->json(['message' => 'Success', 'data' => $booking], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function expire(Request $request)
    {

        $booking = Bookings::find($request->id);

        $fees = PropertyFees::pluck('value', 'field');

        $host_penalty = $fees['host_penalty'];
        $currency = $fees['currency'];
        $more_then_seven = $fees['more_then_seven'];
        $less_then_seven = $fees['less_then_seven'];
        $cancel_limit = $fees['cancel_limit'];

        $code = DB::table('currency')->where('default', 1)->first()->code;

        if ($host_penalty != 0) {
            $penalty = new Penalty;
            $penalty->property_id = $booking->property_id;
            $penalty->user_id = $booking->user_id;
            $penalty->booking_id = $request->id;
            $penalty->currency_code = $booking->currency_code;
            $penalty->amount = $this->helper->convert_currency($penalty_currency, $code, $penalty_before_days);
            $penalty->remain_amount = $penalty->amount;
            $penalty->status = "Pending";
            $penalty->save();
        }

        $to_time = strtotime($booking->created_at);
        $from_time = strtotime(date('Y-m-d H:i:s'));
        $diff_mins = round(abs($to_time - $from_time) / 60, 2);

        if ($diff_mins >= 1440) {
            $booking->status = 'Expired';
            $booking->expired_at = date('Y-m-d H:i:s');
            $booking->save();

            BookingStatusLog::updateOrCreate(
                ['booking_id' => $booking->id],
                ['status' => $booking->status, 'changed_by' => '']
            );

            $days = $this->helper->get_days($booking->start_date, $booking->end_date);
            for ($j = 0; $j < count($days) - 1; $j++) {
                PropertyDates::where('property_id', $booking->property_id)->where('date', $days[$j])->where('status', 'Not available')->delete();
            }

            $payouts = new Payouts;
            $payouts->booking_id = $request->id;
            $payouts->property_id = $booking->property_id;
            $payouts->user_id = $booking->user_id;
            $payouts->user_type = 'guest';
            $payouts->amount = $booking->original_guest_payout;
            $payouts->penalty_amount = 0;
            $payouts->currency_code = $booking->currency_code;
            $payouts->status = 'Future';
            $payouts->save();

            $messages = new Messages;
            $messages->property_id = $booking->property_id;
            $messages->booking_id = $booking->id;
            $messages->receiver_id = $booking->user_id;
            $messages->sender_id = Auth::user()->id;
            $messages->message = '';
            $messages->type_id = 7;
            $messages->save();

            $this->helper->one_time_message('success', trans('messages.success.booking_expire_success'));
            clearCache('.calc.property_price');
            return response()->json(["message" => "Success", "url" => "booking/" . $request->id]);
            // return redirect('booking/'.$request->id);
        } else {
            $this->helper->one_time_message('error', trans('messages.error.booking_expire_error'));
            return response()->json(["message" => "Error", "url" => "booking/" . $request->id]);
            // return redirect('booking/'.$request->id);
        }
    }

    public function makePaymentForHyperpay(Request $request)
    {
        Log::debug('makePaymentForHyperpay');

        try {

            $validator = Validator::make($request->all(), [
                'bookingId' => 'required',
                'paymentMethodId' => 'required',

            ]);


            if ($validator->fails()) {
                return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
            }

            $propertyFees = PropertyFees::pluck('value', 'field');
            $guest_service_charge = $propertyFees['guest_service_charge'];


            $data['booking_id'] = $bookingId = $request->bookingId;

            $booking = Bookings::with('host')->find($bookingId);


            $rand_number = $this->helper->randomCode(3);

            $data['total'] = $booking->total;
            $data['booking_type'] = $booking->booking_type;
            $data['transaction_id'] = $booking->transaction_id;
            $data['customer_ref'] = $customer_ref = $rand_number . "-B-" . $bookingId;
            $data['tax'] = $booking->iva_tax;

            $data['is_elm_document'] = ElmDocument::where('user_id', $booking->user_id)
                ->first();
            // ->where('verified_till', '>=', Carbon::now()->format('Y-m-d'))
            if (isset($data['is_elm_document'])) {
                $data['is_yaqeen_verified'] = "1";
            } else {
                $data['is_yaqeen_verified'] = "0";
            }


            // $finalamount = $data['total']  = $booking->total;
            $finalamount = $data['total'] = $booking->total_with_discount ? $booking->total_with_discount : $booking->total;
            $couponResponse = null;

            if ($request->coupon) {
                $couponResponse = $this->helper->applyCoupon($request->coupon, $booking->property_id, $booking->base_price - $booking->host_discount_amount ?? 0, Auth::user()->id, $request);

                Log::debug('couponResponse', [$couponResponse]);

                $service_fee_on_base_price = $couponResponse->getData()->servicefee;
                $service_fee_on_cleaning = $booking->cleaning_charge * $guest_service_charge / 100;
                $service_fee_on_security_money = $booking->security_money * $guest_service_charge / 100;


                if ($booking->properties->platform_id == 4) {
                    $cleaning_fee_ser = ($booking->cleaning_charge / 100) * 11;
                    $allothers = $booking->iva_tax + $booking->cleaning_charge + $cleaning_fee_ser;
                } else {

                    $allothers = $booking->iva_tax + $service_fee_on_cleaning + $service_fee_on_security_money + $booking->cleaning_charge + $booking->additional_guest + $booking->accomodation_tax + $booking->security_money;
                }
                Log::debug('allothers', [$allothers]);


                $finalamount = $data['total'] = $couponResponse->getData()->amount_after_discount + $allothers;
                // $finalamount = $data['total'] = $booking->total_with_discount;
                Log::debug('finalamount', [$finalamount]);

                // return $finalamount;


                if ($couponResponse->getData()->status == 'success' || $couponResponse->getData()->status == 'campaign') {
                    $promoCode = PromoCode::where(['code' => $request->coupon])->first();
                    $codeUsage = PromoCodeUsage::firstOrNew(['promo_code_id' => $promoCode->id, 'user_id' => Auth::user()->id, 'property_id' => $booking->property_id, 'booking_id' => $bookingId]);

                    $codeUsage->fill([
                        'discount_type' => $couponResponse->getData()->discount_type,
                        'discount_value' => $couponResponse->getData()->discount_value,
                        'original_amount' => $booking->total,
                        'after_discount' => $finalamount,
                        'booking_id' => $bookingId,
                    ]);

                    $codeUsage->save();

                    $booking->total_with_discount = $data['total'] = $finalamount;
                    $booking->total_discount = $couponResponse->getData()->discount_value;
                    $booking->base_price_with_discount = $couponResponse->getData()->amount_after_discount - $couponResponse->getData()->servicefee;
                    if ($booking->properties->platform_id == 4) {

                        $booking->service_charge = $service_fee_on_security_money + $booking->additional_guest + $booking->accomodation_tax + $service_fee_on_base_price + $cleaning_fee_ser;
                    } else {

                        $booking->service_charge = $service_fee_on_cleaning + $service_fee_on_security_money + $booking->additional_guest + $booking->accomodation_tax + $service_fee_on_base_price;
                    }
                    $booking->save();
                } else {
                    return response()->json(['error' => true, 'error_message' => $couponResponse->getData()->message], 200);
                }
            }

            if ($finalamount <= 0) {
                return response()->json(['message' => 'failure', 'error' => "We apologize for any inconvenience, Contact to Admin for further assistance. Booking Id: " . $bookingId], 422);
            }

            if ($request->paymentMethodId == PaymentTypeEnum::VISAMASTER || $request->paymentMethodId == PaymentTypeEnum::MADA || $request->paymentMethodId == PaymentTypeEnum::STCPay) {

                if (app('PAYMENT_METHOD') != Bookings::PAY_BY_HYPERPAY) {

                    $exist_card = UserCards::where(['is_default' => 1, 'user_id' => auth()->user()->id, 'paymentMethodId' => $request->paymentMethodId])->first();

                    if (!$exist_card) {

                        if (!request()->exists('number')) {

                            return response()->json(['message' => 'failure', 'error' => 'Please supply card details also'], 422);
                        }


                        $validator = Validator::make($request->all(), [
                            'name' => 'required',
                            'number' => 'required|min:16,max:16',
                            'month' => 'required|min:2,max:2',
                            'year' => 'required|min:2,max:2',
                            'cvv' => 'required|min:3,max:3',

                        ]);

                        if ($validator->fails()) {
                            return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                        }


                        $exist_supplied_card = UserCards::where([
                            'card_number' => $request->number,
                            'is_verified' => 0,
                            'user_id' => auth()->user()->id
                        ])
                            ->first();

                        if ($exist_supplied_card) {
                            $exist_supplied_card->forceDelete();
                        }


                        $usercard = new UserCards;
                        $usercard->user_id = auth()->user()->id;
                        $usercard->card_number = $request->number;
                        $usercard->card_name = $request->name;
                        $usercard->card_month = $request->month;
                        $usercard->card_year = $request->year;
                        $usercard->card_cvv = $request->cvv;
                        $usercard->save();
                        $exist_card = $usercard;
                    }
                }

                try {
                    // -------------------------------- WALLET INTEGRATION --------------------------------------------
                    $walletData = [];
                    if ($request->pay_by_wallet) {
                        $walletData = $this->helper->payByWallet($finalamount, $booking);
                        $data['total'] = $walletData['finalamount'];
                        $finalamount = $walletData['finalamount'];
                    }


                    if ($request->pay_by_wallet && $finalamount == 0) {
                        $data['paymentURL'] = url('/') . '/payment/callback/' . Bookings::PAYMENT_STATUS_WALLET . '?booking=' . $data['booking_id'];
                        $data['fullPaymentByWallet'] = true;
                        return response()->json(['message' => 'Success', "data" => $data], 200);
                    }

                    // -------------------------------- WALLET INTEGRATION --------------------------------------------
                    if (app('PAYMENT_METHOD') == Bookings::PAY_BY_MOYASAR) {
                        $data['paymentURL'] = (new MoyasarService())->payment('credit_card', $finalamount, route('payment.initiated'), $exist_card, ['payer' => auth()->user(), 'payee' => $booking->host, 'service' => $booking, 'action' => 1, 'data' => ['booking' => $booking->toArray()]], ['walletData' => $walletData]);
                    } elseif (app('PAYMENT_METHOD') == Bookings::PAY_BY_HYPERPAY) {


                        $shopperResultUrl = route('payment.hyperinitiated');
                        // $hyperpayService = new HyperpayService();
                        // $response = $hyperpayService->makePayment(($finalamount), 'SAR', $request->paymentMethodId, $exist_card, $shopperResultUrl, ['service' => $booking, 'action' => 1], ['booking' => $booking->id, 'walletData' => $walletData]);
                        // if ($response['status'] == 'success' && isset($response['redirect'])) {
                        //     $redirectUrl = $response['redirect'];
                        //     $redirectParams = $response['parameters'];
                        //     // Build the query string from the parameters
                        //     $queryParams = [];
                        //     foreach ($redirectParams as $param) {
                        //         if (isset($param['name']) && isset($param['value'])) {
                        //             $queryParams[$param['name']] = $param['value'];
                        //         }
                        //     }
                        //     $queryString = http_build_query($queryParams);
                        //     // Redirect to the constructed URL
                        //     $data['paymentURL'] = $redirectUrl . '?' . $queryString;
                        // }

                        $hyperdata = (new HyperpaySdkService())->checkout($request->paymentMethodId, $finalamount, $booking, $shopperResultUrl, ['booking' => $booking->id, 'walletData' => $walletData]);
                        //    $url = route('ProcessPayment', ['data' => $data]);
                        $data['paymentURL'] = route('MobileProcessPayment', [
                            'url' => $hyperdata['url'],
                            'checkoutId' => $hyperdata['checkoutId'],
                            'type' => $hyperdata['type'],
                        ]);
                    } else {
                        $data['paymentURL'] = (new FatoorahService())->execute($finalamount, $exist_card, $customer_ref, $booking->toArray());
                    }
                } catch (Exception $e) {
                    if ($e instanceof ValidationException) {
                        return response()->json(['error' => $e->errors(), 'status' => false], 422);
                    }
                }

                $hosts = User::select('users.*')->join('properties AS ps', 'ps.host_id', 'users.id')
                    ->leftJoin('properties_cohosts AS pcs', fn($q) => $q->on('pcs.co_host_id', 'users.id')->whereNull('pcs.deleted_at'))
                    ->where('ps.id', $booking->property_id)->distinct()->get();
                Notification::send($hosts, new UserNotify(
                    'guest.booking.payment.click.host',
                    route('managehost.all_reservation'),
                    data: ['slug' => 'booking', 'tab' => 'upcoming-bookings']
                ));
                return response()->json(['message' => 'Success', 'data' => $data], 200);
            } elseif ($request->paymentMethodId == PaymentTypeEnum::ApplePay) {
                $hosts = User::select('users.*')->join('properties AS ps', 'ps.host_id', 'users.id')
                    ->leftJoin('properties_cohosts AS pcs', fn($q) => $q->on('pcs.co_host_id', 'users.id')->whereNull('pcs.deleted_at'))
                    ->where('ps.id', $booking->property_id)->distinct()->get();
                Notification::send($hosts, new UserNotify(
                    'guest.booking.payment.click.host',
                    route('managehost.all_reservation'),
                    data: ['slug' => 'booking', 'tab' => 'upcoming-bookings']
                ));

                // -------------------------------- WALLET INTEGRATION --------------------------------------------
                $walletData = [];
                if ($request->pay_by_wallet) {
                    $walletData = $this->helper->payByWallet($finalamount, $booking);
                    $data['total'] = $walletData['finalamount'];
                    $finalamount = $walletData['finalamount'];
                }

                if ($request->pay_by_wallet && $finalamount == 0) {
                    $data['paymentURL'] = url('/') . '/payment/callback/' . Bookings::PAYMENT_STATUS_WALLET . '?booking=' . $data['booking_id'];
                    $data['fullPaymentByWallet'] = true;
                    return response()->json(['message' => 'Success', "data" => $data], 200);
                }

                $data['finalamount'] = $finalamount;
                // -------------------------------- WALLET INTEGRATION --------------------------------------------

                if ($request->has('api_version')) {
                    // Initialize the parameters array with booking_id
                    //     $params = ['booking_id' => $bookingId];

                    //     // Check if pay_by_wallet exists in the request
                    //     if ($request->pay_by_wallet) {
                    //         // Encode walletData as JSON and add it to the parameters array
                    //         $params['walletData'] = json_encode($walletData);
                    //     }
                    // $data['payment_service'] = route('payment.mobilesuccess', $params);


                    $shopperResultUrl = route('payment.hyperinitiated');
                    $hyperdata = (new HyperpaySdkService())->checkout($request->paymentMethodId, $finalamount, $booking, $shopperResultUrl, ['booking' => $booking->id, 'walletData' => $walletData]);
                    //    $url = route('ProcessPayment', ['data' => $data]);
                    $data['payment_service'] = route('MobileProcessPayment', [
                        'url' => $hyperdata['url'],
                        'checkoutId' => $hyperdata['checkoutId'],
                        'type' => $hyperdata['type'],
                    ]);
                } else {
                    $data['payment_service'] = app('PAYMENT_METHOD') == Bookings::PAY_BY_MOYASAR ? Bookings::PAY_BY_MOYASAR : Bookings::PAY_BY_FATOORAH;
                }
                return response()->json(['message' => 'Success', "data" => $data], 200);
            } else {

                return response()->json(['message' => 'failure', 'error' => 'Something went wrong with payment method'], 500);
            }
        } catch (\Throwable $th) {
            return response(['message' => $th->getMessage(), 'error' => $th->getTrace()], 500);
        }
    }

    public function cancel(Request $request, $id)
    {
        try {
            $booking = Bookings::find($id);
            if (!$booking) {
                return response(['message' => 'failure', 'error' => "No booking found"], 404);
            }


            $now = new DateTime();
            $booking_start = new DateTime($booking->start_date);
            $interval_diff = $now->diff($booking_start);
            $interval = $interval_diff->days;
            $refund_summary = $this->helper->GetRefundAmount($booking, $interval);
            // dd(isset($refund_summary['status']));
            if (isset($refund_summary['status'])) {
                $this->helper->refundAmount($booking, $refund_summary['amount_to_refund']);

                $booking->cancelled_by = "Guest";
                $booking->cancelled_at = date('Y-m-d H:i:s');
                $booking->status = "Cancelled";
                $booking->save();

                $this->helper->cancelPolicyTawuniya($booking);

                $this->helper->staahBooking($booking->id);

                $days = $this->helper->get_days($booking->start_date, $booking->end_date);

                for ($j = 0; $j < count($days) - 1; $j++) {
                    PropertyDates::where('property_id', $booking->property_id)->where('date', $days[$j])->where('status', 'Not available')->delete();
                }

                BookingStatusLog::updateOrCreate(
                    ['booking_id' => $booking->id],
                    ['status' => $booking->status, 'changed_by' => Auth::id()]
                );

                $hosts = User::select('users.*')->join('properties AS ps', 'ps.host_id', 'users.id')
                    ->leftJoin('properties_cohosts AS pcs', fn($q) => $q->on('pcs.co_host_id', 'users.id')->whereNull('pcs.deleted_at'))
                    ->where('ps.id', $booking->property_id)->distinct()->get();
                Notification::send($hosts, new UserNotify(
                    'booking.guest.cancelled',
                    route('managehost.all_reservation'),
                    [':property' => $booking->properties->name],
                    ['slug' => 'booking', 'tab' => 'cancelled-bookings']
                ));
            } else {
                return response()->json(["message" => "failure", "error" => "Something went wrong", "data" => null], 200);
            }

            return response()->json(["message" => "Success", "data" => null], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    function CancellationPolicy(Request $request)
    {
        try {
            $User = User::find(Auth::id());
            $User->cancel_policy = $request->cancel_policy;
            $User->save();
            return response()->json(["message" => "Success"], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    function Store_GuestRequirement(Request $request)
    {
        try {
            $User = User::find(Auth::id());
            $User->guest_req = ($request->guest_req == null ? "No" : "Yes");
            $User->save();
            return response()->json(["message" => "Success"], 200);
        } catch (\Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function bookingDetail(Request $request)
    {
        try {
            $booking = Bookings::with(['properties', 'users', 'paymentType'])->where('code', $request->code)->first();

            if ($booking) {
                $booking['chat_head_id'] = $this->helper->createOrUpdateChatHead($booking->user_id, $booking->host_id, $booking->property_id);
                $booking['property_status'] = true;
                $priceStatus = $this->helper->getPrice($booking->property_id, $booking->start_date, $booking->end_date, $booking->guest,true,$booking->id);
                $decodedPriceStatus = json_decode($priceStatus, true);
                if (!empty($decodedPriceStatus['status']) && $decodedPriceStatus['status'] == "Not available") {
                    $booking['property_status'] = false;
                }
                return new GuestSingleBookingDetailResource($booking, $decodedPriceStatus['yousaved_without_symbol'] ?? null);
            } else {
                return apiResponse("Booking Does Not Exist", "Failure", 422);
            }
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function chatBookingDetail(Request $request)
    {
        try {
            $booking = Bookings::with(['properties', 'users', 'paymentType'])->where('id', $request->code)->orWhere('code', $request->code)->first();
            if ($booking) {
                $booking['chat_head_id'] = $this->helper->createOrUpdateChatHead($booking->user_id, $booking->host_id, $booking->property_id);

                $reservationData = new HostReservationResource($booking);
                return apiResponse($reservationData, 'Success', 200);
            } else {
                return apiResponse("Booking Does Not Exist", "Failure", 422);
            }
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }


    public function makePayment(Request $request)
    {
        Log::debug('makePayment');

        try {

            $validator = Validator::make($request->all(), [
                'bookingId' => 'required',
                'paymentMethodId' => 'required',

            ]);


            if ($validator->fails()) {
                return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
            }

            $propertyFees = PropertyFees::pluck('value', 'field');
            $guest_service_charge = $propertyFees['guest_service_charge'];


            $data['booking_id'] = $bookingId = $request->bookingId;

            $booking = Bookings::with('host')->find($bookingId);


            $rand_number = $this->helper->randomCode(3);

            $data['total'] = $booking->total;
            $data['booking_type'] = $booking->booking_type;
            $data['customer_ref'] = $customer_ref = $rand_number . "-B-" . $bookingId;
            $data['tax'] = $booking->iva_tax;

            $data['is_elm_document'] = ElmDocument::where('user_id', $booking->user_id)
                ->first();
            // ->where('verified_till', '>=', Carbon::now()->format('Y-m-d'))
            if (isset($data['is_elm_document'])) {
                $data['is_yaqeen_verified'] = "1";
            } else {
                $data['is_yaqeen_verified'] = "0";
            }


            // $finalamount = $data['total']  = $booking->total;
            $finalamount = $data['total'] = $booking->total_with_discount ? $booking->total_with_discount : $booking->total;
            $couponResponse = null;

            if ($request->coupon) {
                $couponResponse = $this->helper->applyCoupon($request->coupon, $booking->property_id, $booking->base_price - $booking->host_discount_amount ?? 0, Auth::user()->id, $request);

                Log::debug('couponResponse', [$couponResponse]);


                $service_fee_on_base_price = $couponResponse->getData()->servicefee;
                $service_fee_on_cleaning = $booking->cleaning_charge * $guest_service_charge / 100;
                $service_fee_on_security_money = $booking->security_money * $guest_service_charge / 100;

                $allothers = $service_fee_on_cleaning + $service_fee_on_security_money + $booking->cleaning_charge + $booking->additional_guest + $booking->accomodation_tax + $booking->security_money;
                Log::debug('couponResponse', [$allothers]);

                $finalamount = $data['total'] = $couponResponse->getData()->amount_after_discount + $allothers;

                if ($couponResponse->getData()->status == 'success' || $couponResponse->getData()->status == 'campaign') {
                    $promoCode = PromoCode::where(['code' => $request->coupon])->first();
                    $codeUsage = PromoCodeUsage::firstOrNew(['promo_code_id' => $promoCode->id, 'user_id' => Auth::user()->id, 'property_id' => $booking->property_id, 'booking_id' => $bookingId]);

                    $codeUsage->fill([
                        'discount_type' => $couponResponse->getData()->discount_type,
                        'discount_value' => $couponResponse->getData()->discount_value,
                        'original_amount' => $booking->total,
                        'after_discount' => $finalamount,
                        'booking_id' => $bookingId,
                    ]);

                    $codeUsage->save();

                    $booking->total_with_discount = $data['total'] = $finalamount;
                    $booking->total_discount = $couponResponse->getData()->discount_value;
                    $booking->base_price_with_discount = $couponResponse->getData()->amount_after_discount - $couponResponse->getData()->servicefee;
                    $booking->service_charge = $service_fee_on_cleaning + $service_fee_on_security_money + $booking->additional_guest + $booking->accomodation_tax + $service_fee_on_base_price;
                    $booking->save();
                } else {
                    return response()->json(['error' => true, 'error_message' => $couponResponse->getData()->message], 200);
                }
            }

            if ($finalamount <= 0) {
                return response()->json(['message' => 'failure', 'error' => "We apologize for any inconvenience, Contact to Admin for further assistance. Booking Id: " . $bookingId], 422);
            }

            if ($request->paymentMethodId == PaymentTypeEnum::VISAMASTER) {

                $exist_card = UserCards::where(['is_default' => 1, 'user_id' => auth()->user()->id])->first();
                if (!$exist_card) {


                    if (!request()->exists('number')) {

                        return response()->json(['message' => 'failure', 'error' => 'Please supply card details also'], 422);
                    }


                    $validator = Validator::make($request->all(), [
                        'name' => 'required',
                        'number' => 'required|min:16,max:16',
                        'month' => 'required|min:2,max:2',
                        'year' => 'required|min:2,max:2',
                        'cvv' => 'required|min:3,max:3',

                    ]);

                    if ($validator->fails()) {
                        return response()->json(['message' => 'failure', 'error' => $validator->errors()], 422);
                    }


                    $exist_supplied_card = UserCards::where([
                        'card_number' => $request->number,
                        'is_verified' => 0,
                        'user_id' => auth()->user()->id
                    ])
                        ->first();

                    if ($exist_supplied_card) {
                        $exist_supplied_card->forceDelete();
                    }


                    $usercard = new UserCards;
                    $usercard->user_id = auth()->user()->id;
                    $usercard->card_number = $request->number;
                    $usercard->card_name = $request->name;
                    $usercard->card_month = $request->month;
                    $usercard->card_year = $request->year;
                    $usercard->card_cvv = $request->cvv;
                    $usercard->save();
                    $exist_card = $usercard;
                }
                try {
                    // -------------------------------- WALLET INTEGRATION --------------------------------------------
                    $walletData = [];
                    if ($request->pay_by_wallet) {
                        $walletData = $this->helper->payByWallet($finalamount, $booking);
                        $data['total'] = $walletData['finalamount'];
                        $finalamount = $walletData['finalamount'];
                    }

                    if ($request->pay_by_wallet && $finalamount == 0) {
                        $data['paymentURL'] = url('/') . '/payment/callback/' . Bookings::PAYMENT_STATUS_WALLET . '?booking=' . $data['booking_id'];
                        $data['fullPaymentByWallet'] = true;
                        return response()->json(['message' => 'Success', "data" => $data], 200);
                    }
                    // -------------------------------- WALLET INTEGRATION --------------------------------------------
                    $data['paymentURL'] = (new MoyasarService())->payment('credit_card', $finalamount, route('payment.initiated'), $exist_card, ['payer' => auth()->user(), 'payee' => $booking->host, 'service' => $booking, 'action' => 1, 'data' => ['booking' => $booking->toArray()]], ['walletData' => $walletData]);
                    // if (1 == 1) {
                    // } else {
                    //     $data['paymentURL'] = (new FatoorahService())->execute($finalamount, $exist_card, $customer_ref, $booking->toArray());
                    // }
                } catch (Exception $e) {
                    if ($e instanceof ValidationException) {
                        return response()->json(['error' => $e->errors(), 'status' => false], 422);
                    }
                }

                $hosts = User::select('users.*')->join('properties AS ps', 'ps.host_id', 'users.id')
                    ->leftJoin('properties_cohosts AS pcs', fn($q) => $q->on('pcs.co_host_id', 'users.id')->whereNull('pcs.deleted_at'))
                    ->where('ps.id', $booking->property_id)->distinct()->get();
                Notification::send($hosts, new UserNotify(
                    'guest.booking.payment.click.host',
                    route('managehost.all_reservation'),
                    data: ['slug' => 'booking', 'tab' => 'upcoming-bookings']
                ));
                return response()->json(['message' => 'Success', 'data' => $data], 200);
            } elseif ($request->paymentMethodId == PaymentTypeEnum::ApplePay || $request->paymentMethodId == PaymentTypeEnum::STCPay) {
                $hosts = User::select('users.*')->join('properties AS ps', 'ps.host_id', 'users.id')
                    ->leftJoin('properties_cohosts AS pcs', fn($q) => $q->on('pcs.co_host_id', 'users.id')->whereNull('pcs.deleted_at'))
                    ->where('ps.id', $booking->property_id)->distinct()->get();
                Notification::send($hosts, new UserNotify(
                    'guest.booking.payment.click.host',
                    route('managehost.all_reservation'),
                    data: ['slug' => 'booking', 'tab' => 'upcoming-bookings']
                ));

                // -------------------------------- WALLET INTEGRATION --------------------------------------------
                $walletData = [];
                if ($request->pay_by_wallet) {
                    $walletData = $this->helper->payByWallet($finalamount, $booking);
                    $data['total'] = $walletData['finalamount'];
                }

                if ($request->pay_by_wallet && $finalamount == 0) {
                    $data['paymentURL'] = url('/') . '/payment/callback/' . Bookings::PAYMENT_STATUS_WALLET . '?booking=' . $data['booking_id'];
                    $data['fullPaymentByWallet'] = true;
                    return response()->json(['message' => 'Success', "data" => $data], 200);
                }

                $data['finalamount'] = $finalamount;
                // -------------------------------- WALLET INTEGRATION --------------------------------------------

                if ($request->has('api_version')) {
                    // Initialize the parameters array with booking_id
                    $params = ['booking_id' => $bookingId];

                    // Check if pay_by_wallet exists in the request
                    if ($request->pay_by_wallet) {
                        // Encode walletData as JSON and add it to the parameters array
                        $params['walletData'] = json_encode($walletData);
                    }
                    $data['payment_service'] = route('payment.mobilesuccess', $params);
                } else {
                    $data['payment_service'] = app('PAYMENT_METHOD') == Bookings::PAY_BY_MOYASAR ? Bookings::PAY_BY_MOYASAR : Bookings::PAY_BY_FATOORAH;
                }
                return response()->json(['message' => 'Success', "data" => $data], 200);
            } else {

                return response()->json(['message' => 'failure', 'error' => 'Something went wrong with payment method'], 500);
            }
        } catch (\Throwable $th) {
            return response(['message' => $th->getMessage(), 'error' => $th->getTrace()], 500);
        }
    }
}
