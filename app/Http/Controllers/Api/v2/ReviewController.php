<?php

namespace App\Http\Controllers\Api\v2;

use App\Enums\PropertyChatTypeEnum;
use App\Helpers\ImageHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\ReviewPaginatedResource;
use App\Models\Bookings;
use App\Models\Image;
use App\Models\PropertyChat;
use App\Models\PropertyChatHead;
use App\Models\Reviews;
use App\Models\User;
use App\Notifications\UserNotify;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use App\Http\Helpers\Common;
use Illuminate\Support\Facades\Log;
use Intervention\Image\ImageManagerStatic as InterImage;
use Modules\Reviews\Services\ReviewsService;

class ReviewController extends Controller
{
    private ReviewsService $_reviews_srv;
    private $helper;

    public function __construct(ReviewsService $reviewsService)
    {
        $this->helper = new Common;
        $this->_reviews_srv = $reviewsService;
    }

    function list(Request $request, $type, $property_id = null)
    {
        $isMob = $request->is('api/*');
        $page = $request->page ?? 5;
        // $reviews = Reviews::select('bookings.*')->reviewsList($type, $property_id)
        $reviews = Reviews::reviewsList($type, $property_id)
            ->when($property_id && auth()->id(), fn($q) => $q->where('receiver_id', auth()->id()))
            ->when(!$property_id, fn($q) => $q->orderByDesc('ps.id'))->orderByDesc('reviews.id')->cursorPaginate($page);
        $data = [
            'reviews' => new ReviewPaginatedResource($reviews),
            'status' => true
        ];
        if (!!$request->avgs) {
            $data['avgs'] = Reviews::avgs($type, ($property_id ?? auth()->id()));
        }
        if ($isMob) {
            return apiResponse($data, 'Success', 200);
        }
        return response()->json($data);
    }

    function create(Request $request, $booking_id)
    {
        $isMob = $request->is('api/*');
        $booking = Bookings::select('bookings.*')->with(['users', 'review' => fn($q) => $q->where('sender_id', auth()->id())])->where('bookings.host_id', auth()->id())
            ->where('bookings.status', 'Accepted')->findOrFail($booking_id);
        $cleanliness_messages = ['property damage', 'Ignore the check out directions', 'Bed linen is damaged', 'Messy kitchen'];
        $communication_messages = ['Stayed after check out time', 'Arrive early', 'Pets not approved', 'Unapproved guests'];
        if (!$booking->review) {
            $step = 1;
        } else {
            if (!!$booking->review->cleanliness_message) {
                $booking->review['cleanliness_message'] = json_decode($booking->review->cleanliness_message, true);
            }
            if (!!$booking->review->communication_message) {
                $booking->review['communication_message'] = json_decode($booking->review->communication_message, true);
            }
            $step = $booking->review->secret_feedback ?? 1;
            if ($step == 5) {
                abort(404);
            }
        }
        if ($isMob) {
            return apiResponse(['booking' => $booking, 'step' => $step], 'Success', 200);
        }
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.create_review', compact('booking', 'step', 'communication_messages', 'cleanliness_messages'));
        } else {
            return view('managehost.create_review', compact('booking', 'step', 'communication_messages', 'cleanliness_messages'));
        }
    }

    function store(Request $request, $type, $booking_id)
    {
        try {
            $step = !!$request->step ? (in_array($request->step, [1, 2, 3, 4, 5]) ? $request->step : 4) : 1;
            $isMob = $request->is('api/*');
            $isGuest = $type == 'guest';
            $by = $isGuest ? 'user_id' : 'host_id';
            $to = $isGuest ? 'host_id' : 'user_id';
            $other = $isGuest ? 'host' : 'guest';
            $booking = Bookings::with('reviews')->select('bookings.*')->where($by, auth()->id())->where('status', 'Accepted')
                ->findOrFail($booking_id);
            $review = $booking->reviews->firstWhere('reviewer', $type);
            $otherReview = $booking->reviews->firstWhere('reviewer', $other);
            $rules = ($isGuest) ? $this->_reviews_srv->getGuestStoreValidationRules()
                : $this->_reviews_srv->getHostStoreValidationRules($request, $step);

            $validated_data = $request->validate($rules);
            $validated_data = array_merge($validated_data, [
                'is_guest' => $isGuest,
                'sender_id' => auth()->id(),
                'receiver_id' => $booking[$to],
                'booking_id' => $booking->id,
                'property_id' => $booking->property_id,
                'ispublic' => $request->ispublic ?? !$isGuest,
                'reviewer' => $type,
                'secret_feedback' => (!$isGuest) ? $step : null,
            ]);

            $review = $this->_reviews_srv->addOrUpdateReview($validated_data, $review);

            $images = null;
            if ($isGuest && isset($validated['images']) && count($validated['images']) > 0) {
                $images = array_pop($validated);
            }

            if (!!$otherReview) {
                if (!$isGuest) {
                    $otherReview->update(['ispublic' => true]);
                } else {
                    $review->update(['ispublic' => true]);
                }
            }

            // Track review events across all platforms
            if ($isGuest) {
                // Track review_published event when guest reviews host/property
                \App\Jobs\EventTrackingJob::trackReviewPublished(
                    $booking->id,
                    auth()->id(),
                    $booking->host_id,
                    $review->rating,
                    $review->message
                );
            } else {
                // Track host_guest_review_published event when host reviews guest
                \App\Jobs\EventTrackingJob::trackHostGuestReviewPublished(
                    auth()->id(),
                    $booking->user_id,
                    $review->rating,
                    $review->message
                );
            }
            if ($isGuest && !!$images) {
                $data = [];
                foreach ($images as $image) {

                    $save_path ='images/reviews/';
                    $large = InterImage::make($image)->encode('webp');
                    $largeUrl=ImageHelper::upload($large, $save_path , md5('large' . time()) . '.webp');

                    $small = InterImage::make($image)->fit(300)->encode('webp');
                    $smallUrl=ImageHelper::upload($small, $save_path , md5('small' . time()) . '.webp');


                    $data[] = [
                        'small' => $smallUrl,
                        'large' => $largeUrl,
                        'imageable_id' => $review->id,
                        'imageable_type' => get_class(new Reviews()),
                        'created_at' => now(),
                        'updated_at' => now()
                    ];
                    Image::insert($data);
                }
            } else if (!$isGuest && !!$request->private_message) {
                // Do it using service
                $now = now();
                $chat_head = PropertyChatHead::firstOrCreate(['property_id' => $booking->property_id, 'guest_id' => $booking->user_id, 'host_id' => auth()->id()]);
                $msg = mb_convert_encoding($validated_data['private_message'], 'UTF-8', 'UTF-8');
                PropertyChat::create([
                    'message' => $msg,
                    'sender_id' => auth()->id(),
                    'type' => PropertyChatTypeEnum::Text->value,
                    'property_chat_head_id' => $chat_head->id,
                    'created_at' => $now,
                    'updated_at' => $now
                ]);
                (User::find($booking->user_id))->notify(new UserNotify(
                    'message.text.received',
                    route('properties.chat.view', ['type' => 'guest']),
                    [':user' => auth()->user()->first_name, ':msg' => (substr($msg, 0, 30) . (strlen($msg) > 30 ? '...' : ''))],
                    [
                        'slug' => 'guest/property_inbox',
                        'chat_head_id' => $chat_head->id
                    ]
                ));
            }
            $data = ['message' => 'Review ' . (is_bool($review) ? 'updated' : 'created') . ' successfully.', 'status' => true];
            // if (!is_bool($review)) {
            //     Log::debug('check', ["{$type}.review.booking.{$other}"]);
            //     (User::find($booking->{$to}))->notify(new UserNotify("{$type}.review.booking.{$other}", route('userreview'), data: [
            //         'slug' => "{$type}_review"
            //     ]));
            // }
            if ($isMob) {
                return apiResponse($data, 'Success', 200);
            }
            return response()->json($data);
        } catch (\Exception $e) {
            $arr = [
                'message' => 'Error',
                'status' => false
            ];
            $code = 500;
            if ($e instanceof ValidationException) {
                $code = 422;
                $arr['errors'] = $e->errors();
            } else if ($e instanceof ModelNotFoundException) {
                $code = 404;
                $arr['message'] = 'Booking not found.';
            } else {
                $arr['message'] = $e->getMessage();
                $arr['trace'] = $e->getTrace();
            }
            if ($isMob) {
                return apiResponse('Failure', $e->getMessage(), $code);
            }
            return response()->json($arr, $code);
        }
    }
}
