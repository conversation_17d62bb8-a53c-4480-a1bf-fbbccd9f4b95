<?php

namespace App\Http\Controllers;

use App\Http\Helpers\Common;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;

use Illuminate\Http\Request;

use App\Models\{
    Bookings,
    BookingDetails,
    Messages,
    Penalty,
    Payouts,
    Properties,
    PayoutPenalties,
    PropertyDates,
    PropertyFees,
    Settings,
    User,
    Refund,
    WishlistName,
    WishlistProperties
};
use Illuminate\Support\Facades\DB;

class WishlistController extends Controller
{
    private $helper;

    public function __construct()
    {
        $this->helper = new Common;
    }

    public function createWishlistName(Request $request)
    {
        $msg = "";
        $rules = array(
            'name' => 'required',
            'property_id' => 'required',
        );

        $fieldNames = array(
            'name' => 'Name',
            'property_id' => 'Property Id',
        );

        $validator = Validator::make($request->all(), $rules);
        $validator->setAttributeNames($fieldNames);
        if ($validator->fails()) {
            $msg = $validator;
            return response()->json($msg);
        } else {
            $baseUrl = url('/');
            $code = Str::random(8);
            $wishlistName = new WishlistName;
            $wishlistName->code = $code;
            $wishlistName->name = $request->name;
            $wishlistName->creater_id = Auth::guard('web')->user()->id;
            $wishlistName->share_link = $baseUrl . '/wishlists/' . $code;
            $wishlistName->collaborate_link = $baseUrl . '/collaborators/' . $code;
            $wishlistName->save();
            $wishlistProperty = new WishlistProperties;
            $wishlistProperty->wishlist_name_id = $wishlistName->id;
            $wishlistProperty->property_id = $request->property_id;
            $wishlistProperty->user_id = Auth::guard('web')->user()->id;
            $wishlistProperty->status = '1';
            $wishlistProperty->save();

            $msg = "Success";
            return response()->json($msg);
        }
    }

    public function updateWishlistName(Request $request)
    {

        $msg = "";
        $rules = array(
            'name' => 'required|max:50',
        );

        $fieldNames = array(
            'name' => 'Name',
        );

        $validator = Validator::make($request->all(), $rules);
        $validator->setAttributeNames($fieldNames);
        if ($validator->fails()) {
            $msg = $validator;
            return response()->json($msg);
        } else {
            $wishlistName = WishlistName::find($request->id);
            $wishlistName->name = $request->name;
            $wishlistName->save();

            return response()->json(["msg" => "Success"]);
        }
    }

    public function addRemoveWishlistProperty(Request $request)
    {
        $validate = Validator::make(
            $request->all(),
            [
                'wishlist_name_id' => 'required',
                'property_id' => 'required',
            ],

            [
                'wishlist_name_id.required' => 'wishlist name id is required',
                'property_id.required' => 'property id is required',
            ]
        );

        if ($validate->fails()) {
            return response()->json(['msg' => 'Failure']);
        }

        $wishlistProperty = WishlistProperties::where('wishlist_name_id', $request->wishlist_name_id)->where('property_id', $request->property_id)->first();

        if (isset($wishlistProperty)) {

            $wishlistProperty->delete();
            return response()->json(['msg' => 'Success', 'status' => $wishlistProperty->status]);
        } else {
            $property = Properties::with(['property_details', 'propertyType', 'property_price'])->find($request->property_id);
            $wishlistProperty = new WishlistProperties;
            $wishlistProperty->wishlist_name_id = $request->wishlist_name_id;
            $wishlistProperty->property_id = $request->property_id;
            $wishlistProperty->user_id = Auth::guard('web')->user()->id;
            $wishlistProperty->save();

            // Track add_to_wishlist event in MoEngage
            $property = Properties::with(['property_address', 'propertyType', 'property_price'])->find($request->property_id);
            if ($property) {
                \App\Jobs\EventTrackingJob::trackAddToWishlist(
                    Auth::guard('web')->user()->id,
                    $request->property_id,
                    $property->propertyType->name ?? '',
                    $property->property_address->city ?? '',
                    $property->property_price->price ?? '0'
                );
            }

            return response()->json(['msg' => 'Success', 'property' => $property]);
        }
    }

    public function toggleWishlist(Request $request)
    {
        // dd($request->all());
        $wishlist = WishlistProperties::where('property_id', $request->property_id)->where('user_id', $request->user_id)->where('status', '1')->first();
        if (isset($wishlist)) {
            $wishlist->delete();
            return response()->json(['msg' => "Success"]);
        }
        return response()->json(['msg' => "Not Found"]);
    }

    public function deleteWishlistByName(Request $request)
    {
        $wishlistProperties = WishlistProperties::where('wishlist_name_id', $request->id)->get();
        foreach ($wishlistProperties as $wishlistProperty) {
            $wishlistProperty->delete();
        }
        $wishlistName = WishlistName::find($request->id)->delete();
        return response()->json(['msg' => "Success"]);
    }

    public function getAllWishlistGroups(Request $request)
    {
        $data['title'] = customTrans('wishlist.wishlist');

        $data['wishlistGroups'] = WishlistName::with('wishlistProperties')
            ->where('creater_id', Auth::id())
            ->orWhere(DB::raw("FIND_IN_SET('" . Auth::id() . "', share_with)"), '>', 0)
            ->get()->filter(function ($wishlist) {
                return $wishlist->wishlistProperties && $wishlist->wishlistProperties->count();
            });

        return $this->helper->isRequestFromMobile($request)
            ? view('mobile.wishlist.new_wishlist', $data)
            : view('wishlist.new_wishlist', $data);
    }

    public function getAllWishlistProperties(Request $request)
    {
        $data['title'] = customTrans('wishlist.wishlist_listing');
        $data['wishlist'] = $wishlist = WishlistName::with('user')->where('code', $request->code)->first();
        if (Auth::check()) {
            if (isset($wishlist)) {
                $data['wishlistProperties'] = WishlistProperties::with('property')->where('wishlist_name_id', $wishlist->id)->where('user_id', Auth::id())->get();
            } else {
                return redirect()->route('home');
            }
        } else {
            $data['wishlistProperties'] = WishlistProperties::with('property')->where('wishlist_name_id', $wishlist->id)->distinct()->get();
        }

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.wishlist.wishlist_listing', $data);
        } else {
            return view('wishlist.wishlist_listing', $data);
        }
        return view('wishlist.wishlist_listing', $data);
    }

    public function leaveWishlist(Request $request)
    {
        $user_id = Auth::id();
        $wishlistGroup = WishlistName::find($request->id);

        $shared_users = explode(',', $wishlistGroup->share_with);

        if (($key = array_search($user_id, $shared_users)) !== false) {
            unset($shared_users[$key]);
            $shared_users = array_values($shared_users);

            if (empty($shared_users)) {
                $wishlistGroup->share_with = null;
            } else {
                $wishlistGroup->share_with = implode(',', $shared_users);
            }

            $wishlistGroup->save();
        }
        return response()->json(['msg' => "Success"]);
    }

    public function updateCollaborate(Request $request)
    {
        $wishlistGroup = WishlistName::where('code', $request->code)->first();
        $wishlistGroup->is_collaborate = '1';
        $wishlistGroup->save();
        return response()->json(['msg' => 'Success']);
    }

    public function collaborateUser(Request $request)
    {
        $wishlist = WishlistName::where('code', $request->code)->first();

        $existingShareWith = $wishlist->share_with;

        $newUserId = Auth::guard('web')->user()->id;
        // dd($existingShareWith, $newUserId);
        if (!in_array($newUserId, explode(',', $existingShareWith))) {
            $newShareWith = $existingShareWith ? $existingShareWith . ',' . $newUserId : $newUserId;
            $wishlist->share_with = $newShareWith;
            $wishlist->is_collaborate = '1';

            $wishlist->save();
        }

        $wishlistProperties = WishlistProperties::where('wishlist_name_id', $wishlist->id)->get();
        // dd($wishlistProperties);
        foreach ($wishlistProperties as $properties) {

            $wishlistProperties = new WishlistProperties;
            $wishlistProperties->wishlist_name_id = $wishlist->id;
            $wishlistProperties->property_id = $properties->property_id;
            $wishlistProperties->user_id = $newUserId;
            // dd($wishlistProperties);
            $wishlistProperties->save();
        }
        return redirect()->route('wishlists.listing');
    }
}
