<?php

namespace App\Http\Controllers;


use Auth;
use Illuminate\Support\Facades\Validator;
use Session;
use DateTime;
use App\Notifications\UserNotify;
use App\Notifications\UserNotifySms;
use App\Enums\PaymentTypeEnum;
use App\Enums\TransactionActionEnum;
use App\Http\Services\TabbyService;
use Illuminate\Support\Facades\URL;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;




use App\Http\{
    Requests,
    Helpers\Common,
    Controllers\EmailController
};
use App\Http\Services\FatoorahService;
use App\Http\Services\HyperpaySdkService;
use App\Http\Services\HyperpayService;
use App\Http\Services\MoyasarService;
use App\Http\Services\BookingService;
use App\Http\Services\Silkhaus\ReservationService;
use App\Models\{
    Admin,
    Bank,
    Payouts,
    Currency,
    Country,
    Settings,
    Payment,
    Photo,
    Withdraw,
    Messages,
    Wallet,
    Properties,
    Bookings,
    PaymentMethods,
    BookingDetails,
    BookingPaymentDetails,
    PropertyDates,
    PropertyPrice,
    PaymentResponse,
    User,
    PropertyFees,
    UserCards,
    Transactions,
    UserWallet,
    PromoCode,
    PromoCodeUsage,
    DiscountUsage,
    ElmDocument,
    BookingStatusLog,
    CustomPricing,
    GridProperty,
    HostReferalCode,
    NewUserWallet,
    PropertyPhotos,
    Transaction,
    TransactionCategories,
    TransactionTypes,
};
use App\Http\Services\HyperpayService as ServicesHyperpayService;
use App\Http\Services\PlatformReservationService;
use App\Http\Services\PropertyReservationService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Query\JoinClause;
use Omnipay\Omnipay;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class PaymentController extends Controller
{

    public $myfatoorah_apiURL;
    public $myfatoorah_apikey;

    // public $payzaty_apiURL;
    // public $payzaty_secret_key;

    public $paymentMethods;
    public $paymentMethodId;
    public $callbackUrl;
    public $errorUrl;
    public $curl_ssl;
    public $scriptUrl;

    protected $helper;
    protected $tabbyService;
    private $bookingService;
    protected $propertyReservationService;

    public function __construct(BookingService $bookingService, PropertyReservationService $propertyReservationService)
    {
        $this->bookingService = $bookingService;
        $this->propertyReservationService = $propertyReservationService;
        /* ------------------------ Configurations ---------------------------------- */
        if (env('MYFATOORAH_MODE') == 'test') {
            $this->myfatoorah_apiURL = 'https://apitest.myfatoorah.com/v2/';
            $this->myfatoorah_apikey = env('MYFATOORAH_API_KEY_TEST'); //Test token value to be placed here: https://myfatoorah.readme.io/docs/test-token
            $this->curl_ssl = false;
        } else {
            $this->myfatoorah_apiURL = 'https://api-sa.myfatoorah.com/v2/';
            $this->myfatoorah_apikey = env('MYFATOORAH_API_KEY_LIVE'); //Live token value to be placed here: https://myfatoorah.readme.io/docs/live-token
            $this->curl_ssl = true;
        }

        $this->callbackUrl = route('paymentCallback', ['status' => 1]);
        $this->errorUrl = route('paymentCancel', ['status' => 1]);
        $this->paymentMethodId = 2; // it maybe change on prod
        $this->tabbyService = new TabbyService;
        $this->helper = new Common;

        //Hyperpay mode
        $HyperpayMode = env('HYPERPAY_MODE');
        if ($HyperpayMode == "test") {

            $this->scriptUrl = 'https://eu-test.oppwa.com/';
        } else {

            $this->scriptUrl = 'https://eu-prod.oppwa.com/';
        }
    }

    /**
     * Handle web reservation
     */
    public function webReservation(Request $request)
    {
        $result = $this->propertyReservationService->processReservation($request, 'web');
        if (!!$result['redirect']) {
            return redirect($result['redirect_web']);
        }
        // if (!$result['success']) {
        //     return redirect()->back()->with('error', $result['message']);
        // }

        // if ($result['redirect_web']) {
        //     return redirect()->away($result['redirect_web']);
        // }

        // return redirect()->route('booking.success')->with('data', $result['data']);
    }

    public function propertyReservation(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            "first_name" => "required|string|max:255",
            "last_name" => "required|string|max:255",
            "email" => [
                'nullable',
                'regex:/^[\w\.\-]+@([\w\-]+\.)+[a-zA-Z]{2,7}$/',
                Rule::unique('users')->ignore($user->id),
            ],
        ], [
            'email.regex' => 'The email format is invalid. Please enter a valid email address.',
            'email.unique' => 'This email is already taken by another user.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        if ($request->selectedPaymentId === 'ch-tabby-pay') if ($request->coupon != null) return redirect()->back()->with('error', 'Promo code is not available for Tabby.');

        $processedData = $this->bookingService->beforePayment($request);
        if ($processedData === 'PNA' || !empty($processedData['error'])) {
            return redirect()->back()->with('error', 'The property is not available for the selected dates.');
        }
        //updating auth user


        if ($user->is_guest) {
            $user->first_name = $request->first_name;
            $user->last_name = $request->last_name;
            $user->email = $request->email;
            $user->is_guest = false;
            $user->save();
        } else if (empty($user->email) && !empty($request->email)) {
            $user->email = $request->email;

            $user->save();
        }

        $isMobile = $this->helper->isRequestFromMobile($request);
        $request->merge(['booking_source' => ($isMobile) ? 'web-mobile' : "desktop"]);
        $data = $this->bookingService->storeBooking($processedData, $request);
        $finalamount = $data['finalamount'];
        $propId = $request->property_id;
        $actualAmount = $data['total'];
        $userlogin = Auth::user()->id;
        $walletData = [];
        $property = Properties::find($propId);

        if ($property->property_discount && $request->selectedPaymentId != "ch-tabby-pay") {
            $discountusage = DiscountUsage::firstOrNew(['user_id' => $userlogin, 'property_id' => $propId, 'booking_id' => $data['booking_id']]);
            $discountusage->fill([
                'booking_id' => $data['booking_id'],
                'original_amount' => $processedData['price_list']->total,
                'after_discount' => $processedData['price_list']->total_with_discount
            ]);
            $discountusage->save();
        }


        if ($data['booking_type'] == 'request' && $request->booking_type_1 == '0') {
            $booking = Bookings::find($data['booking_id']);
            $text = $property->bedrooms . " bedrooms(s) " . $property->propertyType->name;
            $data = [
                'heading' => $text,
                'guest_name' => $booking->users->FullName,
                'image_path' => $property->cover_photo,
            ];

            $bookingAmount = $booking->total_with_discount ?: $booking->total;

            $hosts = User::select('users.*')->join('properties AS ps', 'ps.host_id', 'users.id')
                ->leftJoin('properties_cohosts AS pcs', fn($q) => $q->on('pcs.co_host_id', 'users.id')->whereNull('pcs.deleted_at'))
                ->where('ps.id', $booking->property_id)->distinct()->get();
            Notification::send($hosts, new UserNotify(
                'booking.request.new.host',
                route('managehost.all_reservation'),
                [':property' => $property->name],
                ['slug' => 'booking', 'tab' => 'pending-bookings']
            ));

            return redirect('booking/requested?code=' . $booking->code)->with('successmodal', "Request Send successfully");
        } elseif ($data['booking_type'] === 'instant' && ($request->booking_type_1 == 0 || $request->booking_type_1 == 2)) {
            if ($finalamount <= 0) {
                return redirect()->route('contact.admin', ['bookingid' => $data['booking_id']]);
            }

            $booking = Bookings::with('host')->find($data['booking_id']);
            $data['booking'] = $booking;
            $data['finalamount'] = $finalamount;

            $paymentURL = null;

            if ($request->selectedPaymentId == "ch-tabby-pay") { //tabby payment Url
                $result = $this->tabbyService->createSession($data);
                if ($result->status === "created") {
                    $paymentURL = $result->configuration->available_products->installments[0]->web_url;
                } elseif ($result->status === "rejected" && $result->rejection_reason_code === "under_limit") {
                    $this->helper->one_time_message('error', customTrans('payment.tabby_under_limit_error'));
                    return redirect()->back();
                }
            } else {
                try {
                    // -------------------------------- WALLET INTEGRATION --------------------------------------------
                    if ($request->has('pay_by_wallet')) {
                        $walletData = $this->helper->payByWallet($finalamount, $booking);
                        $finalamount = $walletData['finalamount'];
                        if ($finalamount == 0) {
                            $url = url('/') . '/payment/callback/' . Bookings::PAYMENT_STATUS_WALLET . '?booking=' . $data['booking_id'];
                            return redirect()->away($url);
                        }
                    }

                    // -------------------------------- WALLET INTEGRATION --------------------------------------------
                    if (app('PAYMENT_METHOD') == Bookings::PAY_BY_MOYASAR) {
                        $paymentURL = (new MoyasarService())->payment('credit_card', $finalamount, route('payment.initiated'), $exist_card, ['payer' => auth()->user(), 'payee' => $booking->host, 'service' => $booking, 'action' => 1, 'data' => ['booking' => $booking->toArray()]], ['walletData' => $walletData]);
                    } elseif (app('PAYMENT_METHOD') == Bookings::PAY_BY_HYPERPAY) {

                        $hosts = User::select('users.*')->join('properties AS ps', 'ps.host_id', 'users.id')
                            ->leftJoin('properties_cohosts AS pcs', fn($q) => $q->on('pcs.co_host_id', 'users.id')->whereNull('pcs.deleted_at'))
                            ->where('ps.id', $propId)->distinct()->get();
                        Notification::send($hosts, new UserNotify(
                            'guest.booking.payment.click.host',
                            route('managehost.all_reservation'),
                            data: ['slug' => 'booking', 'tab' => 'upcoming-bookings']
                        ));
                        $shopperResultUrl = route('payment.hyperinitiated');

                        $data = (new HyperpaySdkService())->checkout($request->paymentMethodId, $finalamount, $booking, $shopperResultUrl, ['booking' => $booking->id, 'walletData' => $walletData]);
                        //    $data = (new HyperpaySdkService())->checkout(11, $finalamount, $booking, $shopperResultUrl, ['booking' => $booking->id, 'walletData' => $walletData]);
                        Session::put('hyperpayresponce', $data);
                        // $this->ProcessPayment($data);
                        return redirect()->route('ProcessPayment');
                        // return view('check', compact('data'));
                    } else {
                        $rand_number = $this->helper->randomCode(3);
                        $customer_ref = $rand_number . "-B-" . $booking->id;
                        $paymentURL = (new FatoorahService())->execute($finalamount, $exist_card, $customer_ref, $booking->toArray());
                    }
                } catch (Exception $e) {
                    if ($e instanceof ValidationException) {
                        $this->helper->one_time_message('error', $e->getMessage());
                        return redirect()->back();
                    } else {
                        logger()->error($e);
                    }
                }
            }
            return redirect($paymentURL);
        } elseif ($data['booking_type'] == 'instant' && $request->booking_type_1 == 1) {
            if ($request->has('pay_by_wallet')) {
                // -------------------------------- WALLET INTEGRATION --------------------------------------------
                $walletData = $this->helper->payByWallet($finalamount, $data['booking']);
                $finalamount = $walletData['finalamount'];

                if ($request->has('pay_by_wallet') && $finalamount == 0) {
                    $url = url('/') . '/payment/callback/' . Bookings::PAYMENT_STATUS_WALLET . '?booking=' . $data['booking_id'];
                    return redirect()->away($url);
                }
                // -------------------------------- WALLET INTEGRATION --------------------------------------------
            }
            session([
                'booking_id' => $data['booking_id'],
                'walletData' => $walletData ?? null,
            ]);

            // return $request->paymentMethodId;
            $shopperResultUrl = route('payment.hyperinitiated');
            $booking = Bookings::with('host')->find($data['booking_id']);
            $data = (new HyperpaySdkService())->checkout(11, $finalamount, $booking, $shopperResultUrl, ['booking' => $booking->id, 'walletData' => $walletData]);

            // return $data;
            Session::put('hyperpayresponce', $data);
            // $this->ProcessPayment($data);
            return redirect()->route('ProcessPayment');

            // return redirect()->route('payment.success');
        } else {

            $this->helper->one_time_message('error', 'Something went wrong with booking mode / Payment Method');
            return redirect()->back();
        }
    }

    public function paymentSuccess(Request $request)
    {
        // Retrieve the data from the session
        $booking_id = $request->session()->get('booking_id');
        $booking = Bookings::with('users')->findOrFail($booking_id);
        if ($request->session()->has('walletData') && !empty($request->session()->get('walletData'))) {
            $walletData = $request->session()->get('walletData');
            $finalamount = $walletData['finalamount'] * 100;
            return view('payment.testapple', compact(['booking', 'finalamount', 'walletData']));
        }

        $finalamount = ($booking->total_with_discount ?: $booking->total) * 100;
        return view('payment.testapple', compact(['booking', 'finalamount']));
    }

    public function paymentMobileSuccess(Request $request)
    {
        // Retrieve the data from the session
        // Check if booking_id is available in the query parameters

        if ($request->query('booking_id')) {
            $booking_id = $request->query('booking_id');
        } else {
            // Retrieve the booking_id from the session
            $booking_id = $request->session()->get('booking_id');
        }
        // Find the booking using the booking_id
        $booking = Bookings::with('users')->findOrFail($booking_id);
        // Check if walletData is available in the query parameters
        if ($request->query('walletData')) {
            $walletData = json_decode($request->query('walletData'), true);
            if (is_array($walletData) && isset($walletData['finalamount'])) {
                $finalamount = $walletData['finalamount'] * 100;
                return view('payment.testapple', compact(['booking', 'finalamount', 'walletData']));
            } else {
                // Handle the case where walletData is empty or doesn't contain 'finalamount'
                // For example, set a default value or return an error
                $finalamount = ($booking->total_with_discount ?: $booking->total) * 100;
                return view('payment.testapple', compact(['booking', 'finalamount']));
            }
        }

        // Calculate the final amount if walletData is not available
        $finalamount = ($booking->total_with_discount ?: $booking->total) * 100;
        return view('payment.testapple', compact(['booking', 'finalamount']));
    }

    public function walletdepositSuccess(Request $request)
    {
        // Retrieve the data from the session
        $wallet_deposit = $request->session()->get('wallet_deposit');
        if ($wallet_deposit) {
            $finalamount = $request->session()->get('amount') * 100;
            $walletID = null;
            return view('payment.walletdepositapple', compact('wallet_deposit', 'finalamount', 'walletID'));
        }
    }

    public function walletMobiledepositSuccess(Request $request)
    {
        $wallet_deposit = null;
        $finalamount = 0;
        $walletID = null;
        // Check if wallet_deposit is available in the query parameters
        if ($request->query('wallet_deposit')) {
            $wallet_deposit = $request->query('wallet_deposit');
            $finalamount = $request->query('amount') * 100;
            $walletID = $request->query('wallet');
        } elseif ($request->session()->has('wallet_deposit')) {
            // Retrieve wallet_deposit from the session
            $wallet_deposit = $request->session()->get('wallet_deposit');
            $finalamount = $request->session()->get('amount') * 100;
        }

        // Return the view with necessary data if wallet_deposit is available
        if ($wallet_deposit) {
            return view('payment.walletdepositapple', compact('wallet_deposit', 'finalamount', 'walletID'));
        }
    }


    public function BeforePaymentProcess($request)
    {

        if ($request->isMethod('post')) {

            Session::put('payment_property_id', $request->id);
            Session::put('payment_property_slug', $request->property_slug);
            Session::put('payment_checkin', Carbon::parse($request->url_checkin)->format('Y-m-d'));
            Session::put('payment_checkout', Carbon::parse($request->url_checkout)->format('Y-m-d'));
            Session::put('payment_number_of_guests', $request->number_of_guests);
            Session::put('payment_booking_type', $request->booking_type);
            Session::put('payment_booking_status', $request->booking_status);
            Session::put('payment_booking_id', $request->booking_id);

            $booking_id = Session::get('payment_booking_id');
        }
        $id = Session::get('payment_property_id');
        $property_slug = Session::get('payment_property_slug');
        $number_of_guests = Session::get('payment_number_of_guests');
        $checkin = Session::get('payment_checkin');
        $checkout = Session::get('payment_checkout');
        $booking_type = Session::get('payment_booking_type');
        $booking_status = Session::get('payment_booking_status');


        if (!$request->isMethod('post') && !$checkin) {
            return redirect('properties/' . $request->property_slug);
        }

        $data['result'] = Properties::find($id);
        $data['property_id'] = $id;
        $data['number_of_guests'] = $number_of_guests;
        $data['booking_type'] = $booking_type;
        $data['checkin'] = setDateForDb($checkin);
        $data['checkout'] = setDateForDb($checkout);
        $data['status'] = $booking_status ?? "";
        $data['booking_id'] = $booking_id ?? "";
        $from = new DateTime(setDateForDb($checkin));
        $to = new DateTime(setDateForDb($checkout));
        $data['nights'] = $to->diff($from)->format("%a");
        $data['guest_adult'] = $request->guest_adult;
        $data['guest_child'] = $request->guest_child;

        $data['price_list'] = json_decode($this->helper->getPrice($data['property_id'], $data['checkin'], $data['checkout'], $data['number_of_guests']));

        Session::put('payment_price_list', $data['price_list']);

        if (((isset($data['price_list']->status) && !empty($data['price_list']->status)) ? $data['price_list']->status : '') == 'Not available') {
            return 'PNA';
        }

        $data['currencyDefault'] = $currencyDefault = Currency::getAll()->firstWhere('default', 1);

        $data['price_eur'] = numberFormat($this->helper->convert_currency($data['result']->property_price->code, $currencyDefault->code, $data['price_list']->total), 2);
        $data['price_rate'] = $this->helper->currency_rate($data['result']->property_price->currency_code, $this->helper->getCurrentCurrencycode());
        $data['country'] = Country::where('short_name')->take(1)->pluck('name', 'short_name');
        $data['title'] = 'Pay for your reservation';
        $data['currentCurrency'] = $this->helper->getCurrentCurrency();

        $data['status'] = $booking_type == 'request' ? 'Pending' : 'Unpaid';

        return $data;
    }

    public function store($data, $request)
    {
        // ---------------------------------Referal link Work Added-----------------------------------------------------
        $referal_code = null;
        $hostReferalCodeObj = new HostReferalCode();
        $referal_code_session = $request->session()->get('referal_code');
        if ($referal_code_session && $hostReferalCodeObj::validateReferalCode($referal_code_session, $data['property_id'])) {
            $referal_code = $referal_code_session;
        }

        // ---------------------------------Referal link Work Added-----------------------------------------------------

        $property = Properties::with('property_price')->find($data['property_id']);
        $currencyDefault = Currency::getAll()->where('default', 1)->first();
        $userlogin = Auth::user()->id;

        $booking = new Bookings;
        $booking->property_id = $data['property_id'];
        $booking->host_id = $property->host_id;
        $booking->user_id = Auth::user()->id;
        $booking->start_date = setDateForDb($data['checkin']);
        $checkinDate = onlyFormat($booking->start_date);
        $booking->end_date = setDateForDb($data['checkout']);
        // Check if start_date and end_date are the same
        if ($booking->start_date === $booking->end_date) {
            $booking->end_date = Carbon::parse($booking->end_date)->addDay()->toDateString();
        }
        if ($booking->start_date < Carbon::now()->toDateString() || $booking->end_date < $booking->start_date) {

            return "invalid date formate";
        }
        $booking->guest = $data['number_of_guests'];
        $booking->guest_adult = $data['guest_adult'];
        $booking->guest_child = $data['guest_child'];
        $booking->checkin_time = $property->checkinTime;
        $booking->checkout_time = $property->checkoutTime;
        $propertyFees = PropertyFees::pluck('value', 'field');
        $guest_service_charge = $propertyFees['guest_service_charge'];
        $booking->service_fee_percent = $data['price_list']->service_fee_percent;
        $booking->guest_fee_percent = $guest_service_charge;
        $booking->host_fee_percent = $data['price_list']->host_fee_percent;
        $booking->flat_discount_percent = $data['price_list']->flat_discount_percent;
        $booking->sub_total = $data['price_list']->subtotal;
        $booking->flat_discount_percent = $data['price_list']->flat_discount_percent;
        $booking->attachment = $data['attachment'] ?? null;
        $booking->total_night = $data['price_list']->total_nights;
        $booking->per_night = $this->helper->convert_currency('', $currencyDefault->code, $data['price_list']->property_price);

        $booking->custom_price_dates = isset($data['price_list']->different_price_dates_default_curr) ? json_encode($data['price_list']->different_price_dates_default_curr) : null;

        $booking->base_price = $this->helper->convert_currency('', $currencyDefault->code, $data['price_list']->subtotal);
        $booking->cleaning_charge = $cleaning_fee = $this->helper->convert_currency('', $currencyDefault->code, $data['price_list']->cleaning_fee);

        $service_fee_on_cleaning = $this->helper->convert_currency('', $currencyDefault->code, $data['price_list']->service_fee_cleaning ?? 0);


        $booking->guest_charge = $additional_guest = $this->helper->convert_currency('', $currencyDefault->code, $data['price_list']->additional_guest);
        $booking->iva_tax = $iva_tax = $this->helper->convert_currency('', $currencyDefault->code, $data['price_list']->iva_tax);
        $booking->accomodation_tax = $accomodation_tax = $this->helper->convert_currency('', $currencyDefault->code, $data['price_list']->accomodation_tax);
        $booking->security_money = $security_fee = $this->helper->convert_currency('', $currencyDefault->code, $data['price_list']->security_fee);
        if (isset($referal_code)) {
            $property_fees = PropertyFees::where('field', PropertyFees::DARENT_COMMISSION)->first();
            $percent = (int)$property_fees->value;
            $booking->host_fee_percent = $percent;
            $CleaningSecurityTotalNightFee = $data['price_list']->total_night_price + $data['price_list']->cleaning_fee + $data['price_list']->security_fee;
            $referal_host_fee = round(($percent / 100) * $CleaningSecurityTotalNightFee, 2);
            $booking->host_fee = $host_fee = $this->helper->convert_currency('', $currencyDefault->code, $referal_host_fee);
        } else {
            $booking->host_fee = $host_fee = $this->helper->convert_currency('', $currencyDefault->code, $data['price_list']->host_fee);
        }

        $booking->insurance_fee = $data['price_list']->tawunya_insurance_fee;
        $booking->total = $finalamount = $this->helper->convert_currency('', $currencyDefault->code, $data['price_list']->total);

        $promo_success = false;

        if ($request->coupon) {
            $couponCode = $request->coupon;
            $couponResponse = $this->helper->applyCoupon($couponCode, $request->property_id, $booking->base_price, $userlogin, $request);
            Log::debug('couponResponse', [$couponResponse]);

            // Host Fee Remove from allothers
            $service_fee_on_security_money = $booking->security_money * $booking->guest_fee_percent / 100;
            $allothers = $service_fee_on_cleaning + $cleaning_fee + $additional_guest + $accomodation_tax + $security_fee + $service_fee_on_security_money;
            $promoCodeData = PromoCode::where(['code' => $request->coupon])->first();

            // ------------------------------- WALLET CAMPAIGN -------------------------------------------------
            if (!$promoCodeData->is_campaign) {
                $finalamount = round($couponResponse->getData()->amount_after_discount + $allothers + $iva_tax, 2);
                Log::debug('finalamount', [$finalamount]);
            }
            // ------------------------------- WALLET CAMPAIGN --------------------------------------------------

            if ($couponResponse->getData()->status == 'success' || $couponResponse->getData()->status == 'campaign') {
                $promo_success = true;
                Log::debug('finalamount', [$finalamount]);
                Log::debug('iva_tax', [$iva_tax]);
                $booking->total_with_discount = $finalamount;
                $booking->base_price_with_discount = $couponResponse->getData()->amount_after_discount - $couponResponse->getData()->servicefee;
                $booking->service_charge = $service_fee_on_cleaning + $service_fee_on_security_money + $additional_guest + $accomodation_tax + $couponResponse->getData()->servicefee;
                $booking->total_discount = $couponResponse->getData()->discount_value;

                Log::debug('service_charge', [$service_fee_on_cleaning + $service_fee_on_security_money + $additional_guest + $accomodation_tax + $couponResponse->getData()->servicefee]);
            } else {
                $booking->service_charge = $this->helper->convert_currency('', $currencyDefault->code, $data['price_list']->service_fee);
            }
        } else {
            $booking->total_with_discount = 0;
            $booking->base_price_with_discount = 0;
            $booking->service_charge = $this->helper->convert_currency('', $currencyDefault->code, $data['price_list']->service_fee);
        }

        $booking->currency_code = $currencyDefault->code;
        $booking->transaction_id = $data['transaction_id'] ?? " ";
        $booking->payment_method_id = $data['payment_method_id'] ?? " ";
        $booking->cancellation = $property->cancellation;
        $booking->status = $data['status'];
        $booking->booking_type = Session::get('payment_booking_type');

        foreach ($data['price_list']->date_with_price as $key => $value) {
            $allData[$key]['discount'] = $value->discount;
            $allData[$key]['price'] = $this->helper->convert_currency('', $currencyDefault->code, $value->original_price);
            $allData[$key]['date'] = setDateForDb($value->date);
        }
        $booking->date_with_price = json_encode($allData);
        $booking->referal_code = $referal_code;

        if ($data['price_list']->total_nights >= 7 && $data['price_list']->total_nights < 28) {
            $booking->discount_type = "weekly_discount";
            $booking->discount_type_percent = $property->property_price->weekly_discount;
        }
        if ($data['price_list']->total_nights >= 28) {
            $booking->discount_type = "monthly_discount";
            $booking->discount_type_percent = $property->property_price->monthly_discount;
        }
        $booking->host_discount_amount = $data['price_list']->discount;

        // GENERATE 6 DIGIT CODE
        do {
            $code = $this->helper->randomCode(6);
            $check_code = Bookings::where('code', $code)->get();
        } while (empty($check_code));

        $booking->code = $code;

        $booking->save();
        session()->forget('referal_code');


        BookingStatusLog::updateOrCreate(
            ['booking_id' => $booking->id],
            ['status' => $booking->status, 'changed_by' => Auth::id()]
        );

        if ($promo_success) {

            //Promo Code Entry Booking Id Update
            $promoCode = PromoCode::where(['code' => $couponCode])->first();
            $codeUsage = PromoCodeUsage::firstOrNew(['promo_code_id' => $promoCode->id, 'user_id' => $userlogin, 'property_id' => $request->property_id, 'booking_id' => $booking->id]);


            $codeUsage->fill([
                'booking_id' => $booking->id,
                'discount_type' => $couponResponse->getData()->discount_type,
                'discount_value' => $couponResponse->getData()->discount_value,
                'original_amount' => $booking->total,
                'after_discount' => $finalamount
            ]);
            $codeUsage->save();
        }


        $booking_details['country'] = $data['country'];

        foreach ($booking_details as $key => $value) {
            $booking_details = new BookingDetails;
            $booking_details->booking_id = $booking->id;
            $booking_details->field = $key;
            $booking_details->value = $value;
            $booking_details->save();
        }


        $propertyCurrencyCode = PropertyPrice::firstWhere('property_id', $data['property_id'])->currency_code;
        foreach ($data['price_list']->date_with_price as $dp) {
            $tmp_date = setDateForDb($dp->date);
            $property_id = $data['property_id'];
            $price = $this->helper->convert_currency($data['price_list']->currency, $propertyCurrencyCode, $dp->original_price);
            Session::put('propertydatedata', ['property_id' => $property_id, 'status' => "Not available", 'price' => $price, 'date' => $tmp_date]);
        }

        $bookingdata['booking'] = $booking;
        $bookingdata['host'] = $booking->host;
        $bookingdata['booking_id'] = $booking->id;
        $bookingdata['total'] = $booking->total;
        $bookingdata['booking_type'] = $booking->booking_type;
        $bookingdata['booking_code'] = $booking->code;

        //for promo discount
        $bookingdata['service_charge'] = $booking->service_charge;
        $rand_number = $this->helper->randomCode(3);

        $bookingdata['customer_ref'] = $rand_number . "-B-" . $booking->id;


        $bookingdata['finalamount'] = $finalamount;

        return $bookingdata;
    }

    /* ------------------------ Functions --------------------------------------- */

    public function myFatoorahPaymentExecute($bookingid)
    {
        // dd($bookingid);

        //Fill POST fields array


        $booking = Bookings::with('host')->find($bookingid);


        $postFields = [
            'PaymentMethodId' => $this->paymentMethodId,
            'InvoiceValue' => $booking->total,
            // "CallBackUrl"           => 'https://google.com',
            // "ErrorUrl"              => 'https://yahoo.com',
            'CallBackUrl' => route('paymentCallback', ['status' => 1, 'booking_id' => $booking->id]),
            'ErrorUrl' => route('paymentCallback', ['status' => 0, 'booking_id' => $booking->id]),
            'CustomerName' => auth()->user()->FullName,
            'DisplayCurrencyIso' => 'SAR',
            'MobileCountryCode' => '+966',
            'CustomerEmail' => isset(auth()->user()->email) ? auth()->user()->email : '<EMAIL>',
            'Language' => app()->getLocale(),
        ];


        $data = $this->helper->callAPI($this->myfatoorah_apiURL . 'ExecutePayment', $this->myfatoorah_apikey, $postFields);

        $hosts = User::select('users.*')->join('properties AS ps', 'ps.host_id', 'users.id')
            ->leftJoin('properties_cohosts AS pcs', fn($q) => $q->on('pcs.co_host_id', 'users.id')->whereNull('pcs.deleted_at'))
            ->where('ps.id', $booking->property_id)->distinct()->get();
        Notification::send($hosts, new UserNotify(
            'guest.booking.payment.click.host',
            route('managehost.all_reservation'),
            data: ['slug' => 'booking', 'tab' => 'upcoming-bookings']
        ));
        return $data;
    }

    public function addCard(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'number' => 'required|digits:16',
            'month' => 'required|min:2,max:2',
            'year' => 'required|min:2,max:2',
            'cvv' => 'required|min:3,max:3',
        ], [
            'name.required' => __('validation.name.required'),
            'number.required' => __('validation.number.required'),
            'number.min' => __('validation.number.min'),
            'number.max' => __('validation.number.max'),
            'month.required' => __('validation.month.required'),
            'month.min' => __('validation.month.min'),
            'month.max' => __('validation.month.max'),
            'year.required' => __('validation.year.required'),
            'year.min' => __('validation.year.min'),
            'year.max' => __('validation.year.max'),
            'cvv.required' => __('validation.cvv.required'),
            'cvv.min' => __('validation.cvv.min'),
            'cvv.max' => __('validation.cvv.max'),
        ]);


        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        } else {
            $exist_card = UserCards::where([
                'card_number' => $request->number,
                'is_verified' => 0,
                'user_id' => auth()->user()->id
            ])
                ->first();

            if ($exist_card) {
                $exist_card->forceDelete();
            }

            $exist_verified_card = UserCards::where([
                'card_number' => $request->number,
                'is_verified' => 1,
                'user_id' => auth()->user()->id
            ])
                ->first();


            if ($exist_verified_card) {
                return response()->json(['error' => true, 'error_message' => 'Card already exist'], 200);
            }

            $usercard = new UserCards;
            $usercard->user_id = auth()->user()->id;
            $usercard->card_number = $request->number;
            $usercard->card_name = $request->name;
            $usercard->card_month = $request->month;
            $usercard->card_year = $request->year;
            $usercard->card_cvv = $request->cvv;
            $usercard->card_type = $request->paymentMethodId == PaymentTypeEnum::VISAMASTER ? 'visa' : 'mada';
            $usercard->paymentMethodId = $request->paymentMethodId;
            $usercard->save();

            $rand_number = $this->helper->randomCode(3);


            $testamount = Settings::where('name', 'cc_init_verification_amount')->first();
            try {
                // $url = (new MoyasarService())->payment('credit_card', ($testamount?->value ?? 2), route('payment.initiated'), $usercard, ['payer' => auth()->user(), 'payee' => Admin::first(), 'service' => $usercard, 'action' => 1, 'data' => ['identifier' => $usercard->card_number]]);

                if (app('PAYMENT_METHOD') == Bookings::PAY_BY_MOYASAR) {
                    $url = (new MoyasarService())->payment('credit_card', ($testamount?->value ?? 2), route('payment.initiated'), $usercard, ['payer' => auth()->user(), 'payee' => Admin::first(), 'service' => $usercard, 'action' => 1, 'data' => ['identifier' => $usercard->card_number]]);
                } elseif (app('PAYMENT_METHOD') == Bookings::PAY_BY_HYPERPAY) {
                    // Hyperpay Integration
                    $hyperpayService = new HyperpayService();
                    $shopperResultUrl = route('payment.hyperinitiated');
                    $response = $hyperpayService->makePayment(($testamount?->value ?? 2), 'SAR', $request->paymentMethodId ?? 'VISA', $exist_card, $shopperResultUrl, ['service' => $usercard, 'action' => 1, 'data' => ['identifier' => $usercard->card_number]], ['card' => $usercard->id]);
                    if ($response['status'] == 'success' && isset($response['redirect'])) {
                        $redirectUrl = $response['redirect'];
                        $redirectParams = $response['parameters'];
                        // Build the query string from the parameters
                        $queryParams = [];
                        foreach ($redirectParams as $param) {
                            if (isset($param['name']) && isset($param['value'])) {
                                $queryParams[$param['name']] = $param['value'];
                            }
                        }
                        $queryString = http_build_query($queryParams);
                        // Redirect to the constructed URL
                        $url = $redirectUrl . '?' . $queryString;
                    }
                }
            } catch (Exception $e) {
                if ($e instanceof ValidationException) {
                    if (request()->ajax()) {
                        return response()->json(['error' => true, 'custom_error' => 'Invalid card'], 422);
                    }
                    $this->helper->one_time_message('error', "Invalid card");
                    return redirect()->back();
                }
            }
            return response()->json(['message' => 'Success', 'data' => $url], 200);
        }
    }

    public function couponCheck(Request $req)
    {
        return $this->helper->applyCoupon($req->couponNumber, $req->propertyid, $req->amount, $req->user, $req);
    }

    public function addBookingPaymentInHostWallet($booking)
    {
        $walletBalance = Wallet::where('user_id', $booking->host_id)->first();
        $default_code = Currency::getAll()->firstWhere('default', 1)->code;
        $wallet_code = Currency::getAll()->firstWhere('id', $walletBalance->currency_id)->code;
        $balance = ($walletBalance->balance + $this->helper->convert_currency($default_code, $wallet_code, $booking->total) - $this->helper->convert_currency($default_code, $wallet_code, $booking->service_charge) - $this->helper->convert_currency($default_code, $wallet_code, $booking->accomodation_tax) - $this->helper->convert_currency($default_code, $wallet_code, $booking->iva_tax));
        Wallet::where(['user_id' => $booking->host_id])->update(['balance' => $balance]);
    }

    /* Initiate Payment Endpoint Function */

    function initiatePayment($myfatoorah_apiURL, $myfatoorah_apikey, $postFields)
    {

        $json = $this->helper->callAPI($myfatoorah_apiURL, $myfatoorah_apikey, $postFields);
        return $json->PaymentMethods;
    }

    /* -------------------------------------------------------------------------- */

    public function callback(Request $request, $status)
    {
        Log::debug('hyperpay', ['status' => $status, ...$request->all()]);
        $data['title'] = 'Payment | Darent';
        if (!$request->booking && !$request->card && !$request->wallet) {
            abort(404);
        }
        $data['paymentstatus'] = $status != 0 ? $status : 0;
        if ($request->wallet) {
            $data['type'] = 'wallet';
            if ($this->helper->isRequestFromMobile($request)) {
                $view = $request->booking ? 'mobile.pages.after_payment' : 'mobile.pages.card_after_payment';
                return view($view, $data);
            } else {
                $view = $request->booking ? 'pages.after_payment' : 'pages.card_after_payment';
                return view($view, $data);
            }
        }
        isset(auth()->user()->is_elm_verified) && auth()->user()->is_elm_verified ? $data['is_elm_document'] = 1 : $data['is_elm_document'] = 0;

        $bk_id = null;
        $is_booking = !!$request->booking;
        if ($is_booking) {
            $bk_id = $request->booking;
        }
        $data['transactionStatus'] = 'Failed';
        if ($data['paymentstatus']) {
            $data['transactionStatus'] = 'Success';
            if ($is_booking) {
                $booking = Bookings::findOrFail($bk_id);

                if ($status == 2) {
                    // In your controller or service: for third party bookings
                    $platformReservationService = new PlatformReservationService();
                    $platformReservationService->createPlatformReservation($booking);
                }

                //dates blocks in property_dates table
                if (app()->environment('local')) {
                    $price_list = json_decode($this->helper->getPrice($booking->property_id, $booking->start_date, $booking->end_date, $booking->guest, booking_id: $booking->id));
                    if (isset($price_list->date_with_price)) {
                        foreach ($price_list->date_with_price as $dp) {
                            $tmp_date = setDateForDb($dp->date);
                            $property_id = $booking->property_id;
                            $property_data = [
                                'property_id' => $property_id,
                                'booking_id' => $booking->id,
                                'status' => 'Not available',
                                'price' => 0,
                                'date' => $tmp_date
                            ];
                            $insertedDates = PropertyDates::create($property_data);
                        }
                    }
                }


                if (app()->environment('local')) {
                    $bookingdetails = $this->helper->payableToHost($booking);

                    $promo_host_discount = 0;
                    $promo_darent_discount = 0;

                    BookingPaymentDetails::firstOrCreate([
                        'booking_id' => $booking->id
                    ], [
                        'guest_id' => $booking->user_id,
                        'host_id' => $bookingdetails['hostid'],
                        'payment_key' => 'PaymentId',
                        'payment_value' => $request->id,
                        'guest_paid' => $bookingdetails['guestpaid'],
                        'vat' => $bookingdetails['VAT'],
                        'commission' => $bookingdetails['commission'],
                        'commission_in_percenatge' => $bookingdetails['commission_in_percenatge'],
                        'insurance_fees_in_percentage' => $bookingdetails['insurance_fees_percentage'],
                        'security_deposit' => $bookingdetails['security_deposit'],
                        'host_pay' => $bookingdetails['host_pay'],
                        'host_discount' => $promo_host_discount,
                        'darent_discount' => $promo_darent_discount,
                    ]);

                    // $booking->update(['status' => "Accepted", "is_payment" => 1]);
                    //reverting shabib work
                }

                BookingStatusLog::updateOrCreate(
                    ['booking_id' => $booking->id],
                    ['status' => $booking->status, 'changed_by' => Auth::id()]
                );

                $codeUsage = PromoCodeUsage::where(['booking_id' => $booking->id])->orderByDesc('id')->first();
                $discount_usage = DiscountUsage::where(['booking_id' => $booking->id])->first();


                $booking->load(['properties' => function ($q) {
                    $q->with(['property_address', 'propertyType']);
                }, 'promoCodeUsage' => function ($q) {
                    $q->with(['promoCode']);
                }]);

                $data['eventData'] = json_encode([
                    'value' => (float)number_format($booking->total, 2, '.', ''),
                    'tax' => (float)number_format($booking->service_charge, 2, '.', ''),
                    'currency' => Session::get('currency'),
                    'transaction_id' => $booking->transaction_id,
                    'coupon' => isset($booking->promoCodeUsage->code) ? $booking->promoCodeUsage->code : null,
                    'transaction_type' => $booking->booking_type,
                    'reservation_month' => Carbon::parse($booking->start_date)->format('F'),
                    'reservation_duration' => $booking->total_night,
                    'item_id' => $booking->property_id,
                    'affiliation' => 'Darent',
                    'discount' => (float)number_format($booking->total_discount, 2, '.', ''),
                    'item_type' => $booking->properties->propertyType->name,
                    'item_city_name' => $booking->properties->city_name,
                    'item_host_id' => $booking->host_id,
                    'price' => (float)number_format($booking->per_night, 2, '.', ''),
                    'quantity' => $booking->total_night,
                    'total_price' => (float)number_format($booking->total, 2, '.', ''),
                ]);

                $data['snapPixelData'] = json_encode([
                    'price' => (float)number_format($booking->total, 2, '.', ''),
                    'currency' => Session::get('currency'),
                    'transaction_id' => $booking->transaction_id,
                    'item_ids' => $booking->properties->property_code,
                    'item_category' => 'Property',
                    'number_items' => $booking->total_night,
                    'user_email' => auth()->user()->email ?? null,
                    'user_phone_number' => auth()->user()->phone
                ]);

                $data['tikPixelData'] = json_encode([
                    'transaction_id' => $booking->transaction_id,
                    'item_category' => 'Property',
                    'number_items' => $booking->total_night,
                    'email' => auth()->user()->email ?? null,
                    'phone' => auth()->user()->phone,
                    'contents' => [
                        [
                            'content_id' => $booking->properties->property_code,
                            'content_type' => 'product',
                            'content_name' => $booking->properties->name
                        ]
                    ],
                    'value' => (float)number_format($booking->total, 2, '.', ''),
                    'currency' => Session::get('currency'),
                ]);

                //DECLINE OTHER BOOKINGS
                $otherBookings = Bookings::where([
                    'start_date' => $booking->start_date,
                    'end_date' => $booking->end_date,
                    'property_id' => $booking->property_id,
                    'status' => 'Processing',
                ])
                    ->whereNot('id', $booking->id)
                    ->get();

                foreach ($otherBookings as $row) {
                    (User::find($row->user_id))->notify(new UserNotify(
                        'booking.request.declined.reason.assigned',
                        route('managehost.all_reservation'),
                        [':property' => $booking->properties->name],
                        ['slug' => 'reservation', 'tab' => '3']
                    ));

                    $row->fill(['status' => 'Cancelled'])->save();
                }
                //Promo Code id UPDATE in Booking Table
                // if ($codeUsage) {
                //     // $booking->promo_code_usage_id = $codeUsage->id;
                //     $booking->total_with_discount = $codeUsage->after_discount;
                //     $booking->save();
                // }
                //Discount usage Id UPDATE in Booking Table
                if ($discount_usage) {
                    $discount_usage->fill(['is_applied' => 1])->save();
                    $booking->property_discount_id = $discount_usage->id;
                    $booking->save();
                }
            } else {
                $card = UserCards::findOrFail($request->card);
                if (!!$card) {
                    $card->update(['is_verified' => true]);
                }
            }
        }
        if (App::environment('local')) {
            $data['message'] = 'Error';
        } else {
            $data['message'] = $request->status;
        }

        $data['booking_data'] = Bookings::with(['booking_details', 'promoCodeUsage', 'properties'])->find($bk_id);
        Log::debug('payment-status', ['status' => $data['paymentstatus'], 'booking' => $request->booking]);

        if ($this->helper->isRequestFromMobile($request)) {
            $view = $request->booking ? 'mobile.pages.after_payment' : 'mobile.pages.card_after_payment';
            return view($view, $data);
        } else {
            $view = $request->booking ? 'pages.after_payment' : 'pages.card_after_payment';
            return view($view, $data);
        }
    }

    public function paymentCallbackWallet(Request $request, $status)
    {
        $data['title'] = 'Payment | Darent';
        $data['paymentstatus'] = $status != 0 ? $status : 0;
        $data['type'] = 'wallet';

        if ($request->booking) {
            $this->helper->staahBooking($request->booking);

            $booking = Bookings::findOrFail($request->booking);
            $booking->load(['properties' => function ($q) {
                $q->with(['property_address', 'propertyType']);
            }, 'promoCodeUsage' => function ($q) {
                $q->with(['promoCode']);
            }]);

            $data['eventData'] = json_encode([
                'value' => (float)number_format($booking->total, 2, '.', ''),
                'tax' => (float)number_format($booking->service_charge, 2, '.', ''),
                'currency' => Session::get('currency'),
                'transaction_id' => $booking->transaction_id,
                'coupon' => isset($booking->promoCodeUsage->code) ? $booking->promoCodeUsage->code : null,
                'transaction_type' => $booking->booking_type,
                'reservation_month' => Carbon::parse($booking->start_date)->format('F'),
                'reservation_duration' => $booking->total_night,
                'item_id' => $booking->property_id,
                'affiliation' => 'Darent',
                'discount' => (float)number_format($booking->total_discount, 2, '.', ''),
                'item_type' => $booking->properties->propertyType->name,
                'item_city_name' => $booking->properties->city_name,
                'item_host_id' => $booking->host_id,
                'price' => (float)number_format($booking->per_night, 2, '.', ''),
                'quantity' => $booking->total_night,
                'total_price' => (float)number_format($booking->total, 2, '.', ''),
            ]);

            $data['snapPixelData'] = json_encode([
                'price' => (float)number_format($booking->total, 2, '.', ''),
                'currency' => Session::get('currency'),
                'transaction_id' => $booking->transaction_id,
                'item_ids' => $booking->properties->property_code,
                'item_category' => 'Property',
                'number_items' => $booking->total_night,
                'user_email' => auth()->user()->email ?? null,
                'user_phone_number' => auth()->user()->phone
            ]);

            $data['tikPixelData'] = json_encode([
                'transaction_id' => $booking->transaction_id,
                'item_category' => 'Property',
                'number_items' => $booking->total_night,
                'email' => auth()->user()->email ?? null,
                'phone' => auth()->user()->phone,
                'contents' => [
                    [
                        'content_id' => $booking->properties->property_code,
                        'content_type' => 'product',
                        'content_name' => $booking->properties->name
                    ]
                ],
                'value' => (float)number_format($booking->total, 2, '.', ''),
                'currency' => Session::get('currency'),
            ]);
        }

        if ($this->helper->isRequestFromMobile($request)) {
            isset(auth()->user()->is_elm_verified) && auth()->user()->is_elm_verified ? $data['is_elm_document'] = 1 : $data['is_elm_document'] = 0;
            $view = $request->booking ? 'mobile.pages.after_payment' : 'mobile.pages.card_after_payment';
            return view($view, $data);
        } else {
            $view = $request->booking ? 'pages.after_payment' : 'pages.wallet_after_payment';
            return view($view, $data);
        }
    }

    function initiated(Request $request)
    {
        Log::debug('initiated', $request->all());
        if (!$request->booking && !$request->card && !$request->wallet) {
            abort(404);
        }
        $status = (new MoyasarService())->status($request->status);
        $by = $request->booking ? 'booking' : ($request->wallet ? 'wallet' : 'card');
        Log::info($request->wallet);
        Log::info($by);
        if ($by == 'wallet') {
            Log::info('--------------------------------');
            $url = route('paymentCallbackWallet', ['status' => $status ? '1' : '0', 'platform' => 'darent', 'type' => 'w']);
        } else {
            $url = route('paymentCallback', ['status' => $status ? '1' : '0', $by => $request->{$by}]);
        }
        return redirect($url);
    }

    // function hyperPayinitiated(Request $request)
    // {
    //     Log::debug('initiated', $request->all());
    //     if (!$request->booking && !$request->card && !$request->wallet) {
    //         abort(404);
    //     }
    //     $by = $request->booking ? 'booking' : ($request->wallet ? 'wallet' : 'card');

    //     // $status = (new HyperpayService())->getPaymentDetails($request->id);
    //     $status = (new HyperpaySdkService())->getDetails($request->id);


    //     Log::debug('status', [$status]);
    //     if ($by == 'wallet') {
    //         Log::info('--------------------------------');
    //         $url = route('paymentCallbackWallet', ['status' => $status ? '1' : '0', 'platform' => 'darent', 'type' => 'w']);
    //     } else {
    //         $url = route('paymentCallback', ['status' => $status ? '1' : '0', $by => $request->{$by}]);
    //     }
    //     return redirect($url);
    // }

    function hyperPayinitiated(Request $request)
    {
        // return $request;
        Log::debug('initiated', $request->all());

        if (!$request->booking && !$request->card && !$request->wallet) {
            abort(404);
        }

        $by = $request->booking ? 'booking' : ($request->wallet ? 'wallet' : 'card');

        try {
            // $status = (new HyperpayService())->getPaymentDetails($request->id);
            $status = (new HyperpaySdkService())->getDetails($request->id, $request->booking, $by);
            // return $status;

            Log::debug('status', [$status]);

            // dd($status);
        } catch (ValidationException $e) {
            Log::error('Validation error: ' . $e->getMessage(), ['code' => $e->getCode(), 'trace' => $e->getTrace()]);

            if ($request->wantsJson()) {
                return response()->json(['error' => $e->getMessage()], 422);
            } else {
                return redirect()->back()->withErrors(['error' => $e->getMessage()]);
            }
        } catch (BadRequestException $e) {
            Log::error('Bad request: ' . $e->getMessage(), ['code' => $e->getCode(), 'trace' => $e->getTrace()]);

            if ($request->wantsJson()) {
                return response()->json(['error' => $e->getMessage()], 400);
            } else {
                return redirect()->back()->withErrors(['error' => $e->getMessage()]);
            }
        } catch (Exception $e) {
            Log::error('Error: ' . $e->getMessage(), ['code' => $e->getCode(), 'trace' => $e->getTrace()]);

            if ($request->wantsJson()) {
                return response()->json(['error' => 'An unexpected error occurred. Please try again later.'], 500);
            } else {
                return redirect()->back()->withErrors(['error' => 'An unexpected error occurred. Please try again later.']);
            }
        }

        Log::debug('status_check', [$status]);

        // if($by == 'booking' && $status == 'success'){
        //     $booking = Bookings::where('id',$request->booking)->first();
        //      if($booking->properties->platform_id == PlatformTypeEnum::SILKHAUS){

        //      };
        // }

        if ($by == 'wallet') {
            Log::info('--------------------------------');
            $url = route('paymentCallbackWallet', ['status' => $status == 'success' ? '1' : '0', 'platform' => 'darent', 'type' => 'w']);
        } else {
            $url = route('paymentCallback', ['status' => $status == 'success' ? '1' : '0', $by => $request->{$by}]);
        }
        // return $url;

        return redirect($url);
    }


    function invoiceInitiated(Request $request)
    {
        $bookingid = $request->booking_id;
        Log::info("Invoice Success URL Started");
        if ($request->status == 'paid' && $request->invoice_id) {
            $booking = Bookings::with('users', 'properties')->find($bookingid);
            // $invoice_id = $request->id;
            $invoice_id = $request->invoice_id;
            if ($booking->status != 'Accepted') {
                $booking->update(['status' => "Accepted", "is_payment" => 1, 'payment_type_id' => PaymentTypeEnum::VISAMASTER]);
                BookingStatusLog::updateOrCreate(
                    ['booking_id' => $bookingid],
                    ['status' => "Accepted", 'changed_by' => $booking->user_id]
                );
            } else {
                Log::debug('Conditions', [$booking->status]);
            }
            Log::debug('$booking->status->invoice', [$booking->status]);
            if ($booking->status == 'Accepted') {
                $price_list = json_decode($this->helper->getPrice($booking->property_id, $booking->start_date, $booking->end_date, $booking->guest, booking_id: $booking->id));
                $propertyCurrencyCode = PropertyPrice::firstWhere('property_id', $booking->property_id)->currency_code;

                if (isset($price_list->date_with_price)) {
                    foreach ($price_list->date_with_price as $dp) {
                        $tmp_date = setDateForDb($dp->date);
                        $property_id = $booking->property_id;
                        $property_data = [
                            'property_id' => $property_id,
                            'booking_id' => $booking->id,
                            'status' => 'Not available',
                            'price' => 0,
                            'date' => $tmp_date
                        ];
                        $insertedDates = PropertyDates::create($property_data);
                    }
                }

                $is_elm_verified = ElmDocument::where('user_id', $booking->user_id)->where('verified_till', '>=', Carbon::now()->format('Y-m-d'))->first();
                $updateYaqeen = Bookings::where('id', $booking->id)->first();
                if (isset($is_elm_verified)) {
                    $updateYaqeen->is_yaqeen_verified = '1';
                } else {
                    $updateYaqeen->is_yaqeen_verified = '0';
                }
                $updateYaqeen->save();
                $this->helper->createQuotationRequest($booking);
                $this->helper->getPriceTawuniya($booking);
                Log::debug('getPriceTawuniya-reached', ['booking' => $bookingid]);

                // Track booking_completed event across all platforms
                \App\Jobs\EventTrackingJob::trackBookingCompleted(
                    $booking->id,
                    $booking->user_id,
                    $booking->host_id,
                    $booking->property_id,
                    $booking->start_date,
                    $booking->end_date,
                    'Success',
                    $bookingdetails['guestpaid']
                );

                (User::find($booking->user_id))->notify(new UserNotify(
                    'guest.booking.accepted.guest',
                    route('guest_reservation_detail', ['code' => $booking->code]),
                    data: ['slug' => 'reservation', 'tab' => '0', 'booking_id' => $booking->code]
                ));

                $hosts = User::select('users.*')->join('properties AS ps', 'ps.host_id', 'users.id')
                    ->leftJoin('properties_cohosts AS pcs', fn($q) => $q->on('pcs.co_host_id', 'users.id')->whereNull('pcs.deleted_at'))
                    ->where('ps.id', $booking->property_id)->distinct()->get();
                Notification::send($hosts, new UserNotify(
                    'guest.booking.accepted.host',
                    url('managehost/all_reservation?tab=coming&id=' . $booking->id . '&query_code=' . $booking->code ),
                    data: [
                        'slug' => 'booking',
                        'tab' => 'upcoming-bookings'
                    ]
                ));
            }

            if (PaymentTypeEnum::fromString('credit card') == PaymentTypeEnum::VISAMASTER) {

                $user_unverify_card_exist = UserCards::where(['is_verified' => 0, 'user_id' => $booking->user_id])
                    ->orderby('created_at', 'desc')
                    ->first();
                if ($user_unverify_card_exist) {
                    UserCards::where(
                        'id',
                        $user_unverify_card_exist->id
                    )
                        ->update(['is_verified' => 1, 'is_default' => 1]);
                }
            }

            $cus_userid = $booking->user_id; //added

            // Ledger entry
            //CREDIT
            $transaction_data = [
                'payment_type_id' => PaymentTypeEnum::VISAMASTER,
                'transaction_type_id' => TransactionTypes::CREDIT, //CREDIT
                'transaction_category_id' => TransactionCategories::BOOKING_CONFIRMATION, //BOOKING
                'user_id' => $cus_userid,
                'amount' => $booking->total,
                'ref_json' => json_encode(['booking_id' => $bookingid, 'invoice_id' => $invoice_id]),
                'booking_id' => $booking->id,
            ];
            Transactions::create($transaction_data);

            //debit
            $transaction_data = [
                'payment_type_id' => PaymentTypeEnum::Wallet,
                'transaction_type_id' => TransactionTypes::DEBIT, //DEBIT
                'transaction_category_id' => TransactionCategories::BOOKING_CONFIRMATION, //BOOKING
                'user_id' => $cus_userid,
                'amount' => $booking->total,
                'ref_json' => json_encode(['booking_id' => $bookingid, 'invoice_id' => $invoice_id]),
                'booking_id' => $booking->id
            ];

            Transactions::create($transaction_data);

            $bookingdetails = $this->helper->payableToHost($booking);

            $promo_host_discount = 0;
            $promo_darent_discount = 0;

            BookingPaymentDetails::firstOrCreate([
                'booking_id' => $bookingid
            ], [
                'guest_id' => $booking->user_id,
                'host_id' => $bookingdetails['hostid'],
                'payment_key' => 'PaymentId',
                'payment_value' => $request->id,
                'guest_paid' => $bookingdetails['guestpaid'],
                'vat' => $bookingdetails['VAT'],
                'commission' => $bookingdetails['commission'],
                'commission_in_percenatge' => $bookingdetails['commission_in_percenatge'],
                'insurance_fees_in_percentage' => $bookingdetails['insurance_fees_percentage'],
                'security_deposit' => $bookingdetails['security_deposit'],
                'host_pay' => $bookingdetails['host_pay'],
                'host_discount' => $promo_host_discount,
                'darent_discount' => $promo_darent_discount,
            ]);

            $data['paymentstatus'] = 1;
        } else {
            $data['paymentstatus'] = 0;
        }

        if ($this->helper->isRequestFromMobile($request)) {
            $view = 'mobile.pages.wallet_after_payment';
            return view($view, $data);
        } else {
            $view = 'pages.wallet_after_payment';
            return view($view, $data);
        }
    }

    public function paymentApple(Request $request, $booking_id)
    {
        dd($request->all());
        $booking = Bookings::with('host')->find($booking_id);
        $customer_ref = $this->helper->randomCode(3) . "-B-$booking->id";
        $finalamount = number_format((config('app.env') != 'local' ? ($booking->total_with_discount ?: $booking->total) : 1), 2, '.', '');
        $postFields = [
            "SessionId" => $request->sessionId,
            "CustomerName" => auth()->user()->FullName,
            "DisplayCurrencyIso" => "SAR",
            "MobileCountryCode" => "+966",
            "CustomerEmail" => isset(auth()->user()->email) ? auth()->user()->email : '<EMAIL>',
            "InvoiceValue" => $finalamount,
            "CallBackUrl" => route('paymentCallback', ['status' => 1, 'booking_id' => $booking_id]),
            "ErrorUrl" => route('paymentCallback', ['status' => 0, 'booking_id' => $booking_id]),
            "Language" => "en",
            "CustomerReference" => $customer_ref,
            "CustomerCivilId" => auth()->user()->id,
            "InvoiceItems" => [
                [
                    "ItemName" => "Property Reservation ",
                    "Quantity" => 1,
                    "UnitPrice" => $finalamount
                ]
            ]
        ];

        $curl_res = $this->helper->callAPI($this->myfatoorah_apiURL . 'ExecutePayment', $this->myfatoorah_apikey, $postFields);
        $json = ($curl_res->PaymentURL);
        $hosts = User::select('users.*')->join('properties AS ps', 'ps.host_id', 'users.id')
            ->leftJoin('properties_cohosts AS pcs', fn($q) => $q->on('pcs.co_host_id', 'users.id')->whereNull('pcs.deleted_at'))
            ->where('ps.id', $booking->property_id)->distinct()->get();
        Notification::send($hosts, new UserNotify(
            'guest.booking.payment.click.host',
            route('managehost.all_reservation'),
            data: ['slug' => 'booking', 'tab' => 'upcoming-bookings']
        ));
        // $directpayment_res  = $this->helper->callAPI($json->Data->PaymentURL, $this->myfatoorah_apikey, $postFields = [], $requestType = 'GET');
        // Log::info($directpayment_res);
        return response()->json(['message' => 'Success', 'data' => $json], 200);
    }


    // public function InsertPropertyDate(){
    //     foreach(Session::get('pricedata') as $pricedata){
    //         foreach($pricedata as $datefromprice){
    //             $tmp_date = setDateForDb($datefromprice->date);
    //             $recordinserted =  PropertyDates::updateOrCreate(['property_id' => Session::get('propertydatedata')['property_id'], 'date' => $tmp_date], Session::get('propertydatedata'));
    //         }
    //     // $price = $this->helper->convert_currency($data['price_list']->currency, $propertyCurrencyCode, $dp->original_price);

    //     }

    // }

    public function ProcessPayment()
    {

        $data = session()->get('hyperpayresponce');
        $scriptUrl = $this->scriptUrl;

        //  if($data['type'] == "APPLEPAY"){

        //     // return $data;
        //     return view('check',compact('data','scriptUrl'));

        //  }
        return view('check', compact('data', 'scriptUrl'));
    }

    public function MobileProcessPayment(Request $request)
    {

        $url = $request->query('url');
        $checkoutId = $request->query('checkoutId');
        $type = $request->query('type');
        $scriptUrl = $this->scriptUrl;
        return view('mobile_payment', compact('url', 'checkoutId', 'type', 'scriptUrl'));
        // return "Processing payment for URL: $url, Checkout ID: $checkoutId";
    }

    public function cancel(Request $request)
    {
        // $this->helper->one_time_message('success', trans('messages.error.payment_process_error'));
        // return redirect('payments/book/'.Session::get('payment_property_id'));
        dd($request->all());
    }


    function postSummary(Request $request)
    {
        $token = Str::uuid();
        $encrypted = encrypt($request->all());

        Session::put("summary_{$token}", $encrypted);
        $encrypted = encrypt($request->all());
        $lang = Session::get('language') ?? 'en';

        return redirect()
            ->to(route('payment.summary.getUrl', ['token' => $token, 'lang' => $lang]));
    }
    public function getSummary(Request $request)
    {
        $token = $request->token;
        $encrypted = Session::get("summary_{$token}");
        if (!$encrypted) {
            abort(404, 'Session data not found or expired.');
        }

        $decrypted = decrypt($encrypted);

        $lang = $request->lang ?? 'en';
        $checkin = Carbon::parse($decrypted['url_checkin']) ?? now();
        $checkout = Carbon::parse($decrypted['url_checkout']) ?? now();
        $properties_pricing = $this->helper->getPropertiesPricing($checkin->format('Y-m-d'), $checkout->format('Y-m-d'));
        $propertyFees = PropertyFees::pluck('value', 'field');
        $guest_service_charge = $propertyFees['guest_service_charge'];
        $user = Auth::user();

        $result = Properties::query()
            ->select([
                'properties.*',
                'properties_pricing.day_price as day_price',
                'properties_pricing.total_price as total_price',
                'properties_pricing.number_of_days as number_of_days',
                'properties_pricing.before_discount as before_discount',
            ])
            ->joinSub($properties_pricing, 'properties_pricing', function (JoinClause $join) {
                $join->on('properties.id', '=', 'properties_pricing.property_id');
            })
            ->where(function ($query) use ($decrypted) {
                $query->where('properties.id', $decrypted['property_id']);
            })
            ->with(['propertyType', 'space', 'property_price'])
            ->withCount('userPropertyView')
            ->where('status', 'Listed')
            ->where('visibility', 1)
            ->first();
        $booked_dates = PropertyDates::where('property_id', $result->id)
            ->where('status', 'Not available')
            ->select('date')
            ->union(
                CustomPricing::where('property_id', $result->id)
                    ->where('status', 'Not available')
                    ->select('date')
            )
            ->get();
        $grid_property =  GridProperty::where('id', $result->id)->first();
        $property_photos = PropertyPhotos::where('property_id', $result->id)->orderBy('serial', 'asc')->get();
        $user_wallet = NewUserWallet::where('user_id', auth()->id())->first();

        $userAgent = $request->header('User-Agent');
        $showPayOpts = strpos($userAgent, 'Safari') == true && strpos($userAgent, 'Chrome') == false && (strpos($userAgent, 'Macintosh') == true || strpos($userAgent, 'iPhone') == true);


        $data = Compact('token', 'decrypted', 'result', 'guest_service_charge', 'booked_dates', 'grid_property', 'property_photos', 'user_wallet', 'showPayOpts', 'user', 'lang');
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.payment.summary', $data);
        }

        return view('payment.summary', $data);
    }
}
