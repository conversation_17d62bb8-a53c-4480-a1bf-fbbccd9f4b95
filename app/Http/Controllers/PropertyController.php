<?php

namespace App\Http\Controllers;

use App\Enums\PropertyInquiryStatusTypeEnum;
use App\Helpers\ImageHelper;
use DB;
use PDF;
use Session;
use GoogleTranslate;
use App\Exports\ReservationExport;
use App\Http\Helpers\Common;
use App\Http\Resources\HostListingResource;
use App\Http\Resources\YourReservationResource;
use App\Http\Services\MTTourismService;
use App\Models\{
    Admin,
    Amenities,
    AmenityType,
    Bank,
    BedType,
    Bookings,
    City,
    Country,
    Currency,
    CustomPricing,
    District,
    ElmDocument,
    Favourite,
    GridProperty,
    HostLicense,
    HostReferalCode,
    LocalizationKeyword,
    NewUserWallet,
    PhotosTemp,
    Properties,
    PropertiesTemp,
    PropertyAddress,
    PropertyDates,
    PropertyDescription,
    PropertyDetails,
    PropertyFees,
    PropertyPhotos,
    PropertyPrice,
    PropertySteps,
    PropertyType,
    Reviews,
    ScoringSystem,
    Settings,
    SpaceType,
    User
};
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session as FacadesSession;
use Illuminate\Support\Facades\Validator as FacadesValidator;
use Illuminate\Support\Facades\Cache;
use Image;
use Intervention\Image\Exception\NotFoundException;
use Maatwebsite\Excel\Facades\Excel;
use Throwable;
use Validator;
use ZipArchive;

class PropertyController extends Controller
{
    public $helper;
    protected $mtTourismService;

    public function __construct(MTTourismService $mtTourismService)
    {
        $this->helper = new Common;
        $this->mtTourismService = $mtTourismService;
    }

    public function imageopt(Request $request)
    {
        $count = 0;
        $internal_count = 100;

        $folders = glob('images/property/*');


        // $dh = opendir($dir);

        $start_folder = $i = $request->start;
        $end_folder = $request->end;

        for ($start_folder; $i <= $end_folder; $i++) {
            $folder_path = 'images/property/' . $i;

            // echo "filename: $file : filetype: " . filetype($file) . "<br />";

            // echo $folder_path. "<br />";

            if (is_dir($folder_path) === true) {

                $dh = opendir($folder_path);
                $limit = $request->limit;

                while (($file = readdir($dh)) !== false) {
                    // dump($file);

                    if (preg_match('/\.(jpg|jpeg|png|gif)$/i', $file)) {
                        $path = $folder_path . '/' . $file;
                        dump($path . ' SIZE ==>' . filesize($path));

                        // $imgFile = Image::make($request->file->getRealPath());
                        $imgFile = Image::make($path)->orientate();
                        // dd($imgFile);
                        $imgFile->resize(null, 350, function ($constraint) {
                            $constraint->aspectRatio();
                            $constraint->upsize();
                        });
                        // $imgFile->orientate();
                        $imgFile->save($path);
                    }

                    // if($count == $internal_count){
                    //     dump($count);
                    //     $internal_count += 100;
                    // }
                    $count++;
                }
            }
        }


        dd('Script END', 'Done ' . $count, 'Total Folders ' . count(glob('images/property/*')));
    }

    public function imageopt2()
    {

        // $folders = glob('images/starting_cities/*');
        $folders = 'images/starting_cities/';

        // $dh = opendir($dir);

        // foreach($folders as $folder_path) {
        // echo "filename: $file : filetype: " . filetype($file) . "<br />";

        // echo $folder_path. "<br />";
        $count = 0;
        if (is_dir($folders) === true) {

            $dh = opendir($folders);
            while (($file = readdir($dh)) !== false) {

                if (preg_match('/\.(jpg|jpeg|png|gif)$/i', $file)) {
                    $path = $folders . '/' . $file;
                    // dump($path . ' SIZE ==>' . filesize($path));

                    // $imgFile = Image::make($request->file->getRealPath());
                    $imgFile = Image::make($path);
                    // dd($imgFile);
                    $imgFile->resize(null, 450, function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    });
                    $imgFile->orientate();
                    $imgFile->save($path);
                }

                $count++;
            }
        }
        // }


        dd('Script END', 'Count  | ' . $count);
    }


    public function becomeHost(Request $request)
    {
        $data['title'] = 'Become a Host';
        $data['properties'] = Properties::recommendedForHome();

        $isMobile = $this->helper->isRequestFromMobile($request);
        $viewPrefix = $isMobile ? 'mobile.' : '';

        if ($request->filled('create') && $request->create == true) {
                return redirect()->route('propertyLandingPage');
        }
        if (Auth::check()) {
            $data['hostInProgressListing'] = Common::getHostInProgressListing(Auth::user()->id);

            if ($data['hostInProgressListing']->isNotEmpty()) {
                return view("{$viewPrefix}listing.duplicate_limitation", $data);
            }
        }

            return redirect()->route('propertyLandingPage');
    }

    public function userProperties(Request $request)
    {
        switch ($request->status) {
            case 'Listed':
            case 'Unlisted':
                $pram = [['status', '=', $request->status]];
                break;
            default:
                $pram = [];
                break;
        }

        $data['status'] = $request->status;
        $data['properties'] = Properties::with('property_price', 'property_address')
            ->where('host_id', Auth::id())
            ->where($pram)
            ->orderBy('id', 'desc')
            ->paginate(Session::get('row_per_page'));
        $data['currentCurrency'] = $this->helper->getCurrentCurrency();

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.property.listings', $data);
        } else {
            return view('property.listings', $data);
        }
        return view('property.listings', $data);
    }

    public function StartHosting(Request $request)
    {
        $data['title'] = "Start Hosting";

        $viewPrefix = $this->helper->isRequestFromMobile($request) ? 'mobile.listing.' : 'listing.';

        if ($request->filled('create') && $request->create == true) {
                return redirect()->route('propertyLandingPage');
        }
        if (Auth::check()) {
            $data['hostInProgressListing'] = Common::getHostInProgressListing(Auth::user()->id);

            if ($data['hostInProgressListing']->isNotEmpty()) {
                return view($viewPrefix . 'duplicate_limitation', $data);
            }

        }
            return redirect()->route('propertyLandingPage');
    }


    public function preferred_method(Request $request)
    {
        $data['title'] = "preferred method";
        return view('listing.preferred_method', $data);
    }

    public function listing_iqama(Request $request)
    {
        $data['title'] = "Iqama";
        return view('listing.listing_iqama', $data);
    }

    public function addPlace(Request $request)
    {
        if ($request->isMethod('post')) {
            $rules = [
                'property_type_id' => 'required',
            ];

            $validator = Validator::make($request->all(), $rules, [
                'property_type_id.required' => __('validation.property_type_id.required'),
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            } else {
                $property_id = $request->id ?? null;
                if ($property_id) {
                    // Update existing property
                    $property = Properties::find($property_id);
                    if (!$property) {
                        return redirect()->back()->with('error', 'Property not found.');
                    }
                } else {
                    // Create new property
                    $property = new Properties;
                    $property->host_id = Auth::id();
                }
                // $property = new Properties;
                // $property->host_id = Auth::id();     //Auth::id();
                // $property->name            = SpaceType::getAll()->find($request->space_type)->name.' in '.$request->city;
                $property->property_type = $request->property_type_id;
                $property->space_type = isset($request->space_type) ? $request->space_type : NULL;
                $property->accommodates = isset($request->accommodates) ? $request->accommodates : 0;
                $property->save();
                if (!$property_id) {
                    // Only create related models if it's a new property


                    $property_address = new PropertyAddress;
                    $property_address->property_id = $property->id;
                    $property_address->save();

                    $property_price = new PropertyPrice;
                    $property_price->property_id = $property->id;
                    $property_price->currency_code = Session::get('currency');
                    $property_price->save();

                    $property_steps = new PropertySteps;
                    $property_steps->property_id = $property->id;
                    $property_steps->save();

                    $property_description = new PropertyDescription;
                    $property_description->property_id = $property->id;
                    $property_description->save();
                }

                return redirect('listing/' . $property->id . '/spacetype');
            }
        }
        $data['title'] = 'Add Place';
        $data['property_type'] = PropertyType::where('status', 'Active')->limit(5)->get();
        // $data['space_type']    = SpaceType::getAll()->where('status', 'Active')->pluck('name', 'id');
        if ($this->helper->isRequestFromMobile($request)) {
            return view('listing.addplace', $data);
        } else {
            return view('listing.addplace', $data);
        }
        return view('listing.addplace', $data);
    }

    function replace_extension($filename, $new_extension)
    {
        $info = pathinfo($filename);
        return $info['filename'] . '.' . $new_extension;
    }

    public function getimages($property_id)
    {
        $images = PropertyPhotos::orderBy('serial', 'desc')->where('property_id', $property_id)->get();
        return response()->json(['images' => $images, 'success' => true], 200);
    }

    public function AddPropertyWithSteps(Request $request)
    {
        // $step            = $request->step;
        $property_id = $request->id;
        $step = match ($request->step) {
            'basics' => 'basic',
            'pricing' => 'price',
            'booking' => 'question',
            default => $request->step,
        };

        $data['step'] = $step;
        // Auth::id()
        $data['result'] = $property = Properties::with('property_address', 'property_photos')->where('host_id', Auth::id())->findOrFail($property_id);
        $data['is_elm_verified'] = $is_elm_verified = !!ElmDocument::where('user_id', auth()->id())->where('verified_till', '>=', Carbon::now()->format('Y-m-d'))->first();

        $futureBooking = 0;

        if (isset($request->edit)) {
            if (count($property->bookings)) {
                foreach ($property->bookings as $b) {
                    $b->start_date >= date('Y-m-d') ? $futureBooking++ : '';
                }
            }
            if ($futureBooking) {
                abort(401);
            }
        }

        $data['details'] = PropertyDetails::pluck('value', 'field');
        $data['missed'] = PropertySteps::where('property_id', $request->id)->first();
        $data['special_days'] = Country::orderBy('id')->first()->special_days;

        if ($step == "spacetype") {
            if ($request->isMethod('post')) {
                $rules = [
                    'no_of_appartment' => 'required',
                ];
                if (isset($request->space_type) && $request->space_type == "Room") {
                    $rules['space_type'] = ''; // No rule for 'space_type' if it's "Room"
                } else {
                    $rules['space_type'] = 'required'; // Apply 'required' rule for 'space_type'
                }

                $validator = Validator::make($request->all(), $rules, [
                    'space_type.required' => __('validation.space_type.required'),
                    'no_of_appartment.required' => __('validation.no_of_appartment.required'),
                ]);
                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput();
                } else {
                    $property = Properties::find($property_id);
                    $property->space_type = $request->space_type;
                    $property->no_of_appartment = $request->no_of_appartment;
                    $property->save();
                    $this->helper->getLogs($property, 'web');

                    $this->propertyStep($property_id, 'spacetype');
                    if ($request->purpose == 'saveAndExit') {
                        return redirect()->route('managehost.host_listings');
                    }
                    return redirect('listing/' . $property_id . '/location');
                }
            }
            // $data['space_type']    = SpaceType::where('status', 'Active')->get();
            $data['space_type'] = SpaceType::where('property_type', $property->property_type)->where('status', 'Active')->orderBy('priority', 'desc')->get();
            // dd($data['space_type']);
        } elseif ($step == "location") {
            // $property = Properties::find($property_id);
            // dd($property->steps_completed);
            if ($request->isMethod('Post')) {

                // dd($request->all());

                $rules = [
                    'address_line_1' => 'required|max:250',
                    'country' => 'required',
                    'city' => 'required',
                    // 'state'             => 'required',
                    'latitude' => 'required|not_in:0',
                ];

                $fieldNames = [
                    'address_line_1' => 'Address',
                ];

                $messages = [
                    'not_in' => 'Please set :attribute pointer',
                    'address_line_1' => 'Please Select Location',

                ];
                $validator = Validator::make($request->all(), $rules, [
                    'address_line_1.required' => __('validation.address_line_1.required'),
                    'country.required' => __('validation.country.required'),
                    'city.required' => __('validation.city.required'),
                    'latitude.required' => __('validation.latitude.required'),
                    'latitude.not_in' => __('validation.latitude.not_in'),
                ]);
                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput();
                } else {
                    $property_address = PropertyAddress::where('property_id', $property_id)->first();
                    // $minLat =  $request->latitude - $property_address::LAT_LONG_INCREMENT;
                    // $maxLat =  $request->latitude + $property_address::LAT_LONG_INCREMENT;
                    // $minLong = $request->longitude - $property_address::LAT_LONG_INCREMENT;
                    // $maxLong = $request->longitude + $property_address::LAT_LONG_INCREMENT;
                    // $cityID = City::whereRaw('latitude between ' . $minLat . ' and ' . $maxLat . ' and longitude between ' . $minLong . ' and ' . $maxLong)->first();
                    $property_address->address_line_1 = $request->address_line_1;
                    $property_address->address_line_2 = isset($request->address_line_2) ? $request->address_line_2 : NULL;
                    $property_address->latitude = $request->latitude;
                    $property_address->longitude = $request->longitude;
                    $property_address->city = $request->city;
                    // $property_address->city_id         = $cityID->id;
                    $property_address->district = $request->district;
                    $property_address->state = $request->state;
                    $property_address->country = $request->country;
                    $property_address->postal_code = $request->postal_code;
                    $property_address->save();
                    $this->helper->getLogs($property_address, 'web');
                    $this->propertyStep($property_id, 'location');

                    if ($request->purpose == 'saveAndExit') {
                        return redirect()->route('managehost.host_listings');
                    }
                    return redirect('listing/' . $property_id . '/confirmLocation');
                }
            }
        } elseif ($step == "confirmLocation") {
            if ($request->isMethod('Post')) {
                $rules = [
                    'address_line_1' => 'required|max:250',
                    'address_line_2' => 'max:250',
                    'country' => 'required',
                    'city' => 'required',
                    'state' => 'max:50',
                    'postal_code' => 'max:10',
                ];

                $fieldNames = [
                    'address_line_1' => 'Address',
                ];

                $messages = [
                    'not_in' => 'Please set :attribute pointer',
                    'address_line_1' => 'Please Select Location',

                ];
                $validator = Validator::make($request->all(), $rules, [
                    'address_line_1.required' => __('validation.address_line_1.required'),
                    'address_line_1.max' => __('validation.address_line_1.max'),
                    'address_line_2.max' => __('validation.address_line_2.max'),
                    'country.required' => __('validation.country.required'),
                    'city.required' => __('validation.city.required'),
                    'state.max' => __('validation.state.max'),
                    'postal_code.max' => __('validation.postal_code.max'),
                ]);
                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput();
                } else {
                    // $property = Properties::find($property_id);
                    // if ($property->property_type == 27) {
                    //     $property->name = 'Room in ' . $request->city;
                    // } else {
                    //     $property->name = SpaceType::getAll()->find($property->space_type)->name . ' in ' . $request->city;
                    // }
                    // $property->save();
                    // $this->helper->getLogs($property, 'web');
                    $city = City::find($request->city);
                    $district = District::find($request->district);

                    $property_address = PropertyAddress::where('property_id', $property_id)->first();
                    $property_address->address_line_1 = $request->address_line_1;
                    $property_address->address_line_2 = $request->address_line_2;
                    $property_address->state = $request->state;
                    $property_address->city = $city->name;
                    $property_address->city_ar = $city->name_ar;
                    $property_address->district = $district->name;
                    $property_address->district_ar = $district->name_ar;
                    $property_address->city_id = $request->city;
                    $property_address->district_id = $request->district;
                    $property_address->postal_code = $request->postal_code;
                    $property_address->save();
                    $this->helper->getLogs($property_address, 'web');


                    $this->propertyStep($property_id, 'confirmLocation');

                    if ($request->purpose == 'saveAndExit') {
                        return redirect()->route('managehost.host_listings');
                    }
                    return redirect('listing/' . $property_id . '/numberofRoom');
                }
            }
            $data['country'] = Country::pluck('name', 'short_name');
            $data['cities'] = City::all();
        } elseif ($step == "numberofRoom") {
            if ($request->isMethod('Post')) {
                $rules = [
                    'bedroom' => 'required|numeric|min:0',
                    // 'beds'       => 'required_if:bedroom,>0|numeric|min:1',
                    'beds' => [
                        'required_if:bedroom,>0',
                        'numeric',
                        function ($attribute, $value, $fail) {
                            if (request('bedroom') > 0 && $value < 1) {
                                if (app()->getLocale() == "ar") {

                                    $fail("اختر سريرًا واحدًا على الأقل عندما تكون غرفة النوم أكبر من 0.");
                                } else {
                                    $fail("Select at least 1 bed when the bedroom is greater than 0.");
                                }
                            }
                        },
                    ],
                    'bathroom' => 'required',
                ];
                $validator = Validator::make($request->all(), $rules, [
                    'bedroom.required' => __('validation.bedroom.required'),
                    'bedroom.numeric' => __('validation.bedroom.numeric'),
                    'bedroom.min' => __('validation.bedroom.min'),
                    'beds.required' => __('validation.beds.required'),
                    'beds.numeric' => __('validation.beds.numeric'),
                    'bathroom.required' => __('validation.bathroom.required'),
                ]);
                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput();
                } else {
                    $propertyamenities = Properties::find($request->id);
                    $propertyamenities->bedrooms = $request->bedroom;

                    $propertyamenities->single_beds = $request->bedroom ? $request->single_beds : 0;
                    $propertyamenities->double_beds = $request->bedroom ? $request->double_beds : 0;

                    $propertyamenities->beds = $request->bedroom ? $request->beds : 0;
                    $propertyamenities->bathrooms = $request->bathroom;
                    $propertyamenities->save();
                    $this->helper->getLogs($propertyamenities, 'web');

                    $this->propertyStep($property_id, 'numberofRoom');

                    if ($request->purpose == 'saveAndExit') {
                        return redirect()->route('managehost.host_listings');
                    }
                    return redirect()->route('stepTwo', ['property_id' => $property_id]);
                }
            }
        } elseif ($step == "amenities") {
            if ($request->isMethod('Post')) {
                $rules = [
                    'amenities' => 'required',
                ];
                $validator = Validator::make($request->all(), $rules, [
                    'amenities.required' => __('validation.amenities.required'),

                ]);
                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput();
                } else {
                    $custom_amenities = $request->input('custom_amenities');
                    if ($custom_amenities) {
                        Properties::storeCustomAmenities($request->id, explode(",", $custom_amenities));
                    }

                    $propertyamenities = Properties::find($request->id);
                    $propertyamenities->amenities = implode(',', $request->amenities);
                    $propertyamenities->save();
                    $this->helper->getLogs($propertyamenities, 'web');

                    $this->propertyStep($property_id, 'amenities');

                    if ($request->purpose == 'saveAndExit') {
                        return redirect()->route('managehost.host_listings');
                    }
                    return redirect('listing/' . $property_id . '/photos');
                }
            }
            $data['property_amenities'] = explode(',', $data['result']->amenities);
            $data['common_amenities'] = Amenities::where('status', 'Active')->where('type_id', 1)->get();
            $data['safety_amenities'] = Amenities::where('status', 'Active')->where('type_id', 2)->get();
            $data['house_rules'] = Amenities::where('status', 'Active')->where('type_id', 3)->get();
            $data['property'] = Properties::find($request->id);
            $data['property']['custom_amenities'] = $data['property']->custom_amenities ? json_decode($data['property']->custom_amenities) : [];
        } elseif ($step == "photos") {
            if ($request->isMethod('post')) {
                $validate = Validator::make(
                    $request->all(),
                    [
                        'file' => 'required|max:1000', // 1 MB max
                        'file.*' => 'mimes:jpg,jpeg,bmp,png,gif,JPG',
                    ],
                    [
                        'file.required' => __('validation.file.required'),
                        'file.max' => __('validation.file.max'),
                        'file.mimes' => __('validation.file.mimes'),
                    ]
                );

                if ($validate->fails()) {
                    // return back()->withErrors($validate)->withInput();
                    return 'error';
                }

                if (!$request->hasFile('file')) return response()->json(['success' => false, 'data' => null]);

                // $temp = $_FILES['file']['tmp_name'];
                // $target = dirname(__FILE__) . $path;
                // $finalFile =  $target . $_FILES['file']['name'];
                $imgFile = Image::make($_FILES["file"]["tmp_name"]);
                $imgFile->resize(null, 400, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });

                $image_url = ImageHelper::upload($imgFile, 'property/' . $property->id);
                //CHECK IF EXIST
                $exist = PropertyPhotos::orderBy('serial', 'desc')
                    ->select('serial')
                    ->where('property_id', $property_id)
                    ->take(1)->first();

                $photos = new PropertyPhotos;
                $photos->property_id = $property_id;
                $photos->photo = $image_url;

                if ($exist) {
                    // IF PHOTO EXISTS SERIAL PLUS ONE
                    $photos->serial = $exist->serial + 1;
                    $photos->cover_photo = 0;
                } else {
                    //IF NO PHOTOS EXISTS
                    $photos->serial = 1;
                    $photos->cover_photo = 1;
                }

                $photos->save();
                $this->helper->getLogs($photos, 'web');

                $data = [
                    'id' => $photos->id,
                    'name' => $photos->photo,
                ];
                return response()->json(['success' => true, 'data' => $data]);
            }
            $data['cover_photo'] = PropertyPhotos::where('property_id', $property_id)->where('cover_photo', 1)->get();
            $data['photos'] = PropertyPhotos::where('property_id', $property_id)->where('cover_photo', 0)
                ->orderBy('serial', 'asc')
                ->get();
            $photosCount = count($data['cover_photo']) + count($data['photos']);

            if ($photosCount >= 4 && $photosCount <= 20) {
                session()->put("propertyimageuploaded", "1");
            } else {
                session()->put("propertyimageuploaded", "0");
            }
        } elseif ($step == "photosUploading") {
            $photos = PropertyPhotos::where('property_id', $request->id)->count();
            if (!$photos) {
                $photoErrorMessage = "";
                if (app()->getLocale() == "ar") {

                    $photoErrorMessage = "الصور المطلوبة";
                } else {
                    $photoErrorMessage = "photos required";
                }
                return redirect()
                    ->route('listingwithsteps', ['id' => $request->id, 'step' => 'photos'])
                    ->withErrors(['photosRequired' => $photoErrorMessage]);
            }
            if ($photos < 5) {
                $photoMinMessage = "";
                if (app()->getLocale() == "ar") {

                    $photoMinMessage = "يرجى تحميل الصور على الأقل 5";
                } else {
                    $photoMinMessage = "Please upload photos minimum 5";
                }
                return redirect()
                    ->route('listingwithsteps', ['id' => $request->id, 'step' => 'photos'])
                    ->withErrors(['photosRequired' => $photoMinMessage]);
            }
            $this->propertyStep($property_id, 'photos');


            if ($request->purpose == 'saveAndExit') {
                return redirect()->route('managehost.host_listings');
            }
        } elseif ($step == "setCover") {
            if ($request->isMethod('post')) {
                $dragImages = $request->input('dragImages');

                foreach ($dragImages as $index => $dragImage) {

                    $record = PropertyPhotos::where('id', $dragImage)
                        ->where('property_id', $property_id)
                        ->first();

                    if ($record) {
                        $record->update(['serial' => $index + 1]);
                    }
                    $this->helper->getLogs($record, 'web');
                }
                $this->propertyStep($property_id, 'setCover');

                if ($request->purpose == 'saveAndExit') {
                    return redirect()->route('managehost.host_listings');
                }

                return redirect('listing/' . $property_id . '/title');
            }
        } elseif ($step == "title") {
            if ($request->isMethod('post')) {

                // $validate = Validator::make($request->all(), [
                //     'title' => 'required',
                //     'string',
                //     'regex:/^[a-zA-Z\s]+$/' ,
                //     'name_ar' => 'required',
                //     'string',
                //     'regex:/^[\p{Arabic}\s]+$/u', // Improved regex for Arabic validation

                // ], [
                //     'title.required' => __('validation.title.required'),
                //     'name_ar.required' => __('validation.name_ar.required'),

                // ]);

                $validate = Validator::make($request->all(), [
                    'title' => ['required', 'string', 'regex:/^[a-zA-Z0-9.,\/\-\s]+$/'],
                    'name_ar' => ['required', 'string', 'regex:/^[\p{Arabic}0-9\s,\/\-.]+$/u'],
                ], [
                    'title.required' => __('validation.title.required'),
                    'title.regex' => __('validation.title.regex'),
                    'name_ar.required' => __('validation.name_ar.required'),
                    'name_ar.regex' => __('validation.name_ar.regex'),
                ]);


                if ($validate->fails()) {
                    return back()->withErrors($validate)->withInput();
                    // return response()->json(['errors' => $validate->errors()], 422);
                } else {
                    $property = Properties::find($property_id);
                    $property->name = $request->title;
                    $property->name_ar = $request->name_ar;
                    $property->slug = $this->helper->pretty_url($request->title, $property->property_code);
                    $property->save();
                    $this->helper->getLogs($property, 'web');

                    if ($property) {

                        $stepSave = $this->propertyStep($property_id, 'title');
                        if ($stepSave) {
                            if ($request->purpose == 'saveAndExit') {
                                return redirect()->route('managehost.host_listings');
                            }
                            return redirect('listing/' . $property_id . '/description')->with('success', 'File Uploaded Successfully!');
                        } else {
                            return back()->withInput();
                        }
                    } else {
                        return back()->withInput();
                    }
                }
            }
            $data['property_title'] = Properties::where('id', $property_id)->first();
        } elseif ($step == "description") {
            if ($request->isMethod('post')) {
                // dd($request->all());

                Log::debug("message", [$request->all()]);

                $rules = [
                    'summary' => ['required', 'max:1000', 'min:20', 'regex:/^[a-zA-Z0-9.,\/\-\s!?;:\'"\(\)]+$/'],
                    'summary_ar' => ['required', 'max:1000', 'min:20', 'regex:/^[\p{Arabic}0-9\s,\/\-.!?;:\'"\(\)]+$/u'],
                ];

                $fieldNames = [
                    'summary' => 'Description',
                    'summary_ar' => 'Description in arabic',
                ];

                $validator = Validator::make($request->all(), $rules, [
                    'summary.required' => __('validation.summary.required'),
                    'summary.max' => __('validation.summary.max'),
                    'summary.min' => __('validation.summary.min'),
                    'summary_ar.required' => __('validation.summary_ar.required'),
                    'summary_ar.max' => __('validation.summary_ar.max'),
                    'summary_ar.min' => __('validation.summary_ar.min'),
                ]);
                // $validator->setAttributeNames($fieldNames);

                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput();
                } else {

                    $property_description = PropertyDescription::where('property_id', $property_id)->first();
                    $property_description->summary = $request->summary;
                    // $property_description->summary_ar  = mb_convert_encoding($request->summary_ar, 'UTF-8', 'UTF-8') ;
                    $property_description->summary_ar = $request->summary_ar;
                    $property_description->save();
                    $this->helper->getLogs($property_description, 'web');

                    $this->propertyStep($property_id, 'description');
                    $property = Properties::find($property_id);
                    $property->accommodates = 1;
                    $property->adult_guest = 1;
                    $property->children_guest = 0;

                    $property->save();
                    $this->helper->getLogs($property, 'web');
                    $this->propertyStep($property_id, 'basics');


                    if ($request->purpose == 'saveAndExit') {
                        return redirect()->route('managehost.host_listings');
                    }
                    return redirect('listing/' . $property_id . '/basic');

                }
            }
        } elseif ($step == "basic") {
            if ($request->isMethod('Post')) {
                $property = Properties::find($property_id);
                $property->accommodates = $request->adult + $request->children;
                $property->adult_guest = $request->adult;
                $property->children_guest = $request->children;

                $property->save();
                $this->helper->getLogs($property, 'web');
                $this->propertyStep($property_id, 'basics');

                if ($request->purpose == 'saveAndExit') {
                    return redirect()->route('managehost.host_listings');
                }
                return redirect()->route('stepThree', ['property_id' => $property_id]);

                // return redirect('listing/' . $property_id . '/price');
            }
            // return view('listing.location');

        } elseif ($step == "price") {
            if ($request->isMethod('post')) {
                $bookings = Bookings::where('property_id', $property_id)->where('currency_code', '!=', $request->currency_code)->first();
                if ($bookings) {
                    return back()->withErrors(['currency' => trans('messages.error.currency_change')]);
                }
                $rules = [
                    'price' => 'required|numeric|min:50',
                    'weekly_discount' => 'nullable|numeric|max:99|min:0',
                    'monthly_discount' => 'nullable|numeric|max:99|min:0',
                ];
                foreach ($data['special_days'] as $special_day) {
                    $rules["price_{$special_day}"] = 'required|numeric|min:50';
                }

                $validator = Validator::make($request->all(), $rules, [
                    'price.required' => __('validation.price.required'),
                    'price.numeric' => __('validation.price.numeric'),
                    'price.min' => __('validation.price.min'),
                    'weekly_discount.numeric' => __('validation.weekly_discount.numeric'),
                    'weekly_discount.max' => __('validation.weekly_discount.max'),
                    'weekly_discount.min' => __('validation.weekly_discount.min'),
                    'monthly_discount.required' => __('validation.monthly_discount.required'),
                    'monthly_discount.max' => __('validation.monthly_discount.max'),
                    'monthly_discount.min' => __('validation.monthly_discount.min'),

                ]);
                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput();
                } else {
                    $special_days_price = [];
                    foreach ($data['special_days'] as $special_day) {
                        $special_days_price[$special_day] = $request->{"price_{$special_day}"};
                    }

                    $property_price = PropertyPrice::where('property_id', $property_id)->first();
                    $property_price->price = $request->price;
                    $property_price->special_days_price = $special_days_price;
                    $property_price->weekly_discount = isset($request->weekly_discount) ? $request->weekly_discount : 0;
                    $property_price->monthly_discount = isset($request->monthly_discount) ? $request->monthly_discount : 0;
                    $property_price->currency_code = isset($request->currency_code) ? $request->currency_code : 'SAR';
                    $property_price->cleaning_fee = isset($request->cleaning_fee) ? $request->cleaning_fee : 0;
                    $property_price->guest_fee = isset($request->guest_fee) ? $request->guest_fee : 0;
                    $property_price->guest_after = 0;
                    $property_price->security_fee = isset($request->security_fee) ? $request->security_fee : 0;
                    $property_price->weekend_price = isset($request->weekend_price) ? $request->weekend_price : 0;
                    $property_price->save();
                    $this->helper->getLogs($property_price, 'web');
                    $this->propertyStep($property_id, 'pricing');

                    if ($request->purpose == 'saveAndExit') {
                        return redirect()->route('managehost.host_listings');
                    }

                    $bankDetails = Bank::where('user_id', Auth::guard('web')->user()->id)->first();

                    if (!$bankDetails) return redirect('listing/' . $property_id . '/addBankAccount');

                    return redirect('listing/' . $property_id . '/nightsandtime');
                }
            }
        } elseif ($step == "addBankAccount") {
            if ($request->isMethod('post')) {
                $validator = Validator::make(
                    $request->all(),
                    [
                        'bank_name' => 'required|max:255',
                        'account_title' => 'required|max:255',
                        'account_number' => ['required', 'max:255', 'not_regex:/[!@#$%^&*\(\)=|\[\{\}\]\/+]/i'],
                        'iban' => ['required', 'max:255', 'not_regex:/[!@#$%^&*\(\)=|\[\{\}\]\/+]/i'],
                        'phone' => 'required|numeric|min:0',
                        'swift_code' => 'required',
                    ],
                    [
                        'not_regex' => 'The :attribute must not contain special characters.',
                    ]
                );

                if ($validator->fails()) return back()->withErrors($validator)->withInput();

                $data = new Bank();
                $data->user_id = Auth::guard('web')->user()->id;
                $data->bank_name = $request->bank_name;
                $data->account_title = $request->account_title;
                $data->account_number = $request->account_number;
                $data->iban = $request->iban;
                $data->phone = $request->phone;
                $data->swift_code = $request->swift_code;
                $data->save();

                if ($request->purpose == 'saveAndExit') {
                    return redirect()->route('managehost.host_listings');
                }

                return redirect('listing/' . $property_id . '/nightsandtime');
            }

            $bankDetails = Bank::where('user_id', Auth::guard('web')->user()->id)->first();
            if (!empty($bankDetails)) return redirect('listing/' . $property_id . '/nightsandtime');
        } elseif ($step == "nightsandtime") {
            if ($request->isMethod('post')) {
                $rules = [
                    'min_nights' => 'required|numeric|min:1',
                    'max_nights' => 'required|numeric|gte:min_nights',
                    'checkinTime' => 'required',
                    'checkoutTime' => 'required',
                ];

                $validator = Validator::make($request->all(), $rules, [
                    'min_nights.required' => __('validation.min_nights.required'),
                    'min_nights.numeric' => __('validation.min_nights.numeric'),
                    'min_nights.min' => __('validation.min_nights.min'),
                    'max_nights.required' => __('validation.max_nights.required'),
                    'max_nights.numeric' => __('validation.max_nights.numeric'),
                    'max_nights.gte' => __('validation.max_nights.gte'),
                    'checkinTime.required' => __('validation.checkinTime.required'),
                    'checkoutTime.required' => __('validation.checkoutTime.required'),
                ]);

                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput()->with($data);
                } else {
                    $property = Properties::find($property_id);
                    $property->max_nights = $request->max_nights;
                    $property->min_nights = $request->min_nights;
                    $property->checkinTime = $request->checkinTime;
                    $property->checkoutTime = $request->checkoutTime;

                    $property->save();
                    $this->helper->getLogs($property, 'web');
                    $this->propertyStep($property_id, 'nightsandtime');

                    if ($request->purpose == 'saveAndExit') {
                        return redirect()->route('managehost.host_listings');
                    }
                    return redirect('listing/' . $property_id . '/question');
                }
            }
        } elseif ($step == "question") {

            if ($request->isMethod('post')) {


                $property = Properties::find($property_id);
                $property->booking_type = $request->booking_type;
                // $property->status       = ( $property->steps_completed == 0 ) ?  'Listed' : 'Unlisted';
                $property->status = 'Unlisted';
                $property->visibility = 0;
                $property->save();
                $this->helper->getLogs($property, 'web');
                $this->propertyStep($property_id, 'booking');

                if ($request->purpose == 'saveAndExit') {
                    return redirect()->route('managehost.host_listings');
                }

                // if (isset(auth()->user()->communication_preference) && !$is_elm_verified) {
                //     return redirect('listing/' . $property_id . '/listingIqama');
                // } elseif (!isset(auth()->user()->communication_preference) && $is_elm_verified || !isset(auth()->user()->communication_preference) && !$is_elm_verified) {
                //     return redirect('listing/' . $property_id . '/communicationMethod');
                // } else {
                //     return redirect('listing/' . $property_id . '/calendar');
                // }

                return redirect('listing/' . $property_id . '/calendar');
            }
        } elseif ($step == "calendar") {
            if ($request->isMethod('post')) {

                if ($request->purpose == 'saveAndExit') {
                    return redirect()->route('managehost.host_listings');
                }

                if (isset(auth()->user()->communication_preference) && !$is_elm_verified) {
                    return redirect('listing/' . $property_id . '/listingIqama');
                } elseif (!isset(auth()->user()->communication_preference) && $is_elm_verified || !isset(auth()->user()->communication_preference) && !$is_elm_verified) {
                    return redirect('listing/' . $property_id . '/communicationMethod');
                } else {
                    return redirect('listing/' . $property_id . '/reviewListing');
                }
            }
        } elseif ($step == "communicationMethod") {

            if ($request->isMethod('post')) {
                $rules = [];

                if (isset(Auth::guard('api')->user()->communication_preference)) {
                    $rules['comm_method'] = 'nullable';
                } else {
                    $rules['comm_method'] = 'required';
                }

                $validator = Validator::make($request->all(), $rules);
                // if (is_array($request->comm_method)) {
                //     return back()->withErrors($validator)->withInput()->with($data);
                // }
                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput()->with($data);
                } else {
                    $user = User::find(Auth::id());
                    // $user->communication_preference = implode(',', $request->comm_method);
                    $user->communication_preference = is_array($request->comm_method) ? $request->comm_method : [$request->comm_method];
                    $user->save();

                    if (!$is_elm_verified) {
                        return redirect('listing/' . $property_id . '/listingIqama');
                    } else {
                        return redirect('listing/' . $property_id . '/reviewListing');
                    }
                    // return redirect('/managehost/host_listings');
                    return redirect('listing/' . $property_id . '/listingIqama');
                }
            }
        } elseif ($step == "listingIqama") {
            if ($request->isMethod('post')) {
                return redirect('listing/' . $property_id . '/reviewListing');
            }
        } elseif ($step == "reviewListing") {

            if ($request->isMethod('post')) {
                $rules = [
                    'license_number' => 'nullable|numeric|min_digits:8|unique:properties,license_no,' . $property_id . ',id',
                    'cr_number' => 'nullable|numeric|max_digits:10|unique:properties,cr_no,' . $property_id . ',id',
                    'is_company' => 'nullable|in:0,1',
                    'ref_code' => 'nullable|string|size:10|exists:admin,emp_code',
                ];
                $messages = [
                    'license_number.required' => 'License number required',
                    'license_number.numeric' => 'License number must be numeric',
                    'license_number.min_digits' => 'License number must be minimum 8 digits',
                    'license_number.unique' => 'License number already exists',

                    'cr_number.required' => 'CR number required',
                    'cr_number.numeric' => 'CR number must be numeric',
                    'cr_number.max_digits' => 'CR number must be maximum 10 digits',
                    'cr_number.unique' => 'CR number already exists',

                    'ref_code.exists' => 'Invalid Referral code',
                ];

                if (isset($request->is_company) && $request->is_company == 0) {
                    $rules = [
                        'license_number' => 'nullable|numeric|min_digits:8|unique:properties,license_no,' . $property_id . ',id',
                        'cr_number' => 'nullable|numeric|max_digits:20|unique:properties,cr_no,' . $property_id . ',id',
                        'is_company' => 'nullable|in:0,1',
                        'ref_code' => 'nullable|string|size:10|exists:admin,emp_code',
                    ];

                    $messages = [
                        'license_number.required' => 'Permit Number is required',
                        'license_number.numeric' => 'Permit Number must be numeric',
                        'license_number.min_digits' => 'Permit Number must be minimum 8 digits',
                        'license_number.unique' => 'Permit Number already exists',

                        'cr_number.required' => 'ID Number is required',
                        'cr_number.numeric' => 'ID Number must be numeric',
                        'cr_number.max_digits' => 'ID Number must be maximum 20 digits',
                        'cr_number.unique' => 'ID Number already exists',

                        'ref_code.exists' => 'Invalid Referral code',
                    ];
                }

                $validator = FacadesValidator::make(
                    $request->all(),
                    $rules,
                    $messages
                );

                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput();
                }

                $user = Auth::guard('web')->user();

                if ($request->license_number != null && $request->cr_number != null) {
                    $mtTourismService = new MTTourismService();

                    if (isset($request->is_company) && $request->is_company == 0) $verify = $mtTourismService->verifyLicense($request->license_number, $request->cr_number, $request->is_company ?? 1, $property_id, $user->id);
                    else $verify = $mtTourismService->verifyCompanyLicense($request->license_number, $request->cr_number, $request->is_company ?? 1, $property_id, $user->id);

                    if ($verify['status'] != true) {
                        FacadesSession::flash('error', $verify['error']);
                        return back()->withInput();
                    }

                    Properties::find($property_id)->update([
                        'license_no' => $request->license_number,
                        'cr_no' => $request->cr_number,
                        'license_is_company' => $request->is_company ?? 1,
                        'license_verified_at' => now(),
                        'license_expiry' => $verify['expiry_date'],
                        'license_company_name' => json_encode($verify['company']),
                    ]);


                    if (!!$request->ref_code) {
                        $admin = Admin::where('emp_code', $request->ref_code)->first()->id;
                        DB::table('admin_property')->insert([
                            'admin_id' => $admin,
                            'property_id' => $property_id,
                            'type' => 1,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }

                $this->helper->duplicateEntry($property->no_of_appartment, $property->id, 'web');


                $this->propertyStep($property_id, 'reviewListing');

                FacadesSession::flash('success_popup', true);
                return back()->with($data);

                // return redirect('/managehost/host_listings');
                // return redirect('listing/' . $property_id . '/');
            }
        }


        return view("listing.$step", $data);
    }


    /**
     * Handle the license upload.
     */
    public function uploadLicenseV3(Request $request, string $property_id)
    {

        try {
            $request->merge(['property_id' => $property_id]);
            if ($request->is('api/*')) $user = Auth::guard('api')->user();
            else $user = Auth::guard('web')->user();
            $result = $this->mtTourismService->validateAndProcessLicense($request, $property_id, $user->id);
            if ($result['success'] != true) {
                return apiResponse(['status' => "false", 'error' => $result['error']], "Error");
            }
            return apiResponse(['status' => "true"], "Success");
        } catch (Exception $e) {
            return response()->json([
                'status' => "false",
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
            ], 422);
        }
    }

    public function uploadLicense(Request $request, string $property_id)
    {
        try {
            $request->merge(['property_id' => $property_id]);

            $rules = [
                'property_id' => 'required|exists:properties,id',
                'license_is_company' => 'required|in:0,1',
                'step' => 'required|in:1,2',
                'license_no' => 'required|numeric|min_digits:8',
                'cr_no' => 'required|numeric|max_digits:10',
            ];

            if ($request->step == 2) $rules = [
                'property_id' => 'required|exists:properties,id',
                'license_is_company' => 'required|in:0,1',
                'step' => 'required|in:1,2',
                'license_company_name' => 'required',
                'license_expiry' => 'required|date',
                'license_document' => 'nullable|file|mimes:jpeg,png,jpg|max:2048',
            ];

            $attributes = [
                'property_id' => 'Property',
                'license_is_company' => 'Is Company',
                'step' => 'Step',
                'license_no' => 'License Number',
                'cr_no' => 'CR Number',
                'license_company_name' => 'Company Name',
                'license_expiry' => 'Expiry Date',
                'license_document' => 'Document',
            ];

            if ($request->license_is_company == 0) {
                $rules = [
                    'property_id' => 'required|exists:properties,id',
                    'license_is_company' => 'required|in:0,1',
                    'step' => 'required|in:1,2',
                    'license_no' => 'required|numeric|min_digits:8',
                    'cr_no' => 'required|numeric|max_digits:20',
                ];

                if ($request->step == 2) $rules = [
                    'property_id' => 'required|exists:properties,id',
                    'license_is_company' => 'required|in:0,1',
                    'step' => 'required|in:1,2',
                    'license_expiry' => 'required|date',
                    'license_document' => 'nullable|file|mimes:jpeg,png,jpg|max:2048',
                ];

                $attributes = [
                    'property_id' => 'Property',
                    'license_is_company' => 'Is Company',
                    'step' => 'Step',
                    'license_no' => 'Permit Number',
                    'cr_no' => 'ID Number',
                    'license_company_name' => 'Owner Name',
                    'license_expiry' => 'Expiry Date',
                    'license_document' => 'Document',
                ];
            }

            $validator = FacadesValidator::make(
                $request->all(),
                $rules,
                [
                    '*.required' => ':attribute required',
                    '*.unique' => ':attribute already exists',
                    '*.numeric' => ':attribute must be numeric',
                    '*.min_digits' => ':attribute must be minimum :value digits',
                    '*.max_digits' => ':attribute must be maximum :value digits',
                ],
                $attributes
            );

            if ($validator->fails()) throw new Exception($validator->errors()->first(), 422);


            if ($request->is('api/*')) $user = Auth::guard('api')->user();
            else $user = Auth::guard('web')->user();

            $alreadyExists = Properties::whereNotIn('id', [$request->property_id])
                ->where('license_no', $request->license_no)
                ->where('cr_no', $request->cr_no)
                ->where('license_is_company', $request->license_is_company)
                ->whereNotNull('license_verified_at')
                ->count();

            if ($alreadyExists > 0) throw new Exception('Failed due to license already exists', 422);

            if ($request->license_is_company == 0) {

                if ($request->step == 2) {
                    $property = Properties::find($request->property_id);
                    if ($property->license_expiry != setDateForDb($request->license_expiry)) throw new Exception('Failed due to license Expiry does not match', 422);

                    Properties::find($request->property_id)->update(['license_verified_at' => now()]);

                    if ($request->has('license_document')) {
                        $license_document = $request->file('license_document');

                        $uploadHelper = new ImageHelper();
                        $fileName = time() . '.' . $license_document->getClientOriginalExtension();
                        $uploadHelper->uploadNew($license_document, 'license', $fileName);
                        Properties::find($request->property_id)->update(['license_document' => $fileName]);
                    }

                    return apiResponse(['status' => "true"], 'License verified successfully');
                }
            }

            if ($request->step == 2) {
                $property = Properties::find($request->property_id);
                $company_name = json_decode($property->license_company_name, true);

                if ($company_name['ar'] != $request->license_company_name && $company_name['en'] != $request->license_company_name) throw new Exception('Failed due to Company name does not match', 422);
                if ($property->license_expiry != setDateForDb($request->license_expiry)) throw new Exception('Failed due to license Expiry does not match', 422);

                Properties::find($request->property_id)->update(['license_verified_at' => now()]);

                if ($request->has('license_document')) {
                    $license_document = $request->file('license_document');

                    $uploadHelper = new ImageHelper();
                    $fileName = time() . '.' . $license_document->getClientOriginalExtension();
                    $uploadHelper->uploadNew($license_document, 'license', $fileName);
                    Properties::find($request->property_id)->update(['license_document' => $fileName]);
                }

                return apiResponse(['status' => "true"], 'License verified successfully');
            }

            $mtTourismService = new MTTourismService();

            if ($request->license_is_company == 1) $verify = $mtTourismService->verifyCompanyLicense($request->license_no, $request->cr_no, $request->license_is_company ?? 1, $request->property_id, $user->id);
            else $verify = $mtTourismService->verifyLicense($request->license_no, $request->cr_no, $request->license_is_company ?? 0, $request->property_id, $user->id);

            if (!$verify['status']) throw new Exception($verify['error'], 422);

            Properties::find($request->property_id)->update([
                'license_no' => $request->license_no,
                'cr_no' => $request->cr_no,
                'license_is_company' => $request->license_is_company ?? 1,
                'license_expiry' => $verify['expiry_date'],
                'license_company_name' => json_encode($verify['company']),
            ]);

            return apiResponse(['status' => "true"], "Success");
        } catch (Exception $e) {
            return response()->json([
                'status' => "false",
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
            ], 422);
        }
    }

    public function clearLicense(Request $request)
    {

        try {
            $getProperty = Properties::findOrFail($request->id);

            $getProperty->update([
                'license_no' => null,
                'cr_no' => null,
                'license_verified_at' => null,
                'license_is_company' => null,
                'license_company_name' => null,
                'license_expiry' => null,
                'license_document' => null,
                'status' => 'UnListed',
                'visibility' => 0,

            ]);

            return redirect("admin/listing/{$getProperty->id}/license")->with('success', 'License information cleared successfully.');
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return redirect('admin/listing')->with('error', 'Property not found.');
        } catch (\Exception $e) {
            return redirect('admin/listing')->with('error', 'An error occurred while clearing the license.');
        }
    }

    public function edit_listing(Request $request)
    {
        $data['step'] = $step = $request->step;
        $data['property_id'] = $property_id = $request->id;
        $data['result'] = $property = Properties::with('property_address')->where('host_id', Auth::id())->findOrFail($property_id);
        $futureBooking = 0;

        $data['cover_photo'] = PropertyPhotos::where('property_id', $property_id)->where('cover_photo', 1)->first();
        $data['photos'] = PropertyPhotos::where('property_id', $property_id)->orderBy('serial', 'asc')->get();
        $data['updated_photos'] = PhotosTemp::where('property_id', $property_id)->where('cover_photo', 0)->orderBy('serial', 'asc')->get();
        $data['temp'] = PropertiesTemp::where('property_id', $property_id)->first();
        $data['space_type'] = SpaceType::where('status', 'Active')->get();

        //Amenities
        $data['property_amenities'] = explode(',', $data['result']->amenities);
        $data['common_amenities'] = Amenities::where('status', 'Active')->where('type_id', 1)->get();
        $data['safety_amenities'] = Amenities::where('status', 'Active')->where('type_id', 2)->get();
        $data['house_rules'] = Amenities::where('status', 'Active')->where('type_id', 3)->get();


        // if(isset($request->edit)){
        //    if(count($property->bookings)){
        //            foreach($property->bookings as $b){
        //                $b->start_date >= date('Y-m-d') ? $futureBooking++ : '';
        //            }
        //    }
        //    if($futureBooking){
        //        abort(401);
        //    }
        // }

        if (!$property->CanEdit) {
            abort(401);
        }
        $data['special_days'] = Country::orderBy('id')->first()->special_days;
        if ($step == "propertyType") {
            if ($request->isMethod('post')) {

                $rules = [
                    'space_type' => 'required',
                    'no_of_appartment' => 'required',
                ];

                $validator = Validator::make($request->all(), $rules);
                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput();
                } else {
                    $property = Properties::find($property_id);
                    $property->space_type = $request->space_type;
                    $property->no_of_appartment = $request->no_of_appartment;
                    $property->save();
                    $this->helper->getLogs($property, 'web');

                    $this->propertyStep($property_id, 'spacetype');
                    // $this->property_step($property_id, ['spacetype']);

                    //    return redirect('listing/' . $property_id . '/location');
                    $data['success'] = 'Property type updated Successfully!';

                    return back()->with($data);
                }
            }
        } elseif ($step == "photos") {

            if ($request->isMethod('post')) {

                // $rule = ['first_name' => 'string|max:255'];

                // For Permanent Images Ids Count
                $parmanentRequest = $request->permanentDeleteFile ?? [];
                $commaSeparatedString = reset($parmanentRequest);
                $permanentIdsArray = explode(',', $commaSeparatedString);

                // For Temporary Images Ids Count
                $temporaryRequest = $request->tempDeleteFile ?? [];
                $tempcommaSeparatedString = reset($temporaryRequest);
                $temporaryIdsArray = explode(',', $tempcommaSeparatedString);

                $tempArray = array_filter($temporaryIdsArray);
                $parmanentArray = array_filter($permanentIdsArray);


                $tempCount = count($tempArray);
                $permanentCount = count($parmanentArray);
                $rule = [];
                if ($tempCount == 0 && $permanentCount == 0) {
                    $rule = ['file' => 'required|max:2999'];
                }

                $validate = Validator::make($request->all(), $rule, [
                    'file.required' => 'Please select a file to upload.',
                    'file.max' => 'The :attribute may not be greater than 3MB.',
                ]);

                if ($validate->fails()) {
                    return redirect()->back()->withErrors($validate)->with($data);
                }
                $totalPhotos = count($data['photos']); //plus 1 for cover photo
                $reqPhotos = count($request->file('file') ?? []);
                $photos_temp = PhotosTemp::where('property_id', $request->propid)->count();


                //$allphotos = $totalPhotos + $reqPhotos + $photos_temp - $tempCount - $permanentCount;
                $allphotos = ($totalPhotos - $permanentCount) + $reqPhotos;
                $parmanentPhoto = $totalPhotos - $permanentCount;

                if ($allphotos < 5 || ($allphotos && $allphotos > 20)) {
                    if ($allphotos < 5) {
                        $photoErrorMessage = app()->getLocale() == "ar"
                            ? "يرجى تحميل " . (5 - $allphotos) . " المزيد من الصور."
                            : "Please upload " . (5 - $allphotos) . " more image(s).";
                    } else {
                        $photoErrorMessage = app()->getLocale() == "ar"
                            ? "الرجاء إزالة " . ($allphotos - 20) . " الصورة (الصور)."
                            : "Please remove " . ($allphotos - 20) . " image(s).";
                    }
                    return back()->withErrors(['file' => $photoErrorMessage])->with($data);
                }
                // }


                if ($request->hasFile('file')) {

                    $exist = PropertyPhotos::orderBy('serial', 'desc')
                        ->select('serial')
                        ->where('property_id', $property_id)
                        ->take(1)->first();

                    for ($i = 0; $i < count($request->file('file')); $i++) {
                        $name = str_replace(' ', '_', $_FILES["file"]["name"][$i]);
                        $ext = pathinfo($name, PATHINFO_EXTENSION);


                        if ($ext == 'png' || $ext == 'jpg' || $ext == 'jpeg' || $ext == 'JPG') {
                            // $uploaded = move_uploaded_file($tmp_name, $path . "/" . $image);
                            $imgFile = Image::make($_FILES["file"]["tmp_name"][$i]);
                            $imgFile->resize(null, 400, function ($constraint) {
                                $constraint->aspectRatio();
                                $constraint->upsize();
                            });
                            $image_url = ImageHelper::upload($imgFile, 'property/' . $property_id);
                            if ($property->status == 'Listed') {

                                $photos = new PhotosTemp;
                                $photos->property_id = $property_id;
                                $photos->photo = $image_url;
                                $photos->cover_photo = !empty($exist->serial) == true ? 0 : 1;
                                $photos->save();
                                $this->helper->getLogs($photos, 'web');
                            } else {
                                $exist = PropertyPhotos::orderBy('serial', 'desc')
                                    ->select('serial')
                                    ->where('property_id', $property_id)
                                    ->take(1)->first();

                                $photos = new PropertyPhotos;
                                $photos->property_id = $property_id;
                                $photos->photo = $image_url;

                                if ($exist) {
                                    // IF PHOTO EXISTS SERIAL PLUS ONE
                                    $photos->serial = $exist->serial + 1;
                                    $photos->cover_photo = 0;
                                } else {
                                    //IF NO PHOTOS EXISTS
                                    $photos->serial = 1;
                                    $photos->cover_photo = 1;
                                }

                                $photos->save();
                                $this->helper->getLogs($photos, 'web');
                            }
                        } else {

                            return back()->withErrors(['file.mimes' => 'Only jpeg, jpg, and png images are allowed.'])->with($data);
                        }
                    }
                }

                if (!empty($tempArray)) {
                    foreach ($tempArray as $tempFile) {
                        $tempImageIdsArray = explode(',', $tempFile);
                        foreach ($tempImageIdsArray as $imploded) {
                            $photos = PhotosTemp::find($imploded);

                            if ($photos->photo != "images/default-image-not-exist.png") {
                                unlink('images/property/' . $photos->property_id . '/' . $photos->photo);
                            }
                            $photos->delete();
                        }

                        // return response()->json(['status' => 'Success', 'message' => 'Photo removed successfully.']);
                    }
                }
                if (!empty($parmanentArray)) {
                    foreach ($parmanentArray as $permanentFile) {
                        $imageIdsArray = explode(',', $permanentFile);

                        foreach ($imageIdsArray as $imploded) {
                            $photobyid = PropertyPhotos::find($imploded);
                            $property = Properties::find($photobyid->property_id);
                            if ($property->host_id == Auth::id()) {

                                if ($property->status == "Unlisted") {
                                    $this->helper->photos_delete_adjust_serial($photobyid, $property);
                                } else {
                                    $photos = new PhotosTemp;
                                    $photos->property_id = $property_id;
                                    $photos->photo = $photobyid->photo;
                                    $photos->cover_photo = $photobyid->cover_photo;   //!empty($exist->serial) == true ?  0 : 1;
                                    $photos->serial = $photobyid->serial;
                                    $photos->remove = 1;
                                    $photos->remove_photo_id = $photobyid->id;

                                    $photos->save();
                                }

                                // else {
                                //     $deletedSerial = $photobyid->serial;

                                //     // Update the serial numbers of remaining photos with higher serial numbers
                                //     PropertyPhotos::where('property_id', $property->id)
                                //         ->where('serial', '>', $deletedSerial)
                                //         ->decrement('serial', 1);

                                //     if ($photobyid->cover_photo) {
                                //         $makeCoverPhoto = PropertyPhotos::where('property_id', $property->id)
                                //             ->where('cover_photo', 0)
                                //             ->orderBy('serial', 'desc')
                                //             ->take(1)
                                //             ->update(['cover_photo' => 1]);
                                //     }

                                //     //unlink($photobyid->photo);
                                //     $photobyid->delete();
                                // }
                            } else {
                                return response()->json(['status' => 'failure', 'message' => 'Unauthorized.']);
                            }
                        }
                    }
                }

                $data['success'] = customTrans('host_listing.images_success_msg');

                $this->propertyStep($property_id, 'photos');
                // $this->property_step($property_id, ['photos']);
                return back()->with($data);
            }
        } elseif ($step == "basics") {

            // dd($request->all());
            if ($request->isMethod('Post')) {

                // dd($request->all());

                $validate = Validator::make($request->all(), [
                    'summary' => 'required|max:500',
                    'summary_ar' => 'required|max:500|min:20',
                    'adult' => 'required|gt:0',
                    'title' => 'required',


                ], [
                    'summary.required' => 'Description is Required',
                    'summary_ar.required' => 'Arabic Descrtiption is Required',
                    'summary.max' => 'Description must be at most :max characters',
                    'summary.min' => 'Description must be at least :min characters',
                    'summary_ar.max' => 'Arabic Description must be at most :max characters',
                    'summary_ar.min' => 'Arabic Description must be at least :min characters',
                    'adult.gt' => 'Adult can not be less than 1',
                    'title' => 'Title is Required',
                ]);

                if ($validate->fails()) {
                    return back()->withErrors($validate)->withInput()->with($data);
                }

                $property = Properties::find($property_id);
                $property->accommodates = $request->adult + $request->children;
                $property->adult_guest = $request->adult;
                $property->children_guest = $request->children;
                $property->save();
                $this->helper->getLogs($property, 'web');

                $this->propertyStep($property_id, 'basics');
                // $this->property_step($property_id, ['basics']);


                // $newSummary_ar = $request->summary_ar;
                // $newSummary = $request->summary;
                // $newTitle = $request->title;


                if ($property->status == 'Unlisted') {
                    $prop = Properties::find($property_id);
                    $prop->fill([
                        'name' => $request->title,
                        'slug' => $property->name != $request->title ? $this->helper->pretty_url($request->title, $property->property_code) : $property->slug,
                    ]);
                    $prop->save();
                    $this->helper->getLogs($prop, 'web');

                    $property_description = PropertyDescription::where('property_id', $property_id)->first();
                    $property_description->fill([
                        'summary_ar' => $request->summary_ar,
                        'summary' => $request->summary,
                    ]);
                    $property_description->save();
                    $this->helper->getLogs($property_description, 'web');
                } else {

                    $newSummary_ar = $property->property_description->summary_ar != $request->summary_ar ? $request->summary_ar : null;
                    $newSummary = $property->property_description->summary != $request->summary ? $request->summary : null;
                    $newTitle = $property->name != $request->title ? $request->title : null;

                    if ($newSummary_ar || $newSummary || $newTitle) {

                        $temp = PropertiesTemp::firstOrNew(['property_id' => $property_id]);
                        $temp->fill([
                            'summary_ar' => $newSummary_ar,
                            'summary' => $newSummary,
                            'name' => $newTitle,
                            // 'slug' => $property->name != $request->title ? $this->helper->pretty_url($request->title) : $property->slug,
                        ]);

                        $temp->save();
                        $this->helper->getLogs($temp, 'web');
                    }
                }

                $this->propertyStep($property_id, 'description');
                $this->propertyStep($property_id, 'title');

                $data['success'] = 'Basics Updated Successfully! (Title and Description Changes will be reflect after admin approval.)';


                return back()->with($data);
            }
        } elseif ($step == "amenities") {
            if ($request->isMethod('Post')) {


                $validate = Validator::make($request->all(), [
                    'bedroom' => 'required',
                    'beds' => 'required|numeric|min:1',
                    'bathroom' => 'required',
                    'amenities' => 'required',


                ], [
                    'bedroom.required' => 'Bedroom value is Required',
                    'beds.required' => 'Beds is Required',
                    'beds.min' => 'Beds minimum will 1',
                    'bathroom.required' => 'Bathroom is Required',
                    'amenities.required' => 'Amenities is Required',
                ]);


                if ($validate->fails()) {
                    return back()->withErrors($validate)->withInput();
                } else {
                    $propertyamenities = Properties::find($request->id);
                    $propertyamenities->bedrooms = $request->bedroom;
                    $propertyamenities->beds = $request->beds;
                    $propertyamenities->bathrooms = $request->bathroom;
                    $propertyamenities->amenities = implode(',', $request->amenities);
                    $propertyamenities->save();
                    $this->helper->getLogs($propertyamenities, 'web');

                    // $this->property_step($property_id, ['amenities']);
                    $this->propertyStep($property_id, 'amenities');
                    $data['success'] = "Updated Successfully!";
                    return back()->with($data);
                }
            }
        } elseif ($step == "location") {
            if ($request->isMethod('Post')) {

                //  dd($request->all());

                $validate = Validator::make($request->all(), [
                    'address_line_1' => 'required',
                    'country' => 'required',
                    'city' => 'required',
                    'state' => 'required',
                    'latitude' => 'required|not_in:0',


                ], [
                    'address_line_1' => 'Address is Required',
                    'latitude.not_in' => 'Please set :attribute pointer',
                ]);


                if ($validate->fails()) {
                    return back()->withErrors($validate)->withInput();
                } else {
                    // $property = Properties::find($property_id);
                    // $property->name = SpaceType::getAll()->find($property->space_type)->name . ' in ' . $request->city;
                    // $property->save();


                    $property_address = PropertyAddress::where('property_id', $property_id)->first();
                    $property_address->address_line_1 = $request->address_line_1;
                    $property_address->address_line_2 = isset($request->address_line_2) ? $request->address_line_2 : NULL;
                    $property_address->latitude = $request->latitude;
                    $property_address->longitude = $request->longitude;
                    $property_address->city = $request->city;
                    $property_address->district = $request->district;
                    $property_address->state = $request->state;
                    $property_address->country = $request->country;
                    $property_address->postal_code = $request->postal_code;
                    $property_address->save();
                    $this->helper->getLogs($property_address, 'web');

                    // $this->property_step($property_id, ['location']);
                    $this->propertyStep($property_id, 'location');
                    $data['success'] = "Location Updated Successfully!";
                    return back()->with($data);
                }
            }
        } elseif ($step == "price") {
            if ($request->isMethod('post')) {
                $rules = [
                    'price' => 'required|numeric|min:50',
                    'weekly_discount' => 'nullable|numeric|max:99|min:0',
                    'monthly_discount' => 'nullable|numeric|max:99|min:0',
                ];
                foreach ($data['special_days'] as $special_day) {
                    $rules["price_{$special_day}"] = 'required|numeric|min:50';
                }

                $validate = Validator::make($request->all(), $rules);

                if ($validate->fails()) {
                    return back()->withErrors($validate)->withInput()->with($data);
                } else {
                    $special_days_price = [];
                    foreach ($data['special_days'] as $special_day) {
                        $special_days_price[$special_day] = $request->{"price_{$special_day}"};
                    }

                    $property_price = PropertyPrice::where('property_id', $property_id)->first();
                    $property_price->price = $request->price;
                    $property_price->special_days_price = $special_days_price;
                    $property_price->weekly_discount = isset($request->weekly_discount) ? $request->weekly_discount : 0;
                    $property_price->monthly_discount = isset($request->monthly_discount) ? $request->monthly_discount : 0;
                    $property_price->currency_code = isset($request->currency_code) ? $request->currency_code : 'SAR';
                    $property_price->cleaning_fee = isset($request->cleaning_fee) ? $request->cleaning_fee : 0;
                    $property_price->guest_fee = isset($request->guest_fee) ? $request->guest_fee : 0;
                    $property_price->guest_after = isset($request->guest_after) ? $request->guest_after : 0;
                    $property_price->security_fee = isset($request->security_fee) ? $request->security_fee : 0;
                    $property_price->weekend_price = isset($request->weekend_price) ? $request->weekend_price : 0;
                    $property_price->save();
                    $this->helper->getLogs($property_price, 'web');

                    $this->propertyStep($property_id, 'pricing');
                    $data['success'] = "Price Updated Successfully!";
                    return back()->with($data);
                }
            }
        } elseif ($step == "nightsandtime") {
            if ($request->isMethod('post')) {
                $rules = [
                    'min_nights' => 'required|numeric|min:1',
                    'max_nights' => 'required|numeric|gte:min_nights',
                    'checkinTime' => 'required',
                    'checkoutTime' => 'required',
                ];

                $validator = Validator::make($request->all(), $rules);

                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput()->with($data);
                } else {
                    $property = Properties::find($property_id);
                    $property->max_nights = $request->max_nights;
                    $property->min_nights = $request->min_nights;
                    $property->checkinTime = $request->checkinTime;
                    $property->checkoutTime = $request->checkoutTime;

                    $property->save();
                    $this->helper->getLogs($property, 'web');
                    $this->propertyStep($property_id, 'nightsandtime');

                    $data['success'] = "Time & Max/Min nights Updated Successfully!";
                    return back()->with($data);
                }
            }
        } elseif ($step == "question") {

            if ($request->isMethod('post')) {


                $property = Properties::find($property_id);
                $property->booking_type = $request->booking_type;
                $property->save();
                $this->helper->getLogs($property, 'web');

                $this->propertyStep($property_id, 'booking');
                $data['success'] = "Updated Successfully!";

                return back()->with($data);
            }
        }
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.listing.edit_listing', $data);
        } else {
            return view('listing.edit_listing', $data);
        }

        return view('listing.edit_listing', $data);
    }

    public function calender(CalendarController $calendar, Request $request)
    {
        session()->forget("propertyimageuploaded");
        $data['calendar'] = $calendar->generate($request->id);
        $data['result'] = Properties::where('host_id', Auth::id())->findOrFail($request->id);

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.property.booking_calender', $data);
        } else {
            return view('property.booking_calender', $data);
        }
        return view("property.booking_calender", $data);
    }

    public function RatingByGuest(Request $request)
    {
        $rules = [
            'rating' => 'required',
            'message' => 'required',
            'bookingid' => 'required',
            'file' => 'mimes:png,JPG,jpeg,gif,jpg',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            // return back()->withErrors($validator)->withInput();
            return response()->json(['errors' => $validator->errors()], 400);
        } else {
            // dd($request->rating);
            $propertyhost = User::find($request->propertyhostid);
            $property = Properties::find($request->propertyid);
            $review = new Reviews();
            $review->sender_id = Auth::id();
            $review->receiver_id = $request->propertyhostid;
            $review->booking_id = $request->bookingid;
            $review->property_id = $request->propertyid;
            $review->rating = $request->rating;
            $review->reviewer = "guest";
            $review->message = $request->message;
            $review->save();

            // Track review_published event in MoEngage
            \App\Helpers\MoEngageHelper::trackReviewPublished(
                $request->bookingid,
                Auth::id(),
                $request->propertyhostid,
                $request->rating,
                $request->message
            );

            if (isset($_FILES["file"]["name"])) {
                $tmp_name = $_FILES["file"]["tmp_name"];
                $name = str_replace(' ', '_', $_FILES["file"]["name"]);

                $ext = pathinfo($name, PATHINFO_EXTENSION);

                $image = time() . '_' . $name;


                // $path = 'public/images/property/' . $property_id;
                $path = 'images/reviews_by_guest/' . Auth::id() . '/';
                // dd($image);
                if (!file_exists($path)) {
                    mkdir($path, 0777, true);
                }
                if ($ext == 'png' || $ext == 'jpg' || $ext == 'jpeg' || $ext == 'gif' || $ext == 'JPG') {
                    $uploaded = move_uploaded_file($tmp_name, $path . "/" . $image);
                }
            }
            // $propertyhost->notify(new UserNotify("You have a new rating ", "لديك تقييم جديد", URL("/user/reviews"), 'booking.request.new', [':property' => $property->name], "host_review", ""));

            // if ($propertyhost->fcm_token) {
            //     $msg = $propertyhost->lang == "ar" ? "لديك تقييم جديد" : "You have a new rating";
            //     $this->helper->sendPushNotification($propertyhost->fcm_token, $msg, null, "host_review");
            // }
            return response()->json(['success' => true, 'Success']);
            return redirect('/reservation');
        }
    }

    public function RatingByHost(Request $request)
    {
        $rules = [
            'rating' => 'required',
            'message' => 'required',
            'reviewid' => 'required',
            'file' => 'mimes:png,JPG,jpeg,gif,jpg',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        } else {
            $UserReview = Reviews::find($request->reviewid);

            $property = Properties::find($request->propertyid);

            $ReviewedUser = User::find($request->guestid);
            $review = new Reviews();
            $review->sender_id = Auth::id();
            $review->receiver_id = $request->guestid;
            $review->booking_id = $UserReview->booking_id;
            $review->property_id = $UserReview->property_id;
            $review->rating = $request->rating;
            $review->reviewer = "host";
            $review->message = $request->message;
            $review->save();

            // Track host_guest_review_published event across all platforms
            \App\Jobs\EventTrackingJob::track(
                'host_guest_review_published',
                [
                    'host_id' => Auth::id(),
                    'guest_id' => $request->guestid,
                    'host_review_rating' => $request->rating,
                    'host_review_message' => $request->message
                ],
                Auth::id()
            );

            // $UserReview = Reviews::find($request->reviewid);
            $UserReview->ispublic = 1;
            $UserReview->save();
            if (isset($_FILES["file"]["name"])) {
                $tmp_name = $_FILES["file"]["tmp_name"];
                $name = str_replace(' ', '_', $_FILES["file"]["name"]);

                $ext = pathinfo($name, PATHINFO_EXTENSION);

                $image = time() . '_' . $name;


                // $path = 'public/images/property/' . $property_id;
                $path = 'images/reviews_by_host/' . Auth::id() . '/';
                // dd($image);
                if (!file_exists($path)) {
                    mkdir($path, 0777, true);
                }
                if ($ext == 'png' || $ext == 'jpg' || $ext == 'jpeg' || $ext == 'gif' || $ext == 'JPG') {
                    $uploaded = move_uploaded_file($tmp_name, $path . "/" . $image);
                }
            }

            $msg_en = customTrans('notifications.rating.posted', [], 'en');
            $msg_ar = customTrans('notifications.rating.posted', [], 'ar');
            $msg = $ReviewedUser->lang == "ar" ? $msg_ar : $msg_en;
            // $ReviewedUser->notify(new UserNotify($msg_en, $msg_ar, URL("properties/" . $property->slug), 'rating.posted', null, "guest_review", ""));

            if ($ReviewedUser->fcm_token) {
                $this->helper->sendPushNotification($ReviewedUser->fcm_token, $msg, null, "guest_review");
            }

            // return redirect('/user/reviews');
            return true;
        }
    }

    public function DeletePhoto(Request $request)
    {

        try {
            if ($request->photoType == 'temp') {
                $photos = PhotosTemp::find($request->photoid);
                if ($photos->photo != "images/default-image-not-exist.png") {
                    unlink('images/property/' . $photos->property_id . '/' . $photos->photo);
                }
                $photos->delete();
                return response()->json(['status' => 'Success', 'message' => 'Photo removed successfully.']);
            }
            $photobyid = PropertyPhotos::find($request->photoid);
            $property = Properties::find($photobyid->property_id);
            if ($property->host_id == Auth::id()) {

                $photos = new PhotosTemp;
                $photos->property_id = $photobyid->property_id;
                $photos->photo = $photobyid->photo;
                $photos->cover_photo = !empty($exist->serial) == true ? 0 : 1;
                $photos->serial = $photobyid->serial;
                $photos->remove = 1;
                $photos->remove_photo_id = $photobyid->id;

                $photos->save();

                // $deletedSerial = $photobyid->serial; // Store the serial number of the deleted photo

                // // Update the serial numbers of remaining photos with higher serial numbers
                // PropertyPhotos::where('property_id', $property->id)
                //     ->where('serial', '>', $deletedSerial)
                //     ->decrement('serial', 1);

                // if ($photobyid->cover_photo) {

                //     $makeCoverPhoto = PropertyPhotos::where('property_id', $property->id)
                //         ->where('cover_photo', 0)
                //         ->orderBy('serial', 'desc')
                //         ->take(1)
                //         ->update(['cover_photo' => 1]);
                // }
                // $photoPath = public_path(parse_url($photobyid->photo, PHP_URL_PATH));

                // // Delete the photo from the server
                // if (file_exists($photoPath)) {

                //     unlink($photoPath);
                // }
                // // unlink($photobyid->photo);
                // $photobyid->delete();
            } else {
                return response()->json(['status' => 'failure', 'message' => 'Unauthorized.']);
            }
            $checkphotos = PropertyPhotos::where('property_id', $photobyid->property_id)->get();
            if (count($checkphotos) < 1) {
                session()->put("propertyimageuploaded", "0");

                return response()->json(['status' => 'Success', 'message' => 'Photo removed successfully.']);
            }

            return response()->json(['status' => 'Success', 'message' => 'Photo removed successfully.']);
        } catch (Throwable $th) {
            return response(['message' => 'failure', 'error' => $th->getMessage()], 500);
        }
    }

    public function translate(Request $request)
    {

        $trans = $request->translateTo;
        $from = $trans == "ar" ? "en" : "ar";
        $to = $trans == "en" ? "en" : "ar";


        try {

            // $google = GoogleTranslate::translate(mb_convert_encoding($request->sentence, 'UTF-8', 'UTF-8'), 'en', 'ar');
            $google = GoogleTranslate::translate(mb_convert_encoding($request->sentence, 'UTF-8', 'UTF-8'), $from, $to);
            return response(['status' => 200, 'message' => 'success', 'data' => $google['translated_text']]);
        } catch (Throwable $th) {
            return response(['status' => $th->getCode(), 'message' => 'error', 'data' => $th->getMessage()]);
        }
    }

    // host listing

    public function host_listings(Request $request)
    {
        $data['title'] = 'All Listings';
        $data['amenities'] = Amenities::where('status', 'Active')->get(['id', 'title'])->toArray();
        $data['is_mob'] = $this->helper->isRequestFromMobile($request);
        $data['lang'] = LocalizationKeyword::select(app()->getLocale() . '_value AS value')->where('parent_key', 'host_listing')->first();
        $data['lang'] = !!$data['lang'] ? $data['lang']->value : '{}';
        // dd($data);
        return view('managehost.host_listings', $data);
    }


    public function yourlisting(Request $request)
    {
        $data['title'] = 'Listings';

        $data['completeproperties'] = Properties::with([
            'property_price',
            'property_address',
            'property_photos',
            'property_description',
            'property_steps',
        ])
            ->where('host_id', Auth::id())
            ->whereHas('property_steps', function ($query) {
                $query->where('total_steps', 0);
            })
            ->orderBy('id', 'desc')
            ->paginate(10);

        // dd(count($data['completeproperties']));

        $data['incompleteproperty'] = Properties::with([
            'property_price',
            'property_address',
            'property_photos',
            'property_description',
            'property_steps',
        ])
            ->where('host_id', Auth::id())
            ->whereHas('property_steps', function ($query) {
                $query->where('total_steps', '!=', 0);
            })
            ->orderBy('id', 'desc')
            ->paginate(10);

        // upcoming booking (host)
        $data['upcomingBooking'] = Bookings::with('users', 'properties')
            ->where('host_id', Auth::user()->id)
            ->whereIn('status', ['Processing', 'Pending', 'Unpaid', 'Accepted'])
            ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
            ->orderBy('id', 'desc')
            ->paginate(10);

        if ($this->helper->isRequestFromMobile($request)) {
            // The request is from a mobile browser
            // Your code here
            // return view('home.mobile.home', $data);
            return view('mobile.list_management.your_listing', $data);
        } else {
            // The request is not from a mobile browser
            // Your code here
            return view('list_management.your_listing', $data);
        }
        // return view('list_management.your_listing', $data);
    }

    public function moreListing(Request $request)
    {
        if ($request->tab == "complete") {
            $data['completeproperties'] = Properties::with([
                'property_price',
                'property_address',
                'property_photos',
                'property_description',
                'property_steps',
            ])
                ->where('host_id', Auth::id())
                ->whereHas('property_steps', function ($query) {
                    $query->where('total_steps', 0);
                })
                ->orderBy('id', 'desc')
                ->skip(request('offset'))->take(10)->get();

            $view = view('pagination.complete_listing', $data)->render();
            return response()->json(['properties' => $view, 'tab' => 'complete']);
        } else {
            $data['incompleteproperty'] = Properties::with([
                'property_price',
                'property_address',
                'property_photos',
                'property_description',
                'property_steps',
            ])
                ->where('host_id', Auth::id())
                ->whereHas('property_steps', function ($query) {
                    $query->where('total_steps', '!=', 0);
                })
                ->orderBy('id', 'desc')
                ->skip(request('offset'))->take(10)->get();

            $view = view('pagination.incomplete_listing', $data)->render();
            return response()->json(['properties' => $view, 'tab' => 'incomplete']);
        }
    }

    public function checklisting(Request $request)
    {
        $propertyforchangelisting = Properties::find($request->propertyid);
        // dd($request->propertyid,$request->checkbox_value);
        if ($request->checkbox_value == "true") {
            $propertyforchangelisting->visibility = 1;
            $propertyforchangelisting->save();
        }
        if ($request->checkbox_value == "false") {
            $propertyforchangelisting->visibility = 0;
            $propertyforchangelisting->save();
        }
    }

    public function create(Request $request)
    {
        if ($request->isMethod('post')) {
            $rules = [
                'property_type_id' => 'required',
                'space_type' => 'required',
                'accommodates' => 'required',
                'map_address' => 'required',
            ];

            $fieldNames = [
                'property_type_id' => 'Home Type',
                'space_type' => 'Room Type',
                'accommodates' => 'Accommodates',
                'map_address' => 'City',
            ];

            $validator = Validator::make($request->all(), $rules);
            $validator->setAttributeNames($fieldNames);

            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            } else {
                $property = new Properties;
                $property->host_id = Auth::id();
                $property->name = SpaceType::getAll()->find($request->space_type)->name . ' in ' . $request->city;
                $property->property_type = $request->property_type_id;
                $property->space_type = $request->space_type;
                $property->accommodates = $request->accommodates;
                $property->save();

                $property_address = new PropertyAddress;
                $property_address->property_id = $property->id;
                $property_address->address_line_1 = $request->route;
                $property_address->city = $request->city;
                $property_address->state = $request->state;
                $property_address->country = $request->country;
                $property_address->postal_code = $request->postal_code;
                $property_address->latitude = $request->latitude;
                $property_address->longitude = $request->longitude;
                $property_address->save();

                $property_price = new PropertyPrice;
                $property_price->property_id = $property->id;
                $property_price->currency_code = Session::get('currency');
                $property_price->save();

                $property_steps = new PropertySteps;
                $property_steps->property_id = $property->id;
                $property_steps->save();

                $property_description = new PropertyDescription;
                $property_description->property_id = $property->id;
                $property_description->save();

                return redirect('listing/' . $property->id . '/basics');
            }
        }

        $data['property_type'] = PropertyType::getAll()->where('status', 'Active')->pluck('name', 'id');
        // $data['space_type']    = SpaceType::getAll()->where('status', 'Active')->pluck('name', 'id');

        return view('property.create', $data);
    }

    public function listing(Request $request, CalendarController $calendar)
    {

        $step = $request->step;
        $property_id = $request->id;
        $data['step'] = $step;
        $data['result'] = Properties::where('host_id', Auth::id())->findOrFail($property_id);
        $data['details'] = PropertyDetails::pluck('value', 'field');
        $data['missed'] = PropertySteps::where('property_id', $request->id)->first();


        if ($step == 'basics') {
            if ($request->isMethod('post')) {
                $property = Properties::find($property_id);
                $property->bedrooms = $request->bedrooms;
                $property->beds = $request->beds;
                $property->bathrooms = $request->bathrooms;
                $property->bed_type = $request->bed_type;
                $property->property_type = $request->property_type;
                $property->space_type = $request->space_type;
                $property->accommodates = $request->accommodates;
                $property->save();

                $this->propertyStep($property_id, 'basics');
                // $this->property_step($property_id, ['basics']);

                return redirect('listing/' . $property_id . '/description');
            }

            $data['bed_type'] = BedType::getAll()->pluck('name', 'id');
            $data['property_type'] = PropertyType::getAll()->where('status', 'Active')->pluck('name', 'id');
            $data['space_type'] = SpaceType::getAll()->pluck('name', 'id');
            if ($this->scattered()) {
                Session::flush();
                return view('vendor.installer.errors.user');
            }
        } elseif ($step == 'description') {
            if ($request->isMethod('post')) {
                $rules = [
                    'name' => 'required|max:50',
                    'summary' => 'required|max:1000',
                    'summary_ar' => 'required|max:1000',
                ];

                $fieldNames = [
                    'name' => 'Name',
                    'summary' => 'Description',
                    'summary_ar' => 'Description in arabic',
                ];

                $validator = Validator::make($request->all(), $rules);
                $validator->setAttributeNames($fieldNames);

                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput();
                } else {
                    $property = Properties::find($property_id);
                    $property->name = $request->name;
                    $property->slug = $this->helper->pretty_url($request->name, $property->property_code);
                    $property->save();

                    $property_description = PropertyDescription::where('property_id', $property_id)->first();
                    $property_description->summary = $request->summary;
                    $property_description->summary_ar = $request->summary_ar;
                    $property_description->save();

                    $this->propertyStep($property_id, 'description');
                    // $this->property_step($property_id, ['description']);

                    return redirect('listing/' . $property_id . '/location');
                }
            }
            $data['description'] = PropertyDescription::where('property_id', $property_id)->first();
        } elseif ($step == 'details') {
            if ($request->isMethod('post')) {
                $property_description = PropertyDescription::where('property_id', $property_id)->first();
                $property_description->about_place = $request->about_place;
                $property_description->place_is_great_for = $request->place_is_great_for;
                $property_description->guest_can_access = $request->guest_can_access;
                $property_description->interaction_guests = $request->interaction_guests;
                $property_description->other = $request->other;
                $property_description->about_neighborhood = $request->about_neighborhood;
                $property_description->get_around = $request->get_around;
                $property_description->save();

                return redirect('listing/' . $property_id . '/description');
            }
        } elseif ($step == 'location') {
            if ($request->isMethod('post')) {
                $rules = [
                    'address_line_1' => 'required|max:250',
                    // 'address_line_2'    => 'required|max:250',
                    'country' => 'required',
                    'city' => 'required',
                    'state' => 'required',
                    'latitude' => 'required|not_in:0',
                ];

                $fieldNames = [
                    'address_line_1' => 'Address',
                    'country' => 'Country',
                    'city' => 'City',
                    'state' => 'State',
                    'latitude' => 'Map',
                ];

                $messages = [
                    'not_in' => 'Please set :attribute pointer',
                    // 'address_line_2' => 'Please provide house/apt no',

                ];

                $validator = Validator::make($request->all(), $rules, $messages);
                $validator->setAttributeNames($fieldNames);

                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput();
                } else {
                    $property_address = PropertyAddress::where('property_id', $property_id)->first();
                    $property_address->address_line_1 = $request->address_line_1;
                    $property_address->address_line_2 = $request->address_line_2;
                    $property_address->latitude = $request->latitude;
                    $property_address->longitude = $request->longitude;
                    $property_address->city = $request->city;
                    $property_address->state = $request->state;
                    $property_address->country = $request->country;
                    $property_address->postal_code = $request->postal_code;
                    $property_address->save();

                    // $this->property_step($property_id, ['location']);
                    $this->propertyStep($property_id, 'location');


                    return redirect('listing/' . $property_id . '/amenities');
                }
            }
            $data['country'] = Country::pluck('name', 'short_name');
        } elseif ($step == 'amenities') {
            if ($request->isMethod('post') && is_array($request->amenities)) {
                $rooms = Properties::find($request->id);
                $rooms->amenities = implode(',', $request->amenities);
                $rooms->save();
                return redirect('listing/' . $property_id . '/photos');
            }
            $data['property_amenities'] = explode(',', $data['result']->amenities);
            $data['amenities'] = Amenities::where('status', 'Active')->get();
            $data['amenities_type'] = AmenityType::get();
        } elseif ($step == 'photos') {
            if ($request->isMethod('post')) {
                if ($request->crop == 'crop' && $request->photos) {
                    $baseText = explode(";base64,", $request->photos);
                    $name = explode(".", $request->img_name);
                    $convertedImage = base64_decode($baseText[1]);
                    $request->request->add(['type' => end($name)]);
                    $request->request->add(['image' => $convertedImage]);


                    $validate = Validator::make($request->all(), [
                        'type' => 'required|in:png,jpg,JPG,JPEG,jpeg,bmp',
                        'img_name' => 'required',
                        'photos' => 'required',
                    ]);
                } else {
                    $validate = Validator::make($request->all(), [
                        'file' => 'required|file|mimes:jpg,jpeg,bmp,png,gif,JPG',
                        'file' => 'dimensions:min_width=640,min_height=360',
                    ]);
                }

                if ($validate->fails()) {
                    return back()->withErrors($validate)->withInput();
                }

                $path = 'property/' . $property_id;
                $image_url = null;
                if (!file_exists($path)) {
                    mkdir($path, 0777, true);
                }

                if ($request->crop == "crop") {
                    $image = $name[0] . uniqid() . '.' . end($name);
                    $uploaded = file_put_contents($path . $image, $convertedImage);
                    $image = Image::make($uploaded);
                    $image_url = ImageHelper::upload($image, $path);
                } else {
                    if (isset($_FILES["file"]["name"])) {
                        $ext = pathinfo($_FILES["file"]["name"], PATHINFO_EXTENSION);

                        if ($ext == 'png' || $ext == 'jpg' || $ext == 'jpeg' || $ext == 'gif' || $ext == 'JPG') {
                            $image_url = ImageHelper::upload($request->file('file'), $path);
                        }
                    }
                }

                if (!is_null($image_url)) {
                    $photos = new PropertyPhotos;
                    $photos->property_id = $property_id;
                    $photos->photo = $image_url;
                    $photos->serial = 1;
                    $photos->cover_photo = 1;

                    $exist = PropertyPhotos::orderBy('serial', 'desc')
                        ->select('serial')
                        ->where('property_id', $property_id)
                        ->take(1)->first();

                    if (!empty($exist->serial)) {
                        $photos->serial = $exist->serial + 1;
                        $photos->cover_photo = 0;
                    }
                    $photos->save();

                    $this->propertyStep($property_id, 'photos');
                    // $this->property_step($property_id, ['photos']);

                }
                // sessionStorage.setItem("propertyimageuploaded", "1");
                session()->put("propertyimageuploaded", "1");
                return redirect('listing/' . $property_id . '/photos')->with('success', 'File Uploaded Successfully!');
            }
            $data['photos'] = PropertyPhotos::where('property_id', $property_id)
                ->orderBy('serial', 'asc')
                ->get();
        } elseif ($step == 'pricing') {
            if ($request->isMethod('post')) {
                $bookings = Bookings::where('property_id', $property_id)->where('currency_code', '!=', $request->currency_code)->first();
                if ($bookings) {
                    return back()->withErrors(['currency' => trans('messages.error.currency_change')]);
                }
                $rules = [
                    'price' => 'required|numeric|min:5',
                    'weekly_discount' => 'nullable|numeric|max:99|min:0',
                    'monthly_discount' => 'nullable|numeric|max:99|min:0',
                ];

                $fieldNames = [
                    'price' => 'Price',
                    'weekly_discount' => 'Weekly Discount Percent',
                    'monthly_discount' => 'Monthly Discount Percent',
                ];

                $validator = Validator::make($request->all(), $rules);
                $validator->setAttributeNames($fieldNames);

                if ($validator->fails()) {
                    return back()->withErrors($validator)->withInput();
                } else {
                    $property_price = PropertyPrice::where('property_id', $property_id)->first();
                    $property_price->price = $request->price;
                    $property_price->weekly_discount = $request->weekly_discount;
                    $property_price->monthly_discount = $request->monthly_discount;
                    $property_price->currency_code = $request->currency_code;
                    $property_price->cleaning_fee = $request->cleaning_fee;
                    $property_price->guest_fee = $request->guest_fee;
                    $property_price->guest_after = $request->guest_after;
                    $property_price->security_fee = $request->security_fee;
                    $property_price->weekend_price = $request->weekend_price;
                    $property_price->save();


                    $this->propertyStep($property_id, 'pricing');
                    // $this->property_step($property_id, ['pricing']);


                    return redirect('listing/' . $property_id . '/booking');
                }
            }
        } elseif ($step == 'booking') {
            if ($request->isMethod('post')) {

                $this->propertyStep($property_id, 'booking');
                // $this->property_step($property_id, ['booking']);

                $properties = Properties::find($property_id);
                $properties->booking_type = $request->booking_type;
                // $properties->status       = ($properties->steps_completed == 0) ?  'Listed' : 'Unlisted';
                $properties->save();


                return redirect('listing/' . $property_id . '/calendar');
            }
        } elseif ($step == 'calendar') {
            session()->forget("propertyimageuploaded");
            $data['calendar'] = $calendar->generate($request->id);
        }
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.listing.$step', $data);
        } else {
            return view('listing.$step', $data);
        }

        return view("listing.$step", $data);
    }

    public function updateStatus(Request $request)
    {
        $property_id = $request->id;
        $reqstatus = $request->status;
        if ($reqstatus == 'Listed') {
            $status = 'Unlisted';
        } else {
            $status = 'Listed';
        }
        $properties = Properties::where('host_id', Auth::id())->find($property_id);
        $properties->status = $status;
        $properties->save();
        return response()->json($properties);
    }

    public function getPrice(Request $request)
    {

        return $this->helper->getPrice($request->property_id, $request->checkin, $request->checkout, $request->guest_count);
    }

    public function viewProperty(Request $request){
        $success = $this->helper->updateProductView($request);
        if($success) {
            return response()->json(['success'=> true]);
        } else {
            return response()->json(['success'=> false]);
        }
    }

    public function single(Request $request, $lang = null, $slug)
    {
        // Create a cache key based only on the property slug
        // Excluding dynamic parameters like language, checkin, checkout, guests, and user_id
        // since these are handled by the view or AJAX requests on the client side
        $cacheKey = 'property_single_data_' . $slug;

        // Try to get the cached data
        $cachedData = Cache::get($cacheKey);
        if ($cachedData) {
            $data = $cachedData;
        } else {
            // If not cached, prepare the data
            $propertyFees = PropertyFees::pluck('value', 'field');
            $checkin = Carbon::parse($request->get('checkin')) ?? now();
            $checkout = Carbon::parse($request->get('checkout')) ?? now();
            $properties_pricing = $this->helper->getPropertiesPricing($checkin->format('Y-m-d'), $checkout->format('Y-m-d'));

            $data = [];
            $data['guest_service_charge'] = $propertyFees['guest_service_charge'];
            $data['property_slug'] = $request->slug;
            $data['result'] = $result = Properties::query()
            ->select([
                'properties.*',
                'properties_pricing.day_price as day_price',
                'properties_pricing.total_price as total_price',
                'properties_pricing.number_of_days as number_of_days',
                'properties_pricing.before_discount as before_discount',
            ])
            ->joinSub($properties_pricing, 'properties_pricing', function (JoinClause $join) {
                $join->on('properties.id', '=', 'properties_pricing.property_id');
            })
            ->where(function ($query) use ($request) {
                $query->where('slug', $request->slug)
                    ->orWhere('old_slug', $request->slug);
            })
            ->with(['propertyType', 'space', 'property_price'])
            ->withCount('userPropertyView')
            ->where('status', 'Listed')
            ->where('visibility', 1)
            ->first();

        if (empty($result)) {
            abort('404');
        }
        if (empty($result)) {
            return view('errors.not_found');
        }
        if (!empty($result) && $request->slug !== $result->slug) {
            return redirect()->route('property.single', ['slug' => $result->slug]);
        }
        $code = $request->input('code');
        if ($code && HostReferalCode::validateReferalCode($code, $data['result']->id)) {
            // Store a referal_code in the session
            $request->session()->put('referal_code', $code);
        }

        $data['grid_property'] = GridProperty::where('id', $result->id)->first();
        $data['property_id'] = $id = $result->id;
        $data['license_no'] = $result->license_no;
        $this->helper->trackProductView($data['property_id']);
        $data['property_photos'] = PropertyPhotos::where('property_id', $id)->orderBy('serial', 'asc')->get();
        $data['amenities'] = Amenities::normal($id);
        $data['safety_amenities'] = Amenities::security($id);
        $data['house_rule_amenities'] = Amenities::house_rule($id);

        $sa_count = 0;

        foreach ($data['safety_amenities'] as $sa) {
            if ($sa->status != null) {
                $sa_count++;
            }
        }
        $data['safety_amenities_count'] = $sa_count;

        $property_address = $data['result']->property_address;
        $latitude = $property_address->latitude;
        $longitude = $property_address->longitude;
        $data['nearbyCoordinates'] = $this->helper->calculateNearbyCoordinates($latitude, $longitude, Properties::DISTANCE_IN_KM, Properties::BEARING_IN_DEGREES);
        $data['checkin'] = (isset($request->checkin) && $request->checkin != '') ? $request->checkin : '';
        $data['checkout'] = (isset($request->checkout) && $request->checkout != '') ? $request->checkout : '';
        $data['guests'] = (isset($request->guests) && $request->guests != '') ? $request->guests : '';
        $data['user_wallet'] = NewUserWallet::where('user_id', auth()->id())->first();

        $min_price = $data['result']->total_price - ($data['result']->total_price * 30 / 100);
        $max_price = $data['result']->total_price + ($data['result']->total_price * 30 / 100);

        $data['similar'] = Properties::query()
            ->join('property_address', function ($join) {
                $join->on('properties.id', '=', 'property_address.property_id');
            })
            ->joinSub($properties_pricing, 'properties_pricing', function (JoinClause $join) {
                $join->on('properties.id', '=', 'properties_pricing.property_id');
            })
            ->select(DB::raw('*, ( 3959 * acos( cos( radians(' . $latitude . ') ) * cos( radians( latitude ) ) * cos( radians( longitude ) - radians(' . $longitude . ') ) + sin( radians(' . $latitude . ') ) * sin( radians( latitude ) ) ) ) as distance'))
            ->having('distance', '<=', 30)
            ->whereBetween('properties_pricing.total_price', [$min_price, $max_price])
            ->where('properties.host_id', '!=', Auth::id())
            ->where('properties.id', '!=', $id)
            ->where('properties.slug', '!=', '')
            ->where('properties.status', 'Listed')
            ->where('properties.visibility', 1)
            ->limit(8)
            ->get();

        $data['symbol'] = $this->helper->getCurrentCurrencySymbol();
        $data['shareLink'] = url('/') . '/' . 'properties/' . $data['property_id'];

        $data['date_format'] = Settings::getAll()->firstWhere('name', 'date_format_type')->value;
        // dd($id);
        $SingleProperty = Properties::selectRaw(
            'properties.id, properties.property_code, properties.name, properties.slug'
            . (auth()->check() ? ', pchs.id AS chat_head_id, (pis.id IS NOT NULL || bs.id IS NOT NULL) AS chat_redirect' : '')
        )
            ->when(auth()->check(), fn($q) => $q->leftJoin(
                'property_chat_heads AS pchs',
                fn($q1) => $q1->on('pchs.property_id', 'properties.id')
                    ->where('pchs.guest_id', auth()->id())
            )
                ->leftJoin(
                    'property_inquiries as pis',
                    fn($q1) => $q1->on('pis.property_id', 'properties.id')
                        ->where('pis.guest_id', auth()->id())
                        ->whereDate('pis.end_date', '>', now()->format('Y-m-d H:i:s'))
                        ->whereIn('pis.status', [PropertyInquiryStatusTypeEnum::Pending->value, PropertyInquiryStatusTypeEnum::Accepted->value])
                )
                ->leftJoin(
                    'bookings as bs',
                    fn($q1) => $q1->on('bs.property_id', 'properties.id')
                        ->where('bs.user_id', auth()->id())
                        ->whereDate('bs.end_date', '>', now()->format('Y-m-d H:i:s'))
                        ->whereIn('bs.status', ['Processing', 'Pending', 'Unpaid', 'Accepted'])
                ))->where('properties.id', $id)->first();
        if (auth()->check() && $result->host_id != auth()->id()) {
            $data['chat_redirect'] = $SingleProperty->chat_redirect == 1 && !!$SingleProperty->chat_head_id;
            $data['chat_head_id'] = $SingleProperty->chat_head_id;
        }
        $SingleProperty->makeHidden($SingleProperty->getAppends());

        $data['is_contact'] = false;
        if (Auth::guard('web')->check()) {
            $checkOngoingBooking = Bookings::where('property_id', $result->id)
                ->where('status', 'Accepted')
                ->where('user_id', Auth::guard('web')->user()->id)
                ->exists();
            if ($checkOngoingBooking) $data['is_contact'] = true;
        } else {
            $data['is_contact'] = true;
        }

        $reviews = Reviews::reviewsList('guest', $data['property_id'])->orderByDesc('reviews.id')->cursorPaginate(10);

        $data['reviews'] = $reviews->getCollection()->map(function ($review) {
            $temp['reviewer'] = array_combine(
                ['id', 'name', 'profile_image'],
                array_map(function ($v) use ($review) {
                    $temp = $review->{"reviewer_$v"};
                    return $v == 'profile_image' && !$temp ? 'icons/user.svg' : $temp;
                }, ['id', 'name', 'profile_image'])
            );
            return [
                    'id' => $review->id,
                    'message' => $review->message,
                    // 'rating' => $review->rating,
                    'rating' => $review->rating,
                    'cleanliness' => $review->cleanliness,
                    'location' => $review->location,
                    'accuracy' => $review->accuracy,
                    'communication' => $review->communication,
                    'darent_service' => $review->darent_service,
                    'darent_recomended' => $review->darent_recomended,
                    'created_at' => $review->created_at,
                    'review_date' => $review->review_date,
                ] + $temp;
        });

        $data['next_reviews'] = $reviews->nextPageUrl();
        $data['reviews_avg'] = Reviews::avgs('guest', $data['property_id']);
        $data['score'] = ScoringSystem::where('property_id', $data['result']->id)->first();
        // $data['score'] = 0;
        $data['review_counts'] = Reviews::selectRaw('COUNT(*) as total_reviews')
            ->selectRaw('SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star')
            ->selectRaw('SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star')
            ->selectRaw('SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star')
            ->selectRaw('SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star')
            ->selectRaw('SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star')
            ->where('property_id', $data['result']->id)
            ->where('ispublic', 1)
            ->first();

        // Access the counts from the result
        $totalReviewCount = $data['review_counts']->total_reviews;
        $fiveStarCount = $data['review_counts']->five_star;
        $fourStarCount = $data['review_counts']->four_star;
        $threeStarCount = $data['review_counts']->three_star;
        $twoStarCount = $data['review_counts']->two_star;
        $oneStarCount = $data['review_counts']->one_star;

        // $datec = Carbon::parse($data['PropertyReviews'][0]->created_at);
        // dd($datec->format('F-Y'));
        $data['property_type_name'] = propertyTypeName($result->propertyType);
        $data['booked_dates'] = PropertyDates::where('property_id', $id)
            ->where('status', 'Not available')
            ->select('date')
            ->union(
                CustomPricing::where('property_id', $id)
                    ->where('status', 'Not available')
                    ->select('date')
            )
            ->get();
        $data['property_photos'] = array_reduce(array_filter($data['property_photos']->toArray(), function ($item) {
            return $item['cover_photo'] == 1;
        }), function ($acc, $item) {
            array_unshift($acc, $item);
            return $acc;
        }, array_filter($data['property_photos']->toArray(), function ($item) {
            return $item['cover_photo'] != 1;
        }));

        $data['event_data'] = ($SingleProperty->toJson());
        // dd($data['event_data']);
        // pushToDataLayer('view_items', [
        //     'item_id' => $id,
        //     'item_name' => $SingleProperty->name,
        //     // Additional data specific to the "view_items" event
        // ]);
            $userAgent = $request->header('User-Agent');
            $data['showPayOpts'] = strpos($userAgent, 'Safari') == true && strpos($userAgent, 'Chrome') == false && (strpos($userAgent, 'Macintosh') == true || strpos($userAgent, 'iPhone') == true);
        }

        // If we're generating the data (not using cached data), cache it now
        if (!isset($cachedData)) {
            // Cache the data for 5 minutes
            Cache::put($cacheKey, $data, now()->addMinutes(5));
        }

        // Return the appropriate view based on the device type
        if ($this->helper->isRequestFromMobile($request)) {
            // The request is from a mobile browser
            return view('mobile.property.single', $data);
        } else {
            // The request is not from a mobile browser
            return view('property.single', $data);
        }
    }

    public function getAmenities($propertyTypeId)
    {
        $amenities = Amenities::WhereIn('id',[2,4,43,14]) ->get();

        // Format to return amenity id, name, and image
        return response()->json($amenities);
    }


    function contact_host(Request $request)
    {
        $propertyFees = PropertyFees::pluck('value', 'field');
        $data['guest_service_charge'] = $propertyFees['guest_service_charge'];
        $data['property_slug'] = $request->slug;
        $data['result'] = $result = Properties::where('slug', $request->slug)->with('propertyType')->withCount('userPropertyView')->where('status', 'Listed')->where('visibility', 1)->first();
        if (empty($result)) {
            abort('404');
        }

        $data['property_id'] = $id = $result->id;
        $this->helper->trackProductView($data['property_id']);
        $data['property_photos'] = PropertyPhotos::where('property_id', $id)->orderBy('serial', 'asc')->get();
        $data['checkin'] = $request->checkin ?? '';
        $data['checkout'] = $request->checkout ?? '';
        $data['checkin_time'] = Carbon::parse($data['result']['checkinTime'])->format('h:i A');
        $data['checkout_time'] = Carbon::parse($data['result']['checkoutTime'])->format('h:i A');
        $data['adult'] = $request->adult ?? 1;
        $data['child'] = $request->child ?? 0;
        $data['guests'] = $data['adult'] + $data['child'];
        $data['user_wallet'] = NewUserWallet::where('user_id', auth()->id())->first();

        $data['symbol'] = $this->helper->getCurrentCurrencySymbol();
        $data['date_format'] = Settings::getAll()->firstWhere('name', 'date_format_type')->value;
        $SingleProperty = Properties::selectRaw(
            'properties.id, properties.property_code, properties.name, properties.slug,
            pchs.id AS chat_head_id, (pchs.id IS NOT NULL && (pis.id IS NOT NULL ||
            bs.id IS NOT NULL)) AS chat_redirect'
        )
            ->when(
                auth()->check(),
                fn($q) => $q->leftJoin('property_chat_heads AS pchs', fn($q1) => $q1->on('pchs.property_id', 'properties.id')->where('pchs.guest_id', auth()->id()))
                    ->leftJoin('property_inquiries AS pis', fn($q) => $q->on('pis.property_id', 'properties.id')->where('pis.guest_id', auth()->id())->whereIn('pis.status', [PropertyInquiryStatusTypeEnum::Pending->value, PropertyInquiryStatusTypeEnum::Accepted->value])->where('pis.end_date', '>', now()->format('Y-m-d')))
                    ->leftJoin('bookings AS bs', fn($q) => $q->on('bs.property_id', 'properties.id')->where('bs.user_id', auth()->id())->where(fn($q1) => $q1->whereIn('bs.status', ['Processing', 'Pending', 'Accepted'])->orWhere(fn($q2) => $q2->where('bs.booking_type', 'request')->where('bs.status', 'Unpaid')))->where('bs.end_date', '>', now()->format('Y-m-d')))
            )
            ->where('properties.id', $id)->first();
        if (!!$SingleProperty->chat_redirect && !!$SingleProperty->chat_head_id) {
            return redirect()->route('properties.chat.view', ['type' => 'guest', 'chat_head_id' => $SingleProperty->chat_head_id]);
        }
        $SingleProperty->makeHidden($SingleProperty->getAppends());
        $data['property_type_name'] = propertyTypeName($result->propertyType);
        $data['booked_dates'] = PropertyDates::where('property_id', $id)
            ->where('status', 'Not available')
            ->select('date')
            ->union(
                CustomPricing::where('property_id', $id)
                    ->where('status', 'Not available')
                    ->select('date')
            )
            ->get();

        $userAgent = $request->header('User-Agent');
        $data['showPayOpts'] = strpos($userAgent, 'Safari') == true && strpos($userAgent, 'Chrome') == false && (strpos($userAgent, 'Macintosh') == true || strpos($userAgent, 'iPhone') == true);
        if ($SingleProperty->chat_redirect && !!$SingleProperty->chat_head_id) {
            return redirect()->route('properties.chat.view', ['type' => 'guest', 'chat_head_id' => $SingleProperty->chat_head_id]);
        }
        $data['title'] = customTrans('contact_host.contact_host');
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.property.contact_host', $data);
        } else {
            return view('property.contact_host', $data);
        }
    }

    public function currencySymbol(Request $request)
    {
        $symbol = Currency::code_to_symbol($request->currency);
        $data['success'] = 1;
        $data['symbol'] = $symbol;

        return json_encode($data);
    }

    public function photoMessage(Request $request)
    {
        $property = Properties::find($request->id);
        if ($property->host_id == Auth::user()->id) {
            $photos = PropertyPhotos::find($request->photo_id);
            $photos->message = $request->messages;
            $photos->save();
        }

        return json_encode(['success' => 'true']);
    }

    public function photoDelete(Request $request)
    {
        $property = Properties::find($request->id);
        if ($property->host_id == Auth::user()->id) {
            $photos = PropertyPhotos::find($request->photo_id);
            $photos->delete();
            unlink($photos->photo);
        }

        return json_encode(['success' => 'true']);
    }

    public function updateCoverPhoto(Request $request)
    {
        PropertyPhotos::where('property_id', '=', $request->property_id)->update(['cover_photo' => 0]);

        $photos = PropertyPhotos::find($request->photo_id);
        $photos->cover_photo = 1;
        $photos->save();

        // return redirect()->route('property.edit', [$properties->id, 'photos','edit' => '1']);

        return json_encode(['success' => true]);
    }

    public function makePhotoSerial(Request $request)
    {

        $photos = PropertyPhotos::find($request->id);
        $photos->serial = $request->serial;
        $photos->save();

        return json_encode(['success' => 'true']);
    }


    public function set_slug()
    {

        $properties = Properties::where('slug', NULL)->get();
        foreach ($properties as $key => $property) {

            $property->slug = $this->helper->pretty_url($property->name, $property->property_code);
            $property->save();
        }
        return redirect('/');
    }

    public function userBookmark()
    {

        $data['bookings'] = Favourite::with(['properties' => function ($q) {
            $q->with('property_address');
        }])->where(['user_id' => Auth::id(), 'status' => 'Active'])->orderBy('id', 'desc')
            ->paginate(Settings::getAll()->where('name', 'row_per_page')->first()->value);
        return view('users.favourite', $data);
    }

    public function addEditBookMark()
    {
        $property_id = request('id');

        $user_id = request('user_id');
        $favourite = Favourite::where('property_id', $property_id)->where('user_id', $user_id)->first();
        $wishList = Properties::where('id', $property_id)->first(['name', 'slug', 'id']);
        $wishList->makeHidden($wishList->getAppends());

        if (empty($favourite)) {
            $favourite = Favourite::create([
                'property_id' => $property_id,
                'user_id' => $user_id,
                'status' => 'Active',
            ]);
        } else {
            $favourite->status = ($favourite->status == 'Active') ? 'Inactive' : 'Active';
            $favourite->save();
        }
        if ($favourite->status == 'Active') {

            $removeWishList = null;
            $addWishlist = $wishList->toJson();
        } else {
            $addWishlist = null;
            $removeWishList = $wishList->toJson();
        }
        // Track the "Add to Wishlist" event
        if ($favourite->status == 'Active' && Auth::check()) {
            $property = Properties::with(['property_address', 'propertyType', 'property_price'])->find($property_id);
            if ($property) {
                \App\Helpers\MoEngageHelper::trackAddToWishlist(
                    Auth::id(),
                    $property_id,
                    $property->propertyType->name ?? '',
                    $property->property_address->city ?? '',
                    $property->property_price->price ?? '0'
                );
            }
        }


        return response()->json([
            'favourite' => $favourite,
            'addWishlist' => $addWishlist,
            'removeWishList' => $removeWishList,
        ]);
    }


    private function trackAddToWishlistEvent($propertyId)
    {
        $trackingId = 'GTM-MWWQT4N'; // Replace with your Google Analytics Tracking ID
        $clientId = '1234567890'; // Replace with a unique identifier for the user
        $eventCategory = 'Wishlist';
        $eventAction = 'add_to_wishlist';
        $eventLabel = 'Property ID: ' . $propertyId;

        sendEventToGoogleAnalytics($trackingId, $clientId, $eventCategory, $eventAction, $eventLabel);
    }

    public function scattered()
    {
        // if(!g_e_v()) {
        //     return true;
        // }
        // if(!g_c_v()) {
        //     try {
        //         $d_ = g_d();
        //         $e_ = g_e_v();
        //         $e_ = explode('.', $e_);
        //         $c_ = md5($d_ . $e_[1]);
        //         if($e_[0] == $c_) {
        //             p_c_v();
        //             return false;
        //         }
        //         return true;
        //     } catch(\Exception $e) {
        //         return true;
        //     }
        // }
        return false;
    }

    public function propertyStep($prop_id, $columns)
    {
        // dd($columns);
        $count = PropertySteps::where('property_id', $prop_id)->where($columns, 0)->count();
        if ($count) {
            return PropertySteps::where('property_id', $prop_id)->update([$columns => 1]);
        }
        return true;
    }

    public function loadMore(Request $request)
    {
        // dd("function m");
        switch ($request->status) {
            case 'Expired':
                $params = [['created_at', '<', Carbon::yesterday()], ['status', '!=', 'Accepted']];
                break;
            case 'Current':
                $params = [['start_date', '<=', date('Y-m-d')], ['end_date', '>=', date('Y-m-d')], ['status', 'Accepted']];
                break;
            case 'Upcoming':
                $params = [['start_date', '>', date('Y-m-d')], ['status', 'Accepted']];
                break;
            case 'Completed':
                $params = [['end_date', '<', date('Y-m-d')], ['status', 'Accepted']];
                break;
            case 'Pending':
                $params = [['created_at', '>', Carbon::yesterday()], ['status', $request->status]];
                break;
            case 'Unpaid':
                $params = [['created_at', '>', Carbon::yesterday()], ['status', $request->status]];
                break;
            default:
                $params = [];
                break;
        }
        $data['yesterday'] = Carbon::yesterday();
        $data['status'] = $request->status;
        $data['title'] = 'Booking History';
        $data['bookings'] = Bookings::with('host', 'properties')
            ->where('user_id', Auth::user()->id)
            ->where($params)->orderBy('id', 'desc')
            ->skip(request('offset'))->take(10)->get();

        $view = view('pagination.booking_history', $data)->render();

        // $properties = Properties::orderBy('created_at', 'desc');
        return response()->json(['properties' => $view]);
    }

    public function moreBookingRequest(Request $request)
    {
        switch ($request->status) {
            case 'Expired':
                $params = [['created_at', '<', Carbon::yesterday()], ['status', '!=', 'Accepted']];
                break;
            case 'Current':
                $params = [['start_date', '<=', date('Y-m-d')], ['end_date', '>=', date('Y-m-d')], ['status', 'Accepted']];
                break;
            case 'Upcoming':
                $params = [['start_date', '>', date('Y-m-d')], ['status', 'Accepted']];
                break;
            case 'Completed':
                $params = [['end_date', '<', date('Y-m-d')], ['status', 'Accepted']];
                break;
            case 'Pending':
                $params = [['created_at', '>', Carbon::yesterday()], ['status', $request->status]];
                break;
            default:
                $params = [];
                break;
        }

        $data['yesterday'] = Carbon::yesterday();
        $data['status'] = $request->status;
        $data['title'] = "Bookings";
        $data['bookings'] = Bookings::with(['users', 'properties'])
            ->whereHas('users')
            ->where('host_id', Auth::user()->id)
            ->whereIn('status', ['Pending', 'Accepted'])
            ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
            ->orderBy('id', 'desc')
            ->skip(request('offset'))->take(10)->get();

        $view = view('pagination.booking_request', $data)->render();

        return response()->json(['bookingRequest' => $view]);
    }

    public function moreHostOngoingBooking(Request $request)
    {
        $data['bookings'] = Bookings::with('users', 'properties')
            ->where('host_id', Auth::user()->id)
            ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
            ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
            ->where('status', 'Accepted')
            ->orderBy('updated_at', 'desc')
            ->skip(request('offset'))->take(10)->get();

        $view = view('pagination.host_ongoing_booking', $data)->render();

        return response()->json(['ongoingBooking' => $view]);
    }

    public function moreHostCancelledBooking(Request $request)
    {
        $data['bookings'] = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
            ->where(fn($q) => $q->where('status', '=', 'Cancelled')
                ->orWhere('status', '=', 'Declined'))
            ->orderBy('updated_at', 'desc')
            ->skip(request('offset'))->take(10)->get();

        $view = view('pagination.host_cancelled_booking', $data)->render();

        return response()->json(['cancelledBooking' => $view]);
    }

    public function moreHostHistoryBooking(Request $request)
    {
        $data['bookings'] = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
            ->where(fn($q) => $q->where('status', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')))
            ->orderBy('id', 'desc')
            ->skip(request('offset'))->take(10)->get();

        $view = view('pagination.host_history_booking', $data)->render();

        return response()->json(['historyBooking' => $view]);
    }

    public function moreHostExpiredBooking(Request $request)
    {
        $data['bookings'] = Bookings::where('host_id', Auth::user()->id)
            ->where('status', 'Expired')
            ->orderBy('updated_at', 'desc')
            ->skip(request('offset'))->take(10)->get();
        if ($this->helper->isRequestFromMobile($request)) {
            $view = view('mobile.pagination.host_expired_booking', $data)->render();
        } else {
            $view = view('pagination.host_expired_booking', $data)->render();
        }


        return response()->json(['expiredBooking' => $view]);
    }


    public function getprofiledata(Request $request)
    {

        $user = User::find($request->hostid);
        $properties = Properties::where([
            'host_id' => $user->id,
            'visibility' => '1',
            'status' => 'Listed',
            'deleted_at' => null,
        ])->get();
        $createdAtDate = Carbon::createFromFormat('Y-m-d H:i:s', $user->created_at);
        $currentDate = Carbon::now();
        $duration = $createdAtDate->diff($currentDate);
        $years = $duration->y;
        $months = $duration->m;
        $days = $duration->d;

        if ($years > 0) {
            $duration = $years . ($years > 1 ? ' years' : ' year');
        } elseif ($months > 0) {
            $duration = $months . ($months > 1 ? ' mos' : ' mo');
        } else {
            $duration = $days . ($days > 1 ? ' days' : ' day');
        }


        // dd($user->hostreviews);
        $hostreviews = view('ajax-views.bookmodal.host-modal-data-ajax', ['reviews' => $user->hostreviews, 'section' => 'reviews'])->render();
        $userlisting = view('ajax-views.bookmodal.host-modal-data-ajax', ['properties' => $properties, 'section' => 'listing'])->render();

        return response()->json([
            "hostName" => $user->fullName,
            "hostImage" => $user->profile_src,
            "hostRating" => $user->user_rating,
            "hostReviewsCount" => $user->hostreviews->count(),
            "duration" => $duration,
            "hostreviews" => $hostreviews,
            "listings" => $userlisting,
            "location" => $user->location ?? "notset",
            "about" => $user->about ?? "notset",

        ]);
    }
    // public function property_step($prop_id, $columns){
    //     $updates = [];
    //     foreach ($columns as $column) {
    //         $updates[$column] = 1;
    //     }

    //     return PropertySteps::where('property_id', $prop_id)->update($updates);
    // }

    public function stepOne()
    {
        $data['title'] = 'Step One';
        return view('listing.stepOne', $data);
    }

    public function stepTwo()
    {
        $data['title'] = 'Step Two';
        return view('listing.stepTwo', $data);
    }

    public function stepThree()
    {
        $data['title'] = 'Step Three';
        return view('listing.stepThree', $data);
    }

    public function confirmLocation()
    {
        $data['title'] = 'Step Three';
        return view('listing.confirmLocation', $data);
    }

    public function numberofRoom()
    {
        $data['title'] = 'Step Three';
        return view('listing.numberofRoom', $data);
    }

    public function photosUploading()
    {
        $data['title'] = 'Arranging Photos';
        return view('listing.photosUploading', $data);
    }

    public function reviewListing()
    {
        $data['title'] = 'Step Three';
        return view('listing.reviewListing', $data);
    }

    public function setCover()
    {
        $data['title'] = 'Step Three';
        return view('listing.setCover', $data);
    }

    public function hostAgreement(Request $request)
    {
        $data['title'] = 'Host Agreement';
        $data['lang'] = isset($request->lang) ? $request->lang : 'en';
        $data['mobile'] = isset($request->mobile) ? $request->mobile : 0;
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.listing.hostAgreement', $data);
        } else {
            return view('listing.hostAgreement', $data);
        }
        return view('listing.hostAgreement', $data);
    }

    public function importCalendar(Request $request)
    {
        $data['title'] = 'Host Agreement';
        $data['lang'] = isset($request->lang) ? $request->lang : 'en';
        $data['mobile'] = isset($request->mobile) ? $request->mobile : 0;
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.listing.importCalendar', $data);
        } else {
            return view('listing.importCalendar', $data);
        }
        return view('listing.importCalendar', $data);
    }

    public function permit(Request $request)
    {
        $data['title'] = 'Permit';
        $data['lang'] = isset($request->lang) ? $request->lang : 'en';
        return view('listing.permit', $data);
    }

    public function hostListingApi(Request $request)
    {
        try {
            //Search Filter
            $filterText = $request->filter;

            //Rooms and Beds Filters
            $bedrooms = $request->bedrooms;
            $beds = $request->beds;
            $bath = $request->bathrooms;
            $license = $request->license;

            //Amenities Filter
            $amenities = $request->amenities; // Array

            //Status Filter
            $listed = $request->listed;
            $unlisted = $request->unlisted;
            $in_progress = $request->in_progress;
            $status = $request->status;

            if ($request->route()->getName() === 'calendar-listings') {
                $listed = 1;
                $unlisted = 0;
                $in_progress = 0;
            }


            //More Filter
            $instantBookOff = $request->instant_book_off; // boolean
            $listingUpdate = $request->update_required; //boolean

            //Dynamic Pagination
            $rowcount = $request->rowCount ?? 10;
            $sortBy = $request->sortBy ?? 'id';
            $dir = $request->sortOrder ?? 'desc';


            $data = isHostOrCohostQuery(Properties::with(
                'property_price',
                'property_address',
                'property_photos',
                'property_description',
                'property_steps'
            ), auth()->id(), conId: 'id');
            // ->where('host_id', 13);

            if ($bedrooms) $data = $data->where('properties.bedrooms', '>=', $bedrooms);
            if ($beds) $data = $data->where('properties.beds', '>=', $beds);
            if ($bath) $data = $data->where('properties.bathrooms', '>=', $bath);
            if ($amenities) $data = $data->whereIn('properties.amenities', $amenities);

            if ($listed == 1 || $unlisted == 1 || $in_progress == 1) {
                $data = $data->where(function ($query) use ($listed, $unlisted, $in_progress) {
                    if ($listed == 1) $query->orWhere('properties.visibility', 1);
                    if ($unlisted == 1) $query->orWhere(function ($subQuery) {
                        $subQuery->where('properties.visibility', 0)
                            ->whereRaw('properties.id IN (SELECT property_id FROM StepsCompleted WHERE total_steps = 0)');
                    });
                    if ($in_progress == 1) $query->orWhereRaw('properties.id IN (SELECT property_id FROM StepsCompleted WHERE total_steps != 0)');
                });
            }

            if ($license) $data = $license == 1 ? $data->whereNotNull('properties.license_verified_at') : $data->whereNull('properties.license_verified_at');

            if ($instantBookOff == 1) $data = $data->where('properties.booking_type', '!=', 'instant');
            if ($filterText) $data = $data->where(function ($query) use ($filterText) {
                $query->where('name', 'LIKE', "%$filterText%")
                    ->orWhere('property_code', 'LIKE', "%$filterText%")
                    ->orWhereHas('property_address', function ($addressQuery) use ($filterText) {
                        $addressQuery->where('city', 'LIKE', "%$filterText%")
                            ->orWhere('state', 'LIKE', "%$filterText%");
                    });
            });


            // dd($data->orderBy($sortBy, $dir)->toSql());
            $data = $data->orderBy($sortBy, $dir)->paginate($rowcount);
            // dd($data);



            $data->getCollection()->transform(function ($property) {
                // Check if a chat_head exists for the given host, guest, and property
                $property['elmVerify'] = !!ElmDocument::where('user_id', Auth::id())->where('verified_till', '>=', Carbon::now()->format('Y-m-d'))->first();
                return $property;
            });

            return HostListingResource::collection($data);
        } catch (Throwable $th) {
        }

        return response()->json(['error' => $th->getmessage()], 500);

    }

    public function reservationData(Request $request)
    {
        try {
            $type = $request->type;
            if (!in_array($type, ['completed', 'coming', 'ongoing', 'pending', 'cancelled', 'all'])) {
                throw new NotFoundException('Incorrect type givin');
            }
            $from = $request->from;
            $to = $request->to;
            $bookingCode = $request->bookingCode;
            $rowcount = $request->rowCount ?? 10;
            $sortBy = $request->sortBy ?? 'start_date';
            $dir = $request->sortOrder ?? 'desc';
            $status = $type == 'all' ? null : (in_array($type, ['completed', 'coming', 'ongoing']) ? 'Accepted' : ($type == 'pending' ? ['Pending', 'Processing'] : ['Declined', 'Cancelled']));
            $date = $status == 'coming' ? 'start_date >= ?' : ($type == 'completed' ? 'end_date < ?' : ($type == 'ongoing' ? '? BETWEEN start_date AND end_date' : null));
            $flterDate = !!$from && !!$to ? [$from, $to] : $from;

            $bookings = $this->bookingQuery(false)
                ->when(!!$status, fn($q) => $q->{'where' . (!is_array($status) ? '' : 'In')}('status', $status))
                ->when(!!$date, fn($q) => $q->whereRaw($date, [now()->format('Y-m-d')]))
                ->when(!!$flterDate, fn($q) => $q->{'where' . (!is_array($date) ? 'Date' : 'Between')}('created_at', $flterDate))
                ->when(!!$bookingCode, fn($q) => $q->where('code', 'LIKE', "%$bookingCode%"))
                ->orderBy($sortBy, $dir)->paginate($rowcount);

            return YourReservationResource::collection($bookings);
        } catch (Throwable $th) {
            return response()->json(
                [
                    'error' => $th->getmessage(),
                    'trace' => $th->getTrace(),
                ],
                500
            );
        }
    }

    public function exportCalendarLink(Request $request)
    {
        try {

            $propid = $request->propertyId . '.ics';

            $data = [
                "link" => route('icalExport', $propid),
            ];

            return response()->json($data, 200);
        } catch (Throwable $th) {
            return response()->json(['error' => $th->getmessage()], 500);
        }
    }


    public function generatePDF($code)
    {
        $data['booking'] = $booking = Bookings::where('bookings.code', $code)->first();


        $data['date_price'] = json_decode($data['booking']->date_with_price);
        $data['title'] = 'Payment receipt for';
        $data['url'] = url('/') . '/';
        $data['additional_title'] = $code;

        $pdf = PDF::loadView('pages.receipt', $data);

        return $pdf->output();

        // return true;

        // $pdfPath = 'pdfs/' . $code . '.pdf';
        // Storage::put($pdfPath, $pdf->output());

        // return response()->download(storage_path('app/' . $pdfPath));
    }

    public function generateAndDownloadZip(Request $request)
    {

        $from = $request->from;
        $to = $request->to;
        $type = $request->type ?? 'all';
        $rowcount = $request->rowCount ?? 10;
        $sortBy = $request->sortBy ?? 'start_date';
        $dir = $request->dir ?? 'desc';
        $codes = $request->input('codes');

        $zip = new ZipArchive();
        $public_dir = public_path();
        $zipfileName = "public/Invoices.zip";
        $zipFilePath = $public_dir . DIRECTORY_SEPARATOR . $zipfileName;

        // dd($zipFilePath);


        if ($type == 'coming') {
            $data = Bookings::with('users', 'properties')
                ->where('host_id', Auth::user()->id)
                ->where('status', 'Accepted')
                ->where('start_date', '>=', Carbon::now()->format('Y-m-d'));
        } elseif ($type == 'canceled') {

            $data = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
                ->where(fn($q) => $q->where('status', '=', 'Cancelled')
                    ->orWhere('status', '=', 'Declined'))
                ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
                ->where('end_date', '<=', Carbon::now()->format('Y-m-d'));
        } elseif ($type == 'completed') {

            $data = Bookings::with('users', 'properties')->where('host_id', Auth::user()->id)
                ->where(fn($q) => $q->where('status', 'Accepted')
                    ->where('end_date', '<', Carbon::now()->format('Y-m-d')));
        } elseif ($type == 'all') {
            $data = Bookings::with('users', 'properties')
                ->where('host_id', Auth::user()->id);
        } else {
            return false;
        }

        if ($request->from && $request->to) {
            $data = $data->whereBetween('created_at', [
                Carbon::parse($request->from)->format('Y-m-d'),
                Carbon::parse($request->to)->format('Y-m-d'),
            ]);
        } elseif ($request->from) {
            $data = $data->where('created_at', '>=', Carbon::parse($request->from)->format('Y-m-d'));
        } elseif ($request->to) {
            $data = $data->where('created_at', '<=', Carbon::parse($request->to)->format('Y-m-d'));
        }

        $data = $data->orderBy($sortBy, $dir)->paginate($rowcount);
        // dd($data);

        if ($zip->open($zipFilePath, ZipArchive::CREATE) === TRUE) {

            foreach ($data as $booking) {
                // Generate and add the PDF to the ZIP
                $pdfContent = $this->generatePDF($booking->code);
                // dd($pdfContent);

                // // Add the PDF content to the ZIP archive
                $zip->addFromString($booking->code . '.pdf', $pdfContent);
                dump($booking->code);
            }

            $zip->close();
        } else {
            echo 'Failed to create the ZIP archive.';
        }

        // return response()->download($zipFilePath)->deleteFileAfterSend();
        response()->download($zipFilePath);
        dd('--------------- Done ---------------');
        // ->deleteFileAfterSend();
    }

    public function reservationDataExport(Request $request)
    {
        $type = $request->type;
        if (!in_array($type, ['completed', 'coming', 'ongoing', 'pending', 'cancelled', 'all'])) {
            throw new NotFoundException('Incorrect type givin');
        }
        if (!in_array($request->filetype, ['pdf', 'csv'])) {
            return 'csv/pdf "filetype" required!';
        }
        $from = $request->from;
        $to = $request->to;
        $sortBy = $request->sortBy ?? 'start_date';
        $dir = $request->dir ?? 'desc';
        $status = $type == 'all' ? null : (in_array($type, ['completed', 'coming', 'ongoing']) ? 'Accepted' : ($type == 'pending' ? ['Pending', 'Processing'] : 'Declined'));
        $date = $status == 'coming' ? 'start_date >= ?' : ($type == 'completed' ? 'end_date < ?' : ($type == 'ongoing' ? '? BETWEEN start_date AND end_date' : null));
        $flterDate = !!$from && !!$to ? [$from, $to] : $from;
        $bookingCode = $request->bookingCode;

        $bookings = $this->bookingQuery(false)
            ->when(!!$status, fn($q) => $q->{'where' . (!is_array($status) ? '' : 'In')}('status', $status))
            ->when(!!$date, fn($q) => $q->whereRaw($date, [now()->format('Y-m-d')]))
            ->when(!!$flterDate, fn($q) => $q->{'where' . (!is_array($date) ? 'Date' : 'Between')}('created_at', $flterDate))
            ->when(!!$bookingCode, fn($q) => $q->where('code', 'LIKE', "%$bookingCode%"))
            ->orderBy($sortBy, $dir)->get()
            ->when($request->filetype == 'csv', fn($bookings) => $bookings->map(fn($booking) => ([
                'Condition' => 'Guest will ' . (Carbon::parse($booking->end_date) == now() ? '' : 'not') . ' be checking out today',
                'Guests' => $booking->users->fullName,
                'CheckIn' => $booking->start_date,
                'CheckOut' => $booking->end_date,
                'BookedUp' => $booking->created_at->format('d-m-Y'),
                'code' => $booking->code,
                'Address' => $booking->properties->name,
                'totalReturn' => (new Common())->payableToHost($booking)['host_pay'] ?? 0,
            ])));
        if ($request->filetype == 'csv') {
            return Excel::download(new ReservationExport($bookings), 'Reservation Data' . time() . '.xlsx');
        } else {
            view()->share(['data' => [
                'companyLogo' => Settings::where('name', 'logo')->select('value')->first(),
                'type' => $type,
                'logo_flag' => 0,
                'reservationData' => $bookings,
            ]]);
            return PDF::loadView('managehost.reservation_pdf')
                ->download('Reservation Data PDF' . time() . '.pdf', ["Attachment" => 0]);
        }
    }

    function bookingQuery($with = true)
    {
        return isHostOrCohostQuery(Bookings::when($with, fn($q) => $q->with('users', 'properties')), auth()->id());
    }

    public function day(Request $request)
    {
        $data['title'] = customTrans('host_dashboard.day');

        // Track host_dashboard_opened event across all platforms
        if (auth()->check()) {
            $platform = $this->helper->isRequestFromMobile($request) ? 'Mobile' : 'Web';
            \App\Jobs\EventTrackingJob::track(
                'host_dashboard_opened',
                [
                    'host_id' => auth()->id(),
                    'timestamp' => now()->toIso8601String(),
                    'platform' => $platform,
                    'user_agent' => $request->header('User-Agent')
                ],
                auth()->id()
            );
        }
        if ($request->type == 'more') {
            $tab = $request->tab;
            if ($request->tab == 'checkingOutToday') {

                $bookingData = $this->bookingQuery()
                    ->where('status', 'Accepted')
                    ->where('end_date', '=', Carbon::now()->format('Y-m-d'))
                    ->orderBy('id', 'desc')
                    ->paginate(4);
            } elseif ($request->tab == 'ongoingBooking') {
                $bookingData = $this->bookingQuery()
                    ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
                    ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
                    ->where('status', 'Accepted')
                    ->orderBy('updated_at', 'desc')
                    ->paginate(4);
            } elseif ($request->tab == 'arrivingSoon') {
                $bookingData = $this->bookingQuery()
                    ->where(function ($query) {
                        $query->where('start_date', '=', Carbon::now()->addDays(1)->format('Y-m-d'))
                            ->orWhere('start_date', '=', Carbon::now()->addDays(2)->format('Y-m-d'));
                    })
                    ->where('status', 'Accepted')
                    ->orderBy('updated_at', 'desc')
                    ->paginate(4);
            } elseif ($request->tab == 'pendingReviews') {
                $bookingData = Bookings::with('users', 'properties')->where('host_id', auth()->id())
                    ->where('end_date', '<', Carbon::now()->format('Y-m-d'))
                    ->where('status', 'Accepted')
                    ->whereNotExists(function ($query) {
                        $query->select(DB::raw(1))
                            ->from('reviews')
                            ->whereColumn('reviews.booking_id', 'bookings.id')
                            ->where('reviews.sender_id', \Illuminate\Support\Facades\Auth::guard('web')->user()->id);
                    })->WhereExists(function ($booking) {
                        $booking->select(DB::raw(1))
                            ->from('reviews')
                            ->whereColumn('reviews.sender_id', 'bookings.user_id');
                    })
                    ->orderBy('updated_at', 'desc')
                    ->paginate(4);
            } elseif ($request->tab == 'upcomingBooking') {
                $bookingData = $this->bookingQuery()
                    ->where('status', 'Accepted')
                    ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
                    ->orderBy('id', 'desc')
                    ->paginate(4);
            } else {
                // return false;
                dd('else');
            }
            $bookingData->getCollection()->transform(function ($booking) {
                $booking['chat_head_id'] = $this->helper->createOrUpdateChatHead($booking->user_id, $booking->host_id, $booking->property_id);
                return $booking;
            });

            $html = view('managehost.daymore')->with(compact('bookingData', 'tab'))->render();

            return response()->json([
                'html' => $html,
                'tab' => $request->tab,
                'nextUrl' => $bookingData->nextPageUrl(),
                'prevUrl' => $bookingData->previousPageUrl(),
                'currentPage' => $bookingData->currentPage(),
                'hasMorePages' => $bookingData->hasMorePages(),
            ]);
        } else {

            $data['checkingOutToday'] = $this->bookingQuery()
                ->where('status', 'Accepted')
                ->where('end_date', '=', Carbon::now()->format('Y-m-d'))
                ->orderBy('id', 'desc')
                ->paginate();

            $data['ongoingBooking'] = $this->bookingQuery()
                ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
                ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
                ->where('status', 'Accepted')
                ->orderBy('updated_at', 'desc')
                ->paginate();

            $data['arrivingSoon'] = $this->bookingQuery()
                ->where(function ($query) {
                    $query->where('start_date', '=', Carbon::now()->addDays(1)->format('Y-m-d'))
                        ->orWhere('start_date', '=', Carbon::now()->addDays(2)->format('Y-m-d'));
                })
                ->where('status', 'Accepted')
                ->orderBy('updated_at', 'desc')
                ->paginate();

            $data['pendingReviews'] = Bookings::with('users', 'properties')->where('host_id', auth()->id())
                ->where('end_date', '<', Carbon::now()->format('Y-m-d'))
                ->where('status', 'Accepted')
                ->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('reviews')
                        ->whereColumn('reviews.booking_id', 'bookings.id')
                        ->where('reviews.sender_id', \Illuminate\Support\Facades\Auth::guard('web')->user()->id);
                })->WhereExists(function ($booking) {
                    $booking->select(DB::raw(1))
                        ->from('reviews')
                        ->whereColumn('reviews.sender_id', 'bookings.user_id');
                })
                ->orderBy('updated_at', 'desc')
                ->get();

            $data['upcomingBooking'] = $this->bookingQuery()
                ->where('status', 'Accepted')
                ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
                ->orderBy('id', 'desc')
                ->paginate();
            $data['all_reservation_count'] = $this->bookingQuery()->count();
        }
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.day', $data);
        } else {
            return view('managehost.day', $data);
        }
    }

    function propertyCalendar(Request $request, $property_code)
    {
        $data['lang'] = LocalizationKeyword::select(app()->getLocale() . '_value AS value')->where('parent_key', 'host_reservation')->first()->value;
        $property = isHostOrCohostQuery(Properties::query(), auth()->id(), conId: 'id')->where('property_code', $property_code)->firstOrFail();
        $data['property_id'] = $property->id;
        $data['is_mob'] = $this->helper->isRequestFromMobile($request);
        return view('managehost.host_full_calendar', $data);
    }

    public function propertyLandingPage(Request $request){
            $data['lang'] = isset($request->lang) ? $request->lang : 'en';
            $data['title'] = 'Property Landing Page';
            $data['cities'] = City::all();
            return view('listing.propertyLandingPage', $data);
    }
}
