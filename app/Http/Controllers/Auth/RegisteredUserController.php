<?php

namespace App\Http\Controllers\Auth;

use App\Data\RegisterData;
use App\Data\UserData;
use App\Http\Controllers\Controller;
use App\Models\PhoneVerification;
use App\Models\User;
use App\Otp\LoginOtp;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;
use SadiqSalau\LaravelOtp\Facades\Otp;

class RegisteredUserController extends Controller
{

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(RegisterData $request)
    {
        $user = User::create([
            'uuid' => Str::uuid(),
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'phone' => $request->phone,
            'old_id' => '',
        ]);
        $phone = $request->phone;
        $otp = Otp::identifier($phone)->send(
            new LoginOtp(
                phone: $phone
            ),
            Notification::route('unifonic', $phone )
        );
        PhoneVerification::updateOrCreate(['phone' => $phone],[
            'phone' => $phone,
            'otp' => $otp['code'],
            'uuid' => Str::uuid(),
            'guest_uuid' => request('guest_uuid') ?? request()->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null,
        ]);

        // Track guest_registered event across all platforms
        \App\Jobs\EventTrackingJob::trackGuestRegistered(
            $user->id,
            'web',
            'web'
        );

        return response()->json(["message" => Otp::OTP_SENT]);
    }
}
