<?php

namespace App\Http\Controllers\Auth;

use App\Data\LoginData;
use App\Http\Controllers\Controller;
use App\Models\PhoneVerification;
use App\Models\User;
use App\Otp\LoginOtp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;
use SadiqSalau\LaravelOtp\Facades\Otp;

class AuthenticatedSessionController extends Controller
{

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginData $request)
    {
        $phone = $request->phone;
        $user = User::where('phone', $phone)->first();
        if(empty($user)){
            return response()->json(['register'=>true, "message"=>trans("messages.user_not_found_please_register"), "phone"=>$request->phone], 404);
        }
        $otp = Otp::identifier($phone)->send(
            new LoginOtp(
                phone: $phone
            ),
            Notification::route('unifonic', $phone )
        );
        PhoneVerification::updateOrCreate(['phone' => $phone],[
            'phone' => $phone,
            'otp' => $otp['code'],
            'uuid' => Str::uuid(),
            'guest_uuid' => request('guest_uuid') ?? request()->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null
        ]);

        return response()->json(["message" => Otp::OTP_SENT]);
    }

    public function resendOtp(LoginData $request)
    {
        $phone = $request->phone;
        $type = ucfirst($request->type ?? "login");
        $user = User::where('phone', $phone)->first();
        if(empty($user)){
            return response()->json(["message"=>trans("messages.user_not_found_please_register")], 404);
        }
        $otpClass = '\App\Otp\\'.$type.'Otp';
        $otp = Otp::identifier($phone)->send(
            new $otpClass(
                phone: $phone
            ),
            Notification::route('unifonic', $phone )
        );
        PhoneVerification::updateOrCreate(['phone' => $phone],[
            'phone' => $phone,
            'otp' => $otp['code'],
            'uuid' => Str::uuid(),
            'guest_uuid' => request('guest_uuid') ?? request()->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null
        ]);

        return response()->json(["message" => Otp::OTP_SENT]);
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request)
    {
        $user = Auth::user();

        // Track guest_session_end event in MoEngage before logging out
        if ($user) {
            // Calculate session duration in seconds
            $sessionStartTime = session('login_time', now()->subMinutes(1));
            $sessionDuration = now()->diffInSeconds($sessionStartTime);

            \App\Jobs\EventTrackingJob::trackGuestSessionEnd(
                $user->id,
                $sessionDuration,
                'logout',
                now()->toIso8601String()
            );
        }

        $user->tokens()->delete();

        return response()->json(["message" =>'Logged out successfully']);
    }
}
