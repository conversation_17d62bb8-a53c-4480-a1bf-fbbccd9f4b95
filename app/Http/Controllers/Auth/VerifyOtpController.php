<?php

namespace App\Http\Controllers\Auth;

use App\Data\OtpData;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use SadiqSalau\LaravelOtp\Facades\Otp;
class VerifyOtpController extends Controller
{
    /**
     * Mark the authenticated user's email address as verified.
     */
    public function verifyOtp(OtpData $request)
    {
        $otp = Otp::identifier($request->phone)->attempt($request->code);
        if($otp['status'] != Otp::OTP_PROCESSED)
        {
            return response()->json(['message'=>$otp['status']],403);
        }

        // Get the user from the result
        $user = $otp['result']->original['data']['user'] ?? null;

        // Track guest_app_opened event in MoEngage if user is found
        if ($user) {
            // Store login time in session for calculating session duration at logout
            session(['login_time' => now()]);

            \App\Jobs\EventTrackingJob::trackGuestAppOpened(
                $user->id,
                request()->header('User-Agent') ?? 'web',
                '1.0',
                now()->toIso8601String()
            );
        }

        return $otp['result'];
    }
}
