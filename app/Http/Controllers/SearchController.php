<?php

namespace App\Http\Controllers;

use App;
use App\Http\Helpers\Common;
use App\Http\Resources\SearchPropertyCollection;
use App\Models\{
    Amenities,
    AmenityType,
    Currency,
    GridProperty,
    Properties,
    PropertyPhotos,
    PropertyType,
    Settings,
    SpaceType,
    PropertyCacheResult,
    UserSearch
};
use Auth;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session as FacadesSession;
use Session;
use Illuminate\Pagination\LengthAwarePaginator;

class SearchController extends Controller
{
    private $helper;
    private $default_city;

    public function __construct()
    {
        $this->helper = new Common;
        $this->default_city = app('DEFAULT_CITY');
    }

    public function lpRiyadh(Request $request)
    {
        return $this->index($request);
    }

    public function index(Request $request)
    {
        $location = $request->input('location') ?? 'riyadh';
        if ($request->headerdaterange) {
            $dataexplode = explode(" - ", $request->headerdaterange);
            $req_checkin = $dataexplode[0];
            $req_checkout = $dataexplode[1];
        }

        $cityData = DB::table('cities')->where('name', $location)->first();
        $data['lat'] = $cityData->latitude;
        $data['long'] = $cityData->longitude;
        $data['zoom'] = 4;

        $data['location'] = $location;
        $data['checkin'] = $req_checkin ?? '';
        $data['checkout'] = $req_checkout ?? '';

        $from = Carbon::parse($data['checkin'])->format('m/d/Y');
        $to = Carbon::parse($data['checkout'])->format('m/d/Y');

        $to = adDaysinDate($from, $to, 1); // ifsame then add

        Session::put('header_checkin', $from ?? '');
        Session::put('header_checkout', $to ?? '');

        $data['searchNights'] = Carbon::parse($from)->diffInDays($to);
        $data['guest'] = $request->input('adult_guest') + $request->input('child_guest');

        $data['adult_guest'] = $request->input('adult_guest');
        Session::put('adult_guest_session', $data['adult_guest'] ?? '');
        $data['child_guest'] = $request->input('child_guest');
        Session::put('child_guest_session', $data['child_guest'] ?? '');

        $data['bedrooms'] = $request->input('bedrooms');
        $data['beds'] = $request->input('beds');
        $data['bathrooms'] = $request->input('bathrooms');
        $data['min_price'] = $request->input('min_price');
        $data['max_price'] = $request->input('max_price');

        // $data['space_type'] = SpaceType::getAll()->where('status', 'Active')->pluck('name_ar', 'id', 'name_ar');
        $data['space_type'] = SpaceType::getAll()->where('status', 'Active');
        $data['property_type'] = PropertyType::getAll()->where('status', 'Active');
        $data['amenities'] = Amenities::where('status', 'Active')->where('type_id', '!=', 3)->get();
        $data['amenities_type'] = AmenityType::pluck('name', 'id');

        $data['property_type_selected'] = explode(',', $request->input('property_type') ?? '');

        $data['TypeName'] = null;
        if (!empty($request->input('property_type'))) {
            $TypeName = PropertyType::where('id', $data['property_type_selected'])->first('name')->toJson();
        }
        $data['citySelection'] = ['city' => $location];
        $data['amenities_selected'] = explode(',', $request->input('a') ?? '');

        $data['space_type_selected'] = explode(',', $request->input('space_type') ?? '');

        $currency = Currency::getAll();
        if (Session::get('currency')) $data['currency_symbol'] = $currency->firstWhere('code', Session::get('currency'))->symbol;
        else $data['currency_symbol'] = $currency->firstWhere('default', 1)->symbol;
        $minPrice = Settings::getAll()->where('name', 'min_search_price')->first()->value;
        // $maxPrice = Settings::getAll()->where('name', 'max_search_price')->first()->value;
        $maxPrice = DB::table('property_price')->max('price');
        $data['default_min_price'] = $this->helper->convert_currency(Currency::getAll()->firstWhere('default')->code, '', $minPrice);
        $data['default_max_price'] = $this->helper->convert_currency(Currency::getAll()->firstWhere('default')->code, '', $maxPrice);
        if (!$data['min_price']) {
            $data['min_price'] = $data['default_min_price'];
            $data['max_price'] = $data['default_max_price'];
        }

        if ($request->discoverall) {
            $data['title'] = "Discover All";
        }

        $data['date_format'] = Settings::getAll()->firstWhere('name', 'date_format_type')->value;
        if ($this->helper->isRequestFromMobile($request)) {
            // The request is from a mobile browser
            // Your code here
            // return view('home.mobile.home', $data);
            return view('mobile.search.view', $data);
        } else {
            // The request is not from a mobile browser
            // Your code here
            return view('search.view', $data);
        }
    }


    function searchResultV3(Request $request)
    {
        // Create a cache key based only on the relevant search parameters
        $searchParams = [
            'location' => $request->input('location'),
            'checkin' => $request->input('checkin'),
            'checkout' => $request->input('checkout'),
            'bedrooms' => $request->input('bedrooms'),
            'beds' => $request->input('beds'),
            'bathrooms' => $request->input('bathrooms'),
            'property_type' => $request->input('property_type'),
            'space_type' => $request->input('space_type'),
            'amenities' => $request->input('amenities'),
            'book_type' => $request->input('book_type'),
            'min_price' => $request->input('min_price'),
            'max_price' => $request->input('max_price'),
            'sortBy' => $request->input('sortBy'),
            'searchNights' => $request->input('searchNights'),
            'guest' => $request->input('guest'),
            'adult_guest' => $request->input('adult_guest'),
            'child_guest' => $request->input('child_guest'),
            'property_code' => $request->input('property_code'),
            'districts' => $request->input('districts'),
            'page' => $request->input('page', 1),
        ];
        $cacheKey = 'search_result_v3_' . md5(json_encode($searchParams));

        // Try to get the cached result
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult) {
            return $cachedResult;
        }

        if (Session::get('language')) {
            App::setLocale(Session::get('language'));
        } else {
            App::setLocale('en');
        }

        $lang = Session::get('language');
        $location = $request->input('location');
        $checkin = $request->input('checkin');
        $checkout = $request->input('checkout');
        $bedrooms = $request->input('bedrooms');
        $beds = $request->input('beds');
        $bathrooms = $request->input('bathrooms');
        $property_type = $request->input('property_type');
        $space_type = $request->input('space_type');
        $amenities = $request->input('amenities');
        $book_type = $request->input('book_type');
        $min_price = $request->input('min_price');
        $max_price = $request->input('max_price');
        $sortby = $request->input('sortBy');
        $searchNights = $request->input('searchNights');
        $guest = $request->input('guest');
        $unit_code = $request->input('property_code');

        // set requires params for single property page in session instead of query params
        Session::put('header_checkin', $checkin);
        Session::put('header_checkout', $checkout);
        Session::put('adult_guest_session', $guest);

        $adult_guest = $request->input('adult_guest');

        if (!is_array($property_type)) {
            if ($property_type != '') {
                $property_type = explode(',', $property_type);
            } else {
                $property_type = [];
            }
        }

        if (!is_array($space_type)) {
            if ($space_type != '') {
                $space_type = explode(',', $space_type);
            } else {
                $space_type = [];
            }
        }

        if (!is_array($book_type)) {
            if ($book_type != '') {
                $book_type = explode(',', $book_type);
            } else {
                $book_type = [];
            }
        }
        if (!is_array($amenities)) {
            if ($amenities != '') {
                $amenities = explode(',', $amenities);
            } else {
                $amenities = [];
            }
        }

        $property_type_val = [];
        $properties_whereIn = [];

        $propertyObj = new Properties();

        $cityData = DB::table('cities')->where('name', $location)->first();
        $minLat = $cityData->latitude - $propertyObj::LAT_LONG_INCREMENT;
        $maxLat = $cityData->latitude + $propertyObj::LAT_LONG_INCREMENT;
        $minLong = $cityData->longitude - $propertyObj::LAT_LONG_INCREMENT;
        $maxLong = $cityData->longitude + $propertyObj::LAT_LONG_INCREMENT;

        // ----------This Block is specific for Multi Select listing in filter modal -> Start ----------

        if ($lang == 'ar') {
            $districtsMulti = DB::table('districts')->where('city_id', $cityData->id)->pluck('name_ar');
        } else {
            $districtsMulti = DB::table('districts')->where('city_id', $cityData->id)->pluck('name');
        }
        // ----------This Block is specific for Multi Select listing in filter modal -> End ----------

        $checkin = Carbon::parse($checkin)->format('Y-m-d');
        $checkout = Carbon::parse($checkout)->format('Y-m-d');
        $days = $this->helper->get_days($checkin, $checkout);
        unset($days[count($days) - 1]);
        $not_available_property_ids = DB::table('property_dates')
            ->whereIn('date', $days)
            ->where('status', 'Not available')
            ->distinct()
            ->pluck('property_id');

        $custom_pricing_property_ids = DB::table('custom_pricing')
            ->whereIn('date', $days)
            ->where('status', 'Not available')
            ->distinct()
            ->pluck('property_id');

        $not_available_property_ids = $not_available_property_ids->merge($custom_pricing_property_ids)->unique();
        $properties_where['properties.status'] = 'Listed';
        $properties_where['properties.visibility'] = '1';
        $properties_where['properties.min_nights'] = $searchNights;
        if (Auth::check()) {
            $properties_where['properties.host_id'] = Auth::id();
        }
        if (isset($bedrooms) && $bedrooms !== 'Any') {
            $properties_where['properties.bedrooms'] = $bedrooms;
        }

        if (isset($adult_guest)) {
            $properties_where['properties.adult_guest'] = $adult_guest;
        }

        if (isset($bathrooms) && $bathrooms !== 'Any') {
            $properties_where['properties.bathrooms'] = $bathrooms;
        }

        if (isset($beds) && $beds !== 'Any') {
            $properties_where['properties.beds'] = $beds;
        }

        if (count($space_type)) {
            $properties_whereIn['properties.space_type'] = $space_type;
        }

        if (count($property_type)) {
            $properties_whereIn['properties.property_type'] = $property_type;
        }
        $address_condition = 'property_address.latitude between ' . $minLat . ' and ' . $maxLat . ' and property_address.longitude between ' . $minLong . ' and ' . $maxLong;
        $users_condition = "users.status = 'Active'";

        // ----------------------------------------------------Query------------------------------------------------------------------------------------
        $properties = $propertyObj::with('property_photos')
            ->select(
                array_merge(
                    $propertyObj::SEARCH_COLUMNS_WEB,
                    [
                        'properties_pricing.number_of_days as number_of_days',
                        'properties_pricing.day_price as day_price',
                        'properties_pricing.total_price as total_price',
                        'properties_pricing.before_discount as before_discount',
                    ]
                )
            )
            ->selectSub(function ($q) use ($minLat, $minLong) {
                $q->selectRaw("6371 * acos(cos(radians(" . $minLat . "))
                * cos(radians(property_address.latitude))
                * cos(radians(property_address.longitude) - radians(" . $minLong . "))
                + sin(radians(" . $minLat . "))
                * sin(radians(property_address.latitude)))");
            }, 'distance')
            ->selectSub(function ($q) {
                $q->selectRaw('COUNT(user_property_views.property_id) AS view_count')
                    ->from('user_property_views')
                    ->whereRaw('user_property_views.property_id = properties.id');
            }, 'views_count')
            ->addSelect(DB::raw('IF(properties.id IN (' . $not_available_property_ids->implode(',') . '), 0, 1) AS available'))
            ->join('property_type', 'properties.property_type', '=', 'property_type.id')
            ->join('property_description', 'properties.id', '=', 'property_description.property_id')
            ->join('property_address', 'properties.id', '=', 'property_address.property_id')
            ->join('users', 'properties.host_id', '=', 'users.id')
            ->join('property_price', 'properties.id', '=', 'property_price.property_id')
            ->join('scoring_system', 'properties.id', '=', 'scoring_system.property_id')
            ->leftJoin('cities', 'property_address.city_id', '=', 'cities.id')
            ->when($request->exists('districts'), function ($query) {
                return $query->join('districts', 'property_address.district_id', '=', 'districts.id');
            })
            ->leftJoin('property_discounts', 'properties.id', '=', 'property_discounts.property_id')
            ->whereRaw($address_condition)
            ->whereRaw($users_condition)
            ->whereRaw('properties.slug != ""')
            ->where('property_address.city', $location)
            ->where('properties.status', 'Listed')
            ->where('properties.visibility', 1)
            ->when($unit_code, function ($query, $unit_code) {
                return $query->where('properties.property_code', $unit_code);
            })
            ->orderBy('available', 'desc');

        if ($properties_where) {
            foreach ($properties_where as $row => $value) {
                if ($row == 'properties.adult_guest') {
                    $operator = '>=';
                } else if (Auth::check() && $row == 'properties.host_id') {
                    $operator = '!=';
                } else if ($row == 'properties.min_nights') {
                    $operator = '<=';
                } else {
                    $operator = '=';
                }
                $properties = $properties->where($row, $operator, $value);
            }
        }

        if ($properties_whereIn) {
            foreach ($properties_whereIn as $row_properties_whereIn => $value_properties_whereIn) {
                $properties = $properties->whereIn(
                    $row_properties_whereIn,
                    array_values($value_properties_whereIn)
                );
            }
        }

        if (count($amenities)) {
            foreach ($amenities as $amenities_value) {
                $properties = $properties->whereRaw('FIND_IN_SET(' . $amenities_value . ', properties.amenities)');
            }
        }

        if ($request->property_code) {
            $properties = $properties->where('properties.property_code', $request->property_code);
        }

        if ($request->exists('districts')) {
            if (count($request->districts) > 0) {
                if ($lang == 'ar') {
                    $properties = $properties->whereIn('districts.name_ar', $request->districts);
                } else {
                    $properties = $properties->whereIn('districts.name', $request->districts);
                }
            }
        }

        $period = CarbonPeriod::create(Carbon::parse($checkin), Carbon::parse($checkout));
        $chunks = array_chunk(iterator_to_array($period), 14); // Split into 14-day chunks

        $queries = [];

        foreach ($chunks as $index => $chunk) {
            $startDate = reset($chunk)->format('Y-m-d');
            $endDate = end($chunk)->format('Y-m-d');

            // Add a 1-day overlap, except for the last chunk
            if ($index !== count($chunks) - 1) {
                $endDate = Carbon::parse($endDate)->addDay()->format('Y-m-d');
            }

            // Get the query for each chunk
            $results[] = $this->helper->getPropertiesPricing($startDate, $endDate);

        }

        // Combine all queries with UNION ALL
        $combinedQuery = implode(" UNION ALL ", $results);

        // Wrap in a final SELECT to handle duplicates
        $properties_pricing = "SELECT DISTINCT * FROM ({$combinedQuery}) as combined_pricing";


        $pagination = $properties
            ->joinSub($properties_pricing, 'properties_pricing', function (JoinClause $join) {
                $join->on('properties.id', '=', 'properties_pricing.property_id');
            })
            ->whereBetween('properties_pricing.day_price', [$min_price, $max_price])
            ->when(
                !$sortby,
                fn($q) => $q->orderBy('properties.priority', 'desc') // DEFAULT CHILD ORDER BY PRIORITY
            )
            ->when(
                $sortby,
                fn($q) => match ($sortby) {
                    'mostRatedFirst' => $q->orderBy('properties.rating_avg', 'desc'),
                    'nearestToCity' => $q->orderBy('distance', 'asc'),
                    'asc' => $q->orderBy('properties_pricing.total_price', 'asc'),
                    'desc' => $q->orderBy('properties_pricing.total_price', 'desc')
                }
            )
            ->orderBy('property_score_total_points', 'desc')
            ->orderBy('properties.id', 'desc');
        $allPropertyIds = (clone $pagination)->pluck('properties.id')->toArray();
        $availablePropertyIds = array_diff($allPropertyIds, $not_available_property_ids->toArray());
        $notAvailablePropertyIds = array_intersect($allPropertyIds, $not_available_property_ids->toArray());
        $totalAvailableCount = count($availablePropertyIds);
        $totalNotAvailableCount = count($notAvailablePropertyIds);
        $pagination = $pagination->groupBy('properties.id')->paginate($propertyObj::PER_PAGE);
        $data['TypeName'] = null;
        if (!empty($property_type_val) && isset($property_type_val)) {
            $data['TypeName'] = PropertyType::whereIn('id', $property_type_val)->first('name')->toJson();
        }
        $data['citySelection'] = ['city' => $cityData->name];

        if ($this->helper->isRequestFromMobile($request)) {

            $htmlView = view('mobile.search.ajax-result', ['search_result' => $pagination])->renderSections();
        } else {
            $htmlView = view('search.ajax-result', ['search_result' => $pagination])->renderSections();
        }

        $searchData = [
            'location' => $cityData?->name,
            'checkin' => $checkin,
            'checkout' => $checkout,
            'bedrooms' => $bedrooms,
            'beds' => $beds,
            'bathrooms' => $bathrooms,
            'property_type' => $property_type,
            'space_type' => $space_type,
            'amenities' => $amenities,
            'book_type' => $book_type,
            'min_price' => $min_price,
            'max_price' => $max_price,
            'sortby' => $sortby,
            'searchNights' => $searchNights,
            'unit_code' => $unit_code,
        ];

        App\Models\UserSearch::query()->create([
            'user_id' => auth()->user()?->id,
            'session_id' => FacadesSession::getId(),
            'search_query' => json_encode($searchData),
            'total_available_properties_count' => $totalAvailableCount,
            'total_not_available_properties_count' => $totalNotAvailableCount,
            'available_properties_ids' => json_encode($pagination->where('available', '=', 1)->pluck('id')->toArray()),
            'not_available_properties_ids' => json_encode($pagination->where('available', '=', 0)->pluck('id')->toArray()),
            'platform' => DESKTOP_APP_KEY_NAME,
            'guest_uuid' => request('guest_uuid') ?? request()->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null
        ]);

        $response = response()->json([
            'pagedata' => $pagination,
            'proerties_html' => $htmlView['properties_data'],
            'eventData' => $data,
            'current_count' => $pagination->total(),
            'districts' => $districtsMulti,
        ]);
        $property_ids = $pagination->pluck('id')->toArray();

        if(!empty($property_ids)){
            $data = [];
            foreach ($property_ids as $property_id){
                $data[]=[
                    'property_id' => $property_id,
                    'cache_key' => $cacheKey,
                    'created_at' => now()->toDateTimeString(),
                ];
            }
            PropertyCacheResult::insert($data);
            Cache::put($cacheKey, $response, now()->addMinutes(30));
        }

        return $response;
    }

    public function searchResultV4(Request $request)
    {
        $params = $request->only([
            'location', 'checkin', 'checkout', 'bedrooms', 'beds', 'bathrooms',
            'property_type', 'space_type', 'amenities', 'book_type',
            'min_price', 'max_price', 'sortBy', 'searchNights', 'guest',
            'adult_guest', 'child_guest', 'property_code', 'page', 'districts'
        ]);
        $cacheKey = 'search_result_v4_' . md5(json_encode($params));
        if ($cached = Cache::get($cacheKey)) return $cached;

        App::setLocale(Session::get('language', 'en'));
        $lang = Session::get('language', 'en');

        // Inputs
        $location = $request->input('location');
        $checkin = Carbon::parse($request->input('checkin'))->format('Y-m-d');
        $checkout = Carbon::parse($request->input('checkout'))->format('Y-m-d');
        $days = $this->helper->get_days($checkin, $checkout);
        array_pop($days); // remove last date

        // Session updates
        Session::put([
            'header_checkin' => $checkin,
            'header_checkout' => $checkout,
            'adult_guest_session' => $request->input('adult_guest')
        ]);

        // Geo filter
        $cityData = Cache::remember("city_bounds_{$location}", 60, function () use ($location) {
            return DB::table('cities')->where('name', $location)->first();
        });

        $inc = Properties::LAT_LONG_INCREMENT;
        [$minLat, $maxLat, $minLong, $maxLong] = [
            $cityData->latitude - $inc, $cityData->latitude + $inc,
            $cityData->longitude - $inc, $cityData->longitude + $inc
        ];

        $column = $lang === 'ar' ? 'name_ar' : 'name';
        $districtsMulti = Cache::remember("districts_{$cityData->id}_{$lang}", 10, function () use ($cityData, $column) {
            return DB::table('districts')->where('city_id', $cityData->id)->pluck($column);
        });

        // Cache not available property IDs
        $not_available_property_ids = Cache::remember("not_available_ids_" . md5(json_encode($days)), 10, function () use ($days) {
            return DB::table('property_dates')->select('property_id')
                ->whereIn('date', $days)->where('status', 'Not available')
                ->union(
                    DB::table('custom_pricing')->select('property_id')
                        ->whereIn('date', $days)->where('status', 'Not available')
                )->distinct()->pluck('property_id');
        });

        $propertyObj = Properties::query()
            ->with('property_photos')
            ->select(Properties::SEARCH_COLUMNS_WEB_OPTIMIZED)
            ->addSelect(DB::raw("
                6371 * acos(
                    cos(radians($minLat)) * cos(radians(property_address.latitude)) *
                    cos(radians(property_address.longitude) - radians($minLong)) +
                    sin(radians($minLat)) * sin(radians(property_address.latitude))
                ) AS distance
            "))
            ->selectSub($this->viewsSubquery(), 'views_count')
            ->addSelect(DB::raw(
                'IF(properties.id IN (' . $not_available_property_ids->implode(',') . '), 0, 1) AS available'
            ))
            ->join('property_type', 'properties.property_type', '=', 'property_type.id')
            ->join('property_description', 'properties.id', '=', 'property_description.property_id')
            ->join('property_address', 'properties.id', '=', 'property_address.property_id')
            ->join('users', 'properties.host_id', '=', 'users.id')
            ->join('property_price', 'properties.id', '=', 'property_price.property_id')
            ->join('scoring_system', 'properties.id', '=', 'scoring_system.property_id')
            ->leftJoin('cities', 'property_address.city_id', '=', 'cities.id')
            ->leftJoin('property_discounts', 'properties.id', '=', 'property_discounts.property_id')
            ->when($request->exists('districts'), function ($query) {
                $query->join('districts', 'property_address.district_id', '=', 'districts.id');
            })
            ->whereRaw("property_address.latitude BETWEEN ? AND ?", [$minLat, $maxLat])
            ->whereRaw("property_address.longitude BETWEEN ? AND ?", [$minLong, $maxLong])
            ->whereRaw("users.status = 'Active'")
            ->where('property_address.city', $location)
            ->where('properties.status', 'Listed')
            ->where('properties.visibility', 1)
            ->whereNotNull('properties.slug')
            ->where('properties.slug', '!=', '')
            ->orderByDesc('available');

        // Filters
        $this->applyPropertyFilters($propertyObj, $request->all());

        // Add pricing logic (replacing master_availability dependency)
        $period = CarbonPeriod::create(Carbon::parse($checkin), Carbon::parse($checkout));
        $chunks = array_chunk(iterator_to_array($period), 14); // Split into 14-day chunks

        $results = [];
        foreach ($chunks as $index => $chunk) {
            $startDate = reset($chunk)->format('Y-m-d');
            $endDate = end($chunk)->format('Y-m-d');

            // Add a 1-day overlap, except for the last chunk
            if ($index !== count($chunks) - 1) {
                $endDate = Carbon::parse($endDate)->addDay()->format('Y-m-d');
            }

            // Get the query for each chunk
            $results[] = $this->helper->getPropertiesPricing($startDate, $endDate);
        }

        // Combine all queries with UNION ALL
        $combinedQuery = implode(" UNION ALL ", $results);

        // Wrap in a final SELECT to handle duplicates
        $properties_pricing = "SELECT DISTINCT * FROM ({$combinedQuery}) as combined_pricing";

        $pagination = $propertyObj
            ->joinSub($properties_pricing, 'properties_pricing', function (JoinClause $join) {
                $join->on('properties.id', '=', 'properties_pricing.property_id');
            })
            ->whereBetween('properties_pricing.day_price', [$request->input('min_price', 0), $request->input('max_price', 999999)])
            ->when(
                !$request->input('sortBy'),
                fn($q) => $q->orderBy('properties.priority', 'desc') // DEFAULT CHILD ORDER BY PRIORITY
            )
            ->when(
                $request->input('sortBy'),
                fn($q) => match ($request->input('sortBy')) {
                    'mostRatedFirst' => $q->orderBy('properties.rating_avg', 'desc'),
                    'nearestToCity' => $q->orderBy('distance', 'asc'),
                    'asc' => $q->orderBy('properties_pricing.total_price', 'asc'),
                    'desc' => $q->orderBy('properties_pricing.total_price', 'desc')
                }
            )
            ->orderBy('property_score_total_points', 'desc')
            ->orderBy('properties.id', 'desc')
            ->groupBy('properties.id')
            ->simplePaginate(Properties::PER_PAGE);

        // Transform properties - use pricing data from the join
        $number_of_days = Carbon::parse($checkin)->diffInDays(Carbon::parse($checkout));
        $dateRange = CarbonPeriod::create($checkin, Carbon::parse($checkout)->subDay());
        $pagination->getCollection()->transform(function ($property) use ($dateRange, $number_of_days) {
            // Use the pricing data from the join instead of recalculating
            $property->total_before_discount = $property->before_discount ?? $property->total_price;
            $property->total_after_discount = $property->total_price;
            $property->number_of_days = $number_of_days;
            return $property;
        });

        // Count available
        $allPropertyIds = $pagination->pluck('id');
        $availablePropertyIds = $allPropertyIds->diff($not_available_property_ids);
        $notAvailablePropertyIds = $allPropertyIds->intersect($not_available_property_ids);

        // Prepare response
        $viewData = [
            'search_result' => $pagination,
            'number_of_days' => Carbon::parse($checkin)->diffInDays(Carbon::parse($checkout)),
            'date_range' => $dateRange
        ];
        $htmlView = view(
            $this->helper->isRequestFromMobile($request) ? 'mobile.search.ajax-result' : 'search.ajax-result',
            $viewData
        )->renderSections();

        UserSearch::create([
            'user_id' => auth()->user()?->id,
            'session_id' => Session::getId(),
            'search_query' => json_encode($params),
            'total_available_properties_count' => $availablePropertyIds->count(),
            'total_not_available_properties_count' => $notAvailablePropertyIds->count(),
            'available_properties_ids' => json_encode($availablePropertyIds),
            'not_available_properties_ids' => json_encode($notAvailablePropertyIds),
            'platform' => DESKTOP_APP_KEY_NAME,
            'guest_uuid' => request('guest_uuid') ?? request()->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null
        ]);

        $response = response()->json([
            'pagedata' => $pagination,
            'proerties_html' => $htmlView['properties_data'],
            'eventData' => ['city' => $cityData->name],
            'current_count' => $pagination->total(),
            'districts' => $districtsMulti,
        ]);

        Cache::put($cacheKey, $response, now()->addMinutes(30));

        return $response;
    }

    protected function viewsSubquery()
    {
        return DB::table('user_property_views')
            ->selectRaw('COUNT(*)')
            ->whereColumn('user_property_views.property_id', 'properties.id');
    }

    protected function applyPropertyFilters(&$query, $filters)
    {
        if ($filters['property_code'] ?? null) {
            $query->where('properties.property_code', $filters['property_code']);
        }
        if ($filters['adult_guest'] ?? null) {
            $query->where('properties.adult_guest', '>=', $filters['adult_guest']);
            $guestquery = (clone $query);
        }
        if (!empty($filters['beds']) && $filters['beds'] !== 'Any') {
            $query->where('properties.beds', $filters['beds']);
        }
        if (!empty($filters['adult_guest']) && !empty($filters['beds']) && $filters['beds'] !== 'Any' && !$query->exists()) {
            $query = $guestquery;
        }
        if (!empty($filters['bedrooms']) && $filters['bedrooms'] !== 'Any') {
            $query->where('properties.bedrooms', $filters['bedrooms']);
        }
        if (!empty($filters['bathrooms']) && $filters['bathrooms'] !== 'Any') {
            $query->where('properties.bathrooms', $filters['bathrooms']);
        }
        if (!empty($filters['searchNights']) ?? null) {
            $query->where('properties.min_nights', '<=', $filters['searchNights']);
        }
        if (!empty($filters['property_type'])) {
            $query->whereIn('properties.property_type', array_filter(explode(',', $filters['property_type'])));
        }
        if (!empty($filters['space_type'])) {
            $query->whereIn('properties.space_type', array_filter(explode(',', $filters['space_type'])));
        }
        if(Auth::check()){
            $query->where('properties.host_id', '!=', Auth::id());
        }
        if (!empty($filters['amenities'])) {
            foreach (array_filter(explode(',', $filters['amenities'])) as $id) {
                $query->whereRaw('FIND_IN_SET(?, properties.amenities)', [$id]);
            }
        }
        $lang = Session::get('language', 'en');
        if (!empty($filters['districts'])) {
            $query->whereIn($lang === 'ar' ? 'districts.name_ar' : 'districts.name', $filters['districts']);
        }
    }

    function apiSearchResultV4(Request $request){
        $authUser = auth('api')->user() ?? null;
        $lang = $authUser ? $authUser->lang : app()->getLocale();
        $params = $request->only([
            'location', 'checkin', 'checkout', 'bedrooms', 'beds', 'bathrooms',
            'property_type', 'space_type', 'amenities', 'book_type',
            'min_price', 'max_price', 'sortBy', 'searchNights', 'guest',
            'adult_guest', 'child_guest', 'property_code', 'page', 'districts', 'properties'
        ]);
        $cacheKey = 'search_result_v4_' . md5(json_encode($params));
        if ($cached = Cache::get($cacheKey)) return new SearchPropertyCollection($cached['pagination'], $cached['location'], null);
        if (isset($recommendProperties) && $recommendProperties == 'recommended') {
            $properties = Properties::recommendedForHomeApi();
            return new SearchPropertyCollection($properties, [
                'lat' => $cityData?->latitude ?? 0,
                'long' => $cityData?->longitude ?? 0,
            ], null);
        }
        // Inputs
        $location = $request->input('location','riyadh');
        $checkin = Carbon::parse($request->input('checkin',now()->addDay()->format('m/d/Y')))->format('Y-m-d');
        $checkout = Carbon::parse($request->input('checkout',now()->addDays(2)->format('m/d/Y')))->format('Y-m-d');
        $days = $this->helper->get_days($checkin, $checkout);
        array_pop($days); // remove last date

        // Session updates
        Session::put([
            'header_checkin' => $checkin,
            'header_checkout' => $checkout,
            'adult_guest_session' => $request->input('adult_guest')
        ]);

        // Geo filter
        $cityData = Cache::remember("city_bounds_{$location}", 60, function () use ($location) {
            return DB::table('cities')->where('name', $location)->first();
        });

        $inc = Properties::LAT_LONG_INCREMENT;
        [$minLat, $maxLat, $minLong, $maxLong] = [
            $cityData?->latitude ?? 0 - $inc, $cityData->latitude + $inc,
            $cityData?->longitude ?? 0 - $inc, $cityData->longitude + $inc
        ];

        $column = $lang === 'ar' ? 'name_ar' : 'name';
        $districtsMulti = Cache::remember("districts_{$cityData->id}_{$lang}", 10, function () use ($cityData, $column) {
            return DB::table('districts')->where('city_id', $cityData->id)->pluck($column);
        });

        // Cache not available property IDs
        $not_available_property_ids = Cache::remember("not_available_ids_" . md5(json_encode($days)), 10, function () use ($days) {
            return DB::table('property_dates')->select('property_id')
                ->whereIn('date', $days)->where('status', 'Not available')
                ->union(
                    DB::table('custom_pricing')->select('property_id')
                        ->whereIn('date', $days)->where('status', 'Not available')
                )->distinct()->pluck('property_id');
        });

        $propertyObj = Properties::query()
            ->with('property_photos')
            ->select(Properties::SEARCH_COLUMNS_WEB_OPTIMIZED)
            ->addSelect(DB::raw("
                6371 * acos(
                    cos(radians($minLat)) * cos(radians(property_address.latitude)) *
                    cos(radians(property_address.longitude) - radians($minLong)) +
                    sin(radians($minLat)) * sin(radians(property_address.latitude))
                ) AS distance
            "))
            ->selectSub($this->viewsSubquery(), 'views_count')
            ->addSelect(DB::raw(
                'IF(properties.id IN (' . $not_available_property_ids->implode(',') . '), 0, 1) AS available'
            ))
            ->join('property_type', 'properties.property_type', '=', 'property_type.id')
            ->join('property_description', 'properties.id', '=', 'property_description.property_id')
            ->join('property_address', 'properties.id', '=', 'property_address.property_id')
            ->join('master_availability', 'properties.id', '=', 'master_availability.property_id')
            ->join('users', 'properties.host_id', '=', 'users.id')
            ->join('property_price', 'properties.id', '=', 'property_price.property_id')
            ->join('scoring_system', 'properties.id', '=', 'scoring_system.property_id')
            ->leftJoin('cities', 'property_address.city_id', '=', 'cities.id')
            ->leftJoin('property_discounts', 'properties.id', '=', 'property_discounts.property_id')
            ->when($request->exists('districts'), function ($query) {
                $query->join('districts', 'property_address.district_id', '=', 'districts.id');
            })
            ->whereBetween('master_availability.determine_price', [$request->input('min_price', 0), $request->input('max_price', 999999)])
            ->whereRaw("property_address.latitude BETWEEN ? AND ?", [$minLat, $maxLat])
            ->whereRaw("property_address.longitude BETWEEN ? AND ?", [$minLong, $maxLong])
            ->whereRaw("users.status = 'Active'")
            ->where('property_address.city', $location)
            ->where('properties.status', 'Listed')
            ->where('properties.visibility', 1)
            ->whereNotNull('properties.slug')
            ->where('properties.slug', '!=', '')
            ->whereIn('master_availability.date', $days)
            ->where('master_availability.status', 'available')
            ->groupBy('properties.id')
            ->orderByDesc('available');

        // Filters
        $this->applyPropertyFilters($propertyObj, $request->all());

        // Sorting
        $sortby = $request->input('sortBy');
        $propertyObj->when(!$sortby || !in_array($sortby, ['asc', 'desc', 'before_asc', 'before_desc']),
            fn($q) => $q->orderBy('properties.priority', 'desc'))
            ->when($sortby === 'mostRatedFirst', fn($q) => $q->orderBy('properties.rating_avg', 'desc'))
            ->when($sortby === 'nearestToCity', fn($q) => $q->orderBy('distance', 'asc'))
            ->orderBy('property_score_total_points', 'desc')
            ->orderBy('properties.id', 'desc');


        $pagination = $propertyObj->distinct()->simplePaginate(Properties::PER_PAGE);

        // Transform properties
        $number_of_days = Carbon::parse($checkin)->diffInDays(Carbon::parse($checkout));
        $dateRange = CarbonPeriod::create($checkin, Carbon::parse($checkout)->subDay());
        $pagination->getCollection()->transform(function ($property) use ($dateRange,$number_of_days) {
            $cacheKey = "price_{$property->id}_" . md5(json_encode($dateRange));
            $totals = Cache::remember($cacheKey, 10, fn() =>
                App\Http\Helpers\Common::getWeeklyMonthlyDiscount($property, $dateRange));

            $property->before_discount = $totals['total_before_discount'];
            $property->total_price = $totals['total_after_discount'];
            $property->number_of_days = $number_of_days;
            return $property;
        });

        // Count available
        $allPropertyIds = $pagination->pluck('id');
        $availablePropertyIds = $allPropertyIds->diff($not_available_property_ids);
        $notAvailablePropertyIds = $allPropertyIds->intersect($not_available_property_ids);

        UserSearch::create([
            'user_id' => auth()->user()?->id,
            'session_id' => Session::getId(),
            'search_query' => json_encode($params),
            'total_available_properties_count' => $availablePropertyIds->count(),
            'total_not_available_properties_count' => $notAvailablePropertyIds->count(),
            'available_properties_ids' => json_encode($availablePropertyIds),
            'not_available_properties_ids' => json_encode($notAvailablePropertyIds),
            'platform' => DESKTOP_APP_KEY_NAME,
            'guest_uuid' => request('guest_uuid') ?? request()->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null
        ]);

        $cacheData = [
            'pagination' => $pagination,
            'location' => [
                'lat' => $cityData?->latitude ?? 0,
                'long' => $cityData?->longitude ?? 0,
            ]
        ];

        // Cache only the data, not the final response
        Cache::put($cacheKey, $cacheData, now()->addMinutes(30));

        // Create and return the response using the data
        $result = new SearchPropertyCollection($pagination, [
            'lat' => $cityData?->latitude ?? 0,
            'long' => $cityData?->longitude ?? 0,
        ], null);

        return $result;
    }
    function searchResultV2(Request $request)
    {
        // Prepare all values from the request and set default values
        $authUser = auth('api')->user() ?? null;
        $lang = $authUser ? $authUser->lang : app()->getLocale();

        // Get all request parameters with default values
        $location = $request->input('location', 'riyadh');
        $unit_code = $request->input('property_code');
        $checkin = $request->input('checkin', now()->addDay()->format('m/d/Y'));
        $checkout = $request->input('checkout', now()->addDays(2)->format('m/d/Y'));
        $bedrooms = $request->input('bedrooms');
        $beds = $request->input('beds');
        $bathrooms = $request->input('bathrooms');
        $property_type = $request->input('property_type');
        $space_type = $request->input('space_type');
        $amenities = $request->input('amenities');
        $book_type = $request->input('book_type');
        $min_price = $request->input('min_price');
        $max_price = $request->input('max_price');
        $sortby = $request->input('sortBy');
        $guest = $request->input('guest');
        $adult_guest = $request->input('adult_guest');
        $child_guest = $request->input('child_guest');
        $page = $request->input('page', 1);
        $recommendProperties = $request->input('properties');

        // Calculate search nights
        $searchNights = $request->input('searchNights');
        if (!$searchNights) {
            try {
                $searchNights = Carbon::parse($checkin)->diffInDays(Carbon::parse($checkout));
            } catch (\Exception $e) {
                $searchNights = 1; // Default to 1 night if dates are invalid
            }
        }

        // Process array parameters
        if (!is_array($property_type)) {
            $property_type = !empty($property_type) ? explode(',', $property_type) : [];
        }

        if (!is_array($space_type)) {
            $space_type = !empty($space_type) ? explode(',', $space_type) : [];
        }

        if (!is_array($book_type)) {
            $book_type = !empty($book_type) ? explode(',', $book_type) : [];
        }

        if (!is_array($amenities)) {
            $amenities = !empty($amenities) ? explode(',', $amenities) : [];
        }

        // Create a cache key based on the processed parameters
        $searchParams = [
            'location' => $location,
            'checkin' => $checkin,
            'checkout' => $checkout,
            'bedrooms' => $bedrooms,
            'beds' => $beds,
            'bathrooms' => $bathrooms,
            'property_type' => $property_type,
            'space_type' => $space_type,
            'amenities' => $amenities,
            'book_type' => $book_type,
            'min_price' => $min_price,
            'max_price' => $max_price,
            'sortBy' => $sortby,
            'searchNights' => $searchNights,
            'guest' => $guest,
            'adult_guest' => $adult_guest,
            'child_guest' => $child_guest,
            'property_code' => $unit_code,
            'page' => $page,
        ];

        $cacheKey = 'search_result_v2_' . md5(json_encode($searchParams));

        // Try to get the cached data
        $cachedData = Cache::get($cacheKey);
        if ($cachedData) {
            // Create and return the response using the cached data
            return new SearchPropertyCollection($cachedData['pagination'], $cachedData['location'], null);
        }
        // Handle recommended properties (this doesn't need caching as it's a simple query)
        if (isset($recommendProperties) && $recommendProperties == 'recommended') {
            $properties = Properties::recommendedForHomeApi();
            return new SearchPropertyCollection($properties, [
                'lat' => $cityData?->latitude ?? 0,
                'long' => $cityData?->longitude ?? 0,
            ], null);
        }




        // Array parameters have already been processed

        $properties_whereIn = [];

        $propertyObj = new Properties();

        $cityData = DB::table('cities')->where('name', $location)->orWhere('name_ar', $location)->orWhere('id', $location)->first();

        $minLat = ($cityData->latitude ?? 0) - $propertyObj::LAT_LONG_INCREMENT;
        $maxLat = ($cityData->latitude ?? 0) + $propertyObj::LAT_LONG_INCREMENT;
        $minLong = ($cityData->longitude ?? 0) - $propertyObj::LAT_LONG_INCREMENT;
        $maxLong = ($cityData->longitude ?? 0) + $propertyObj::LAT_LONG_INCREMENT;

        $checkin = Carbon::parse($checkin)->format('Y-m-d');
        $checkout = Carbon::parse($checkout)->format('Y-m-d');
        $days = $this->helper->get_days($checkin, $checkout);
        unset($days[count($days) - 1]);
        $not_available_property_ids = DB::table('property_dates')
            ->whereIn('date', $days)
            ->where('status', 'Not available')
            ->distinct()
            ->pluck('property_id');

        $custom_pricing_property_ids = DB::table('custom_pricing')
            ->whereIn('date', $days)
            ->where('status', 'Not available')
            ->distinct()
            ->pluck('property_id');

        $not_available_property_ids = $not_available_property_ids->merge($custom_pricing_property_ids)->unique();
        $properties_where['properties.status'] = 'Listed';
        $properties_where['properties.visibility'] = '1';
        $properties_where['properties.min_nights'] = $searchNights;

        if ($authUser) {
            $properties_where['properties.host_id'] = $authUser->id;
        }

        if (isset($bedrooms) && $bedrooms !== 'Any') {
            $properties_where['properties.bedrooms'] = $bedrooms;
        }

        if (isset($adult_guest)) {
            $properties_where['properties.adult_guest'] = $adult_guest;
        }

        if (isset($bathrooms) && $bathrooms !== 'Any') {
            $properties_where['properties.bathrooms'] = $bathrooms;
        }

        if (isset($beds) && $beds !== 'Any') {
            $properties_where['properties.beds'] = $beds;
        }

        if (count($space_type)) {
            $properties_whereIn['properties.space_type'] = $space_type;
        }

        if (count($property_type)) {
            $properties_whereIn['properties.property_type'] = $property_type;
        }

        $address_condition = 'property_address.latitude between ' . $minLat . ' and ' . $maxLat . ' and property_address.longitude between ' . $minLong . ' and ' . $maxLong;
        $users_condition = "users.status = 'Active'";

        // ----------------------------------------------------Query------------------------------------------------------------------------------------
        $properties = $propertyObj::with('property_photos')
            ->select(array_merge($propertyObj::SEARCH_COLUMNS_WEB, [
                'properties_pricing.number_of_days as number_of_days',
                'properties_pricing.day_price as day_price',
                'properties_pricing.total_price as total_price',
                'properties_pricing.before_discount as before_discount',
            ]))
            ->selectSub(function ($q) use ($minLat, $minLong) {
                $q->selectRaw("6371 * acos(cos(radians(" . $minLat . "))
                    * cos(radians(property_address.latitude))
                    * cos(radians(property_address.longitude) - radians(" . $minLong . "))
                    + sin(radians(" . $minLat . "))
                    * sin(radians(property_address.latitude)))");
            }, 'distance')
            ->selectSub(function ($q) {
                $q->selectRaw('COUNT(user_property_views.property_id) AS view_count')
                    ->from('user_property_views')
                    ->whereRaw('user_property_views.property_id = properties.id');
            }, 'views_count')
            ->join('property_type', 'properties.property_type', '=', 'property_type.id')
            ->join('property_description', 'properties.id', '=', 'property_description.property_id')
            ->join('property_address', 'properties.id', '=', 'property_address.property_id')
            ->join('users', 'properties.host_id', '=', 'users.id')
            ->join('property_price', 'properties.id', '=', 'property_price.property_id')
            ->join('scoring_system', 'properties.id', '=', 'scoring_system.property_id')
            ->leftJoin('cities', 'property_address.city_id', '=', 'cities.id')
            ->addSelect(DB::raw('IF(properties.id IN (' . $not_available_property_ids->implode(',') . '), 0, 1) AS available'))
            ->when($request->exists('districts'), function ($query) {
                $query->join('districts', 'property_address.district_id', '=', 'districts.id');
            })
            ->when($unit_code, function ($query, $unit_code) {
                return $query->where('properties.property_code', $unit_code);
            })
            ->leftJoin('property_discounts', 'properties.id', '=', 'property_discounts.property_id')
            ->whereRaw($address_condition)
            ->whereRaw($users_condition)
            ->whereRaw('properties.slug != ""')
            ->orderBy('available', 'desc');

        if ($properties_where) {
            foreach ($properties_where as $row => $value) {
                if ($row == 'properties.adult_guest') {
                    $operator = '>=';
                } else if ($authUser && $row == 'properties.host_id') {
                    $operator = '!=';
                } else if ($row == 'properties.min_nights') {
                    $operator = '<=';
                } else {
                    $operator = '=';
                }
                $properties = $properties->where($row, $operator, $value);
            }
        }

        if ($properties_whereIn) {
            foreach ($properties_whereIn as $row_properties_whereIn => $value_properties_whereIn) {
                $properties = $properties->whereIn(
                    $row_properties_whereIn,
                    array_values($value_properties_whereIn)
                );
            }
        }

        if (count($amenities)) {
            foreach ($amenities as $amenities_value) {
                $properties = $properties->whereRaw('FIND_IN_SET(' . $amenities_value . ', properties.amenities)');
            }
        }

        if ($request->property_code) {
            $properties = $properties->where('properties.property_code', $request->property_code);
        }

        if ($request->exists('districts') && count($request->districts) > 0) {
            $properties = $properties->whereIn('districts.id', $request->districts);
        }

        // $properties_pricing = $this->helper->getPropertiesPricing($checkin, $checkout);
        $period = CarbonPeriod::create(Carbon::parse($checkin), Carbon::parse($checkout));
        $chunks = array_chunk(iterator_to_array($period), 14); // Split into 14-day chunks

        $queries = [];

        foreach ($chunks as $index => $chunk) {
            $startDate = reset($chunk)->format('Y-m-d');
            $endDate = end($chunk)->format('Y-m-d');

            // Add a 1-day overlap, except for the last chunk
            if ($index !== count($chunks) - 1) {
                $endDate = Carbon::parse($endDate)->addDay()->format('Y-m-d');
            }

            // Get the query for each chunk
            $results[] = $this->helper->getPropertiesPricing($startDate, $endDate);

        }

        // Combine all queries with UNION ALL
        $combinedQuery = implode(" UNION ALL ", $results);

        // Wrap in a final SELECT to handle duplicates
        $properties_pricing = "SELECT DISTINCT * FROM ({$combinedQuery}) as combined_pricing";


        $pagination = $properties
            ->joinSub($properties_pricing, 'properties_pricing', function (JoinClause $join) {
                $join->on('properties.id', '=', 'properties_pricing.property_id');
            })
            ->whereBetween('properties_pricing.day_price', [$min_price, $max_price])
            ->when(
                !$sortby,
                fn($q) => $q->orderBy('properties.priority', 'desc') // DEFAULT CHILD ORDER BY PRIORITY
            )
            ->when(
                $sortby,
                fn($q) => match ($sortby) {
                    'mostRatedFirst' => $q->orderBy('properties.rating_avg', 'desc'),
                    'nearestToCity' => $q->orderBy('distance', 'asc'),
                    'asc' => $q->orderBy('properties_pricing.total_price', 'asc'),
                    'desc' => $q->orderBy('properties_pricing.total_price', 'desc')
                }
            )
            ->orderBy('property_score_total_points', 'desc')
            ->orderBy('properties.id', 'desc');
        $allPropertyIds = (clone $pagination)->pluck('properties.id')->toArray();
        $availablePropertyIds = array_diff($allPropertyIds, $not_available_property_ids->toArray());
        $notAvailablePropertyIds = array_intersect($allPropertyIds, $not_available_property_ids->toArray());
        $totalAvailableCount = count($availablePropertyIds);
        $totalNotAvailableCount = count($notAvailablePropertyIds);
        $pagination = $pagination->groupBy('properties.id')->paginate($propertyObj::PER_PAGE);
        $searchData = [
            'location' => $cityData?->name,
            'checkin' => $checkin,
            'checkout' => $checkout,
            'bedrooms' => $bedrooms,
            'beds' => $beds,
            'bathrooms' => $bathrooms,
            'property_type' => $property_type,
            'space_type' => $space_type,
            'amenities' => $amenities,
            'book_type' => $book_type,
            'min_price' => $min_price,
            'max_price' => $max_price,
            'sortby' => $sortby,
            'searchNights' => $searchNights,
            'unit_code' => $unit_code,
        ];

        App\Models\UserSearch::query()->create([
            'user_id' => $authUser?->id,
            'session_id' => FacadesSession::getId(),
            'api_token' => $request->bearerToken(),
            'search_query' => json_encode($searchData),
            'total_available_properties_count' => $totalAvailableCount,
            'total_not_available_properties_count' => $totalNotAvailableCount,
            'available_properties_ids' => json_encode($pagination->where('available', '=', 1)->pluck('id')->toArray()),
            'not_available_properties_ids' => json_encode($pagination->where('available', '=', 0)->pluck('id')->toArray()),
            'platform' => MOBILE_APP_KEY_NAME,
            'guest_uuid' => request('guest_uuid') ?? request()->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null
        ]);

        // Create the data to be cached
        $cacheData = [
            'pagination' => $pagination,
            'location' => [
                'lat' => $cityData?->latitude ?? 0,
                'long' => $cityData?->longitude ?? 0,
            ]
        ];

        // Cache only the data, not the final response
        Cache::put($cacheKey, $cacheData, now()->addMinutes(5));

        // Create and return the response using the data
        $result = new SearchPropertyCollection($pagination, [
            'lat' => $cityData?->latitude ?? 0,
            'long' => $cityData?->longitude ?? 0,
        ], null);

        return $result;
    }

        public function getPropertyType($id)
        {
            return PropertyType::find($id);
        }

        public function getSpaceType($id)
        {
            return SpaceType::find($id);
        }

        public function getSpaceTypesByPropertyType($id)
        {
            return response()->json([
                'spaceTypes' => SpaceType::where('property_type', $id)->where('status', 'Active')->select('id', 'name')->get(),
                'success' => 'true',
            ]);
        }

        public function content_read($url)
        {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_URL, $url);
            $result = curl_exec($ch);
            curl_close($ch);

            return $result;
        }
    }
