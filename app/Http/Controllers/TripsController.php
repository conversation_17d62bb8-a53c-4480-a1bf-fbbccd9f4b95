<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Helpers\Common;
use App\Http\Controllers\EmailController;
use App\Jobs\EventTrackingJob;
use App\Notifications\UserNotify;
use App\Http\Controllers\Controller;
use App\Http\Services\PlatformReservationService;
use App\Services\EventTracking\EventTrackingConstants;
use DateTime;
use Auth;

use App\Models\{
    Bookings,
    BookingDetails,
    Messages,
    Penalty,
    Payouts,
    PayoutPenalties,
    PropertyDates,
    User,
    Refund,
    BookingStatusLog
};
use Illuminate\Support\Facades\Notification;

class TripsController extends Controller
{
    private $helper;

    public function __construct()
    {
        $this->helper = new Common;
    }

    public function guestCancel(Request $request, EmailController $email)
    {

        $bookings = Bookings::with('properties')->find($request->id);
        $now = new DateTime();

        $booking_start = new DateTime($bookings->start_date);
        $interval_diff = $now->diff($booking_start);
        $interval = $interval_diff->days;
        $result = $this->GetRefundAmount($bookings, $interval);
        if ($result['status'] == 1) {
            $this->AmountRefunded($bookings, $result['amount_to_refund']);
        }

        $payount = Payouts::where(['user_id' => $bookings->host_id, 'booking_id' => $request->id])->first();

        if (isset($payount->id)) {
            $payout_penalties = PayoutPenalties::where('payout_id', $payount->id)->get();
            if (!empty($payout_penalties)) {
                foreach ($payout_penalties as $key => $payout_penalty) {
                    $prv_penalty = Penalty::where('id', $payout_penalty->penalty_id)->first();
                    $update_amount = $prv_penalty->remaining_penalty + $payout_penalty->amount;
                    Penalty::where('id', $payout_penalty->penalty_id)->update(['remaining_penalty' => $update_amount, 'status' => 'Pending']);
                }
            }
        }

        if ($now < $booking_start) {
            $payouts = new Payouts;
            $payouts->booking_id = $request->id;
            $payouts->property_id = $bookings->property_id;
            $payouts->user_id = $bookings->user_id;
            $payouts->user_type = 'guest';
            $payouts->amount = $bookings->total;
            $payouts->currency_code = $bookings->currency_code;
            $payouts->penalty_amount = 0;
            $payouts->status = 'Future';
            $payouts->save();

            $payouts_host_amount = Payouts::where('user_id', $bookings->host_id)->where('booking_id', $request->id)->delete();

            $days = $this->helper->get_days($bookings->start_date, $bookings->end_date);

            for ($j = 0; $j < count($days) - 1; $j++) {
                PropertyDates::where('property_id', $bookings->property_id)->where('date', $days[$j])->where('status', 'Not available')->delete();
            }

            $messages = new Messages;
            $messages->property_id = $bookings->property_id;
            $messages->booking_id = $bookings->id;
            $messages->receiver_id = $bookings->host_id;
            $messages->sender_id = Auth::user()->id;
            $messages->message = "-";
            // $messages->message        = $request->cancel_message;
            $messages->type_id = 2;
            $messages->save();

            $cancel = Bookings::find($request->id);
            $cancel->cancelled_by = "Guest";
            $cancel->cancelled_at = date('Y-m-d H:i:s');
            $cancel->status = "Cancelled";


            $platformReservationService = new PlatformReservationService();
            $platformReservationService->cancelPlatformReservation($cancel);
            $cancel->save();

            BookingStatusLog::updateOrCreate(
                ['booking_id' => $cancel->id],
                ['status' => $cancel->status, 'changed_by' => Auth::id()]
            );

            $booking_details = new BookingDetails;
            $booking_details->booking_id = $request->id;
            $booking_details->field = 'cancelled_reason';
            $booking_details->value = "-";
            // $booking_details->value      = $request->cancel_reason;
            $booking_details->save();

            \App\Jobs\EventTrackingJob::trackBookingCancelledByGuest(
                bookingId: $cancel->id,
                guestId: $cancel->user_id,
                hostId: $cancel->host_id,
                propertyId: $cancel->property_id,
                reason: '-',
                host_cancellation_policy: $cancel->host->cancel_policy ?? 'Flexible',
            );

            $this->helper->cancelPolicyTawuniya($cancel);
        } else {
            $this->helper->one_time_message('success', "You can't cancel booking after arrival");
            return redirect('guest/reservation');
        }
        $email->bookingCancellation($request->id);
        $hosts = User::select('users.*')->join('properties AS ps', 'ps.host_id', 'users.id')
            ->leftJoin('properties_cohosts AS pcs', fn($q) => $q->on('pcs.co_host_id', 'users.id')->whereNull('pcs.deleted_at'))
            ->where('ps.id', $bookings->property_id)->distinct()->get();
        Notification::send($hosts, new UserNotify(
            'booking.guest.cancelled',
            // route('managehost.all_reservation'),
            url('managehost/all_reservation?tab=cancelled&id=' . $bookings->id . '&query_code=' . $bookings->code),
            [':property' => $bookings->properties->name],
            [
                'slug' => 'booking',
                'tab'  => 'cancelled-bookings'
            ]
        ));
        clearCache('.calc.property_price');
        return redirect('guest/reservation')->with('bookingcancelled', "Booking Cancelled Successfully");
    }

    public function GetRefundAmount($bookings, $interval)
    {
        return $this->helper->GetRefundAmount($bookings, $interval);

    }

    public function AmountRefunded($booking, $amount_to_refund)
    {

        return $this->helper->refundAmount($booking, $amount_to_refund);

        $refund = new Refund();
        $refund->user_id = $booking->user_id;
        $refund->host_id = $booking->host_id;
        $refund->booking_id = $booking->id;
        $refund->amount = $amount_to_refund;
        $refund->status = 'Unpaid';
        $refund->save();
    }
}
