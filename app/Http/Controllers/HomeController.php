<?php

namespace App\Http\Controllers;

use App\Http\Requests;
use Illuminate\Http\Request;
use App\DataTables\HostPropertiesDataTable;
use App\DataTables\HostReservationDataTable;
use App\DataTables\HostListingDataTable;
use App\Http\Helpers\Common;
use App\Http\Controllers\Controller;
use Twilio\Rest\Client;
use Illuminate\Support\Facades\Cache;
use Str;
use App\Http\Controllers\Api\UserController;
use App\Jobs\SendOtpEmailJob;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Mail\OtpMail;

use View, Auth, App, Session, Route, DB, PDF, Validator;

use App\Models\{
    AccountDeleteRequest,
    Currency,
    Properties,
    PropertyAddress,
    PropertyPrice,
    PropertySteps,
    PropertyPhotos,
    PropertyType,
    PropertyDescription,
    Page,
    Settings,
    StartingCities,
    Testimonials,
    Language,
    Admin,
    Bookings,
    Bank,
    Banners,
    EmailVerification,
    Favourite,
    PropertyDates,
    User,
    UserDetails,
    Wallet,
    UsersVerification,
    Reviews,
    HostWallets,
    HostWithdrawl,
    LocalizationKeyword,
    Permissions,
    PhoneVerification,
    PropertyFees,
    SpaceType,
    Amenities,
    BookingPaymentDetails,
    Country,
    CustomerSupport,
    ElmDocument,
    CoHostRequest,
    District,
    OtpSetting,
    PropertiesCohost,
    LandingPageVisit
};
use App\Notifications\UserNotify;
use App\Notifications\UserNotifySms;
use App\Rules\ReCaptchaV3;
use Carbon\Carbon;
use DateTime;
use Exception;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

require base_path() . '/vendor/autoload.php';

class HomeController extends Controller
{
    private $helper;
    public $now;

    public function __construct()
    {
        $this->helper = new Common;
        $this->now = Carbon::now()->format('Y-m-d');
    }

    public function cronjob_send_notification()
    {

        $extend_booking = DB::table('extend_booking')
            ->join('bookings', 'bookings.id', '=', 'extend_booking.booking_id')
            ->whereNull('expire_at')
            ->where('extend_booking.created_at', '<=', now()->subMinutes(30))
            ->where('is_expire', 0)
            ->get();
        DB::table('extend_booking')
            ->where('created_at', '<=', now()->subMinutes(30))
            ->where('is_success', 0)
            ->whereNull('expire_at')
            ->update([
                'expire_at' => now(),
                'is_expire' => 1,
                'is_success' => 0,
            ]);
        if (!empty($extend_booking) && count($extend_booking) > 0) {
            $property_dates = [];
            foreach ($extend_booking as $extension) {
                $days = $this->helper->get_days($extension->extended_start_date, $extension->extended_end_date);
                for ($j = 0; $j < count($days); $j++) {
                    PropertyDates::where('property_id', $extension->property_id)->where('date', $days[$j])->where('status', 'Not available')->delete();
                }
                $days2 = $this->helper->get_days($extension->prev_start_date, $extension->prev_end_date);
                for ($i = 0; $i < count($days2) - 1; $i++) {
                    $data = [
                        'property_id' => $extension->property_id,
                        'booking_id' => $extension->booking_id,
                        'price' => '0',
                        'date' => $days2[$i],
                        'status' => "Not Available",
                        'min_day' => '0',
                        'min_stay' => '0',
                    ];
                    PropertyDates::updateOrCreate(['property_id' => $extension->property_id, 'date' => $days2[$i]], $data);
                }
            }
        }


        //                 $booking_id = $extension->booking_id;
        //                 $prev_start_date = $extension->prev_start_date;
        //                 $prev_end_date = $extension->prev_end_date;
        //                 $extended_start_date = $extension->extended_start_date;
        //                 $extended_end_date = $extension->extended_end_date;

        //                 // Get the dates to delete
        //                 $dates_to_delete = PropertyDates::where('booking_id', $booking_id)
        //                                     ->whereBetween('date', [$extended_start_date, $extended_end_date])
        //                                     ->pluck('id')
        //                                     ->toArray();

        //                                     dd($dates_to_delete);

        //                 // Delete the records in bulk
        //                 PropertyDates::whereIn('id', $dates_to_delete)->delete();

        //                 // Create new records for each date between prev_start_date and prev_end_date
        //                 $current_date = new DateTime($prev_start_date);
        //                 $end_date = new DateTime($prev_end_date);
        //                 while ($current_date < $end_date) {
        //                     $property_dates[] = [
        //                         'property_id' => $extension->property_id,
        //                         'booking_id' => $booking_id,
        //                         'date' => $current_date->format('Y-m-d'),
        //                     ];
        //                     $current_date->modify('+1 day');
        //                 }
        //             }

        // // Insert the new records in bulk
        // PropertyDates::insert($property_dates);


        // dd('lplp');
        // $extend_booking = DB::table('extend_booking')->join('bookings', 'bookings.id' ,'=' ,'extend_booking.id')->whereNull('expire_at')->where('is_expire', 0)->get();
        // foreach($extend_booking as $extension){
        //     $booking_id = $extension->booking_id;
        //     PropertyDates::where('booking_id', $extension->booking_id)->delete();
        //     $prev_start_date = $extension->prev_start_date;
        //     $prev_end_date = $extension->prev_end_date;

        //     // Create new records for each date between prev_start_date and prev_end_date
        //     $current_date = new DateTime($prev_start_date);
        //     $end_date = new DateTime($prev_end_date);
        //     while ($current_date < $end_date) {
        //         PropertyDates::create([
        //             'property_id' => $extension->property_id,
        //             'booking_id' => $booking_id,
        //             'date' => $current_date->format('Y-m-d'),
        //         ]);
        //         $current_date->modify('+1 day');
        //     }
        // }

        $yesterday = Carbon::now()->subDay()->toDateString();
        $bookings = Bookings::select('bookings.id', 'user_id', 'end_date')
            ->whereDate('end_date', $yesterday)
            // ->where('bookings.status', '=', 'Accepted')
            ->selectRaw('users.id as user_id, users.fcm_token, users.lang')
            ->join('users', 'bookings.user_id', '=', 'users.id')
            ->get();
        // $bookings->makeHidden(['host_payout', 'label_color', 'date_range', 'expiration_time', 'startdate_dmy', 'enddate_dmy', 'start_date_st', 'end_date_st']);


        // $today = date('Y-m-d');
        // $bookings = Bookings::select('user_id','end_date')
        // ->where('end_date', '<', date('Y-m-d'))
        // ->with(['users'])
        // ->get();


        // dd(count($bookings)); //434

        foreach ($bookings as $booking) {
            if ($booking->users->fcm_token) {
                $message = $booking->users->lang == 'ar' ? 'انتهاء الحجز الليلة! يرجى ترك تقييمك لإقامتك معنا' : 'Booking ending tonight! Please leave a review of your stay with us.';
                $this->helper->sendPushNotification($booking->users->fcm_token, $message, null, "reservation", "history");
            }
            $this->helper->sendPushNotification('duXsvoJLSWSxaoENDPggO6:APA91bHK3so2E9WuQj2uWcX274S6GHKH1Uy9SuYmzERyVsaU63hpjkkbnBUofh_tLYLiQlZAGBCnFCebFJ11ljazMHFxKvMkjCuQwXy0y_1VRiwhosMpTMXqz119BFVRz0vfCf6xRlmD', $message, null, "reservation", "history");
        }
        dd('done');
    }

    public function des_to_arabic()
    {
        $eng_summary = PropertyDescription::where('summary', '!=', null)->get();

        foreach ($eng_summary as $eng_sum) {

            if (strlen($eng_sum->summary) != mb_strlen($eng_sum->summary, 'utf-8')) {

                PropertyDescription::where('summary', $eng_sum->summary)->update(['summary_ar' => $eng_sum->summary, 'summary' => null]);
                dump($eng_sum->summary);
            }
        }
    }

    public function get_cities_and_states()
    {
        // $data = PropertyAddress::where('city', '!=', null)->where('state', '!=', null)->groupBy('state','city')->get();
        if (isset($_GET['colname'])) {

            $data = PropertyAddress::where($_GET['colname'], '!=', null)->groupBy($_GET['colname'])->get();

            foreach ($data as $add) {
                // if(strlen($add->city) != mb_strlen($add->city, 'utf-8')){
                $col = $_GET['colname'];
                if (preg_match('/\p{Arabic}/u', $add->$col)) {
                    dump($add->$col);
                } else {

                    dump($add->$col . '|NOT/ARABIC');
                }
            }
        } else {
            dd('colname required');
        }

        //     // if(strlen($add->state) != mb_strlen($add->state, 'utf-8')){
        //     //     dump($add->state);
        //     // }
    }


    // Transliterate the English city name to its Arabic equivalent

    // Output the translated city name
    public function recommendedHomeProperties(Request $request)
    {
        $page = $request->get('page', 1);
        $properties = Properties::recommendedForHome(10, $page);

        $html = '';
        foreach ($properties['data'] as $property) {
            $html .= view('home.partials.property_item', compact('property'))->render();
        }

        return response()->json(['html' => $html, 'total' => $properties['total'], 'last_page' => $properties['last_page']]);
    }

    public function index(Request $request)
    {
        // Create a cache key based on relevant parameters
        $cacheParams = [
            'language' => Session::get('language'),
            'user_mode' => Session::get('user_mode'),
            'is_mobile' => $this->helper->isRequestFromMobile($request)
        ];
        $cacheKey = 'home_index_data_' . md5(json_encode($cacheParams));

        // Try to get the cached data
        $data = Cache::get($cacheKey);

        // If data is not cached, proceed with normal logic to build the data array
        if (!$data) {
            $data = [];
            $data['starting_cities'] = StartingCities::getAll();
            $data['properties'] = Properties::recommendedForHome();
            $data['testimonials'] = Testimonials::getAll();
            $data['sessionLanguage'] = Session::get('language');
            $data['property_type'] = PropertyType::getAll()->where('status', 'Active');
            $data['banners'] = Banners::where('status', 'Active')->get();

            $language = Settings::getAll()->where('name', 'default_language')->where('type', 'general')->first();
            $language->value = $language->value ?? 'ar';

            $pref = Settings::getAll();
            $prefer = [];

            if (!empty($pref)) {
                foreach ($pref as $value) {
                    $prefer[$value->name] = $value->value;
                }
                Session::put($prefer);
            }
            $data['date_format'] = Settings::getAll()->firstWhere('name', 'date_format_type')->value;

            // Cache the data array for 30 minutes
            Cache::put($cacheKey, $data, now()->addMinutes(30));
        }

        // These session operations should happen on every request, not just when building the data
        if (!Session::has('user_mode')) {
            Session::put('user_mode', "user");
        }

        Session::forget('header_checkin');
        Session::forget('header_checkout');
        Session::forget('child_guest_session');
        Session::forget('adult_guest_session');

        if (Session::get('user_mode') == 'host') {
                return redirect()->route('managehost.day');
        }
        // Render the view with the data (cached or freshly built)
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.home.home', $data);
        } else {
            return view('home.home', $data);
        }
    }


    // mobile account
    public function accounts()
    {
        $data['title'] = customTrans('header.account');
        $data['user'] = auth()->user();

        return view('mobile.pages.account', $data);
    }

    public function view_profile()
    {
        $data['title'] = customTrans('users_profile.profile');
        $data['user'] = auth()->user();

        return view('mobile.pages.view_profile', $data);
    }

    // public function phpinfo()
    // {
    //     echo phpinfo();
    // }

    public function login()
    {
        return view('home.login');
    }

    public function setSession(Request $request)
    {
        if ($request->currency) {
            Session::put('currency', $request->currency);
            $symbol = Currency::code_to_symbol($request->currency);
            Session::put('symbol', $symbol);
        } elseif ($request->language) {
            if (session_status() !== PHP_SESSION_ACTIVE) {
                session_start();
            }
            $_SESSION['language'] = $request->language;
            Session::put('language', $request->language);
            $name = Language::name($request->language);
            Session::put('language_name', $name);
            App::setLocale($request->language);

            if (auth()->check()) {
                auth()->user()->update(['lang' => $request->language]);
            }
        }
    }

    public function setTimeZone(Request $request)
    {
        $user = User::find(Auth::id());
        $user->timezone = $request->timezone;
        // $user->mode = "user";
        $user->save();
        $is_elm_verified = ElmDocument::where('user_id', Auth::id())->where('verified_till', '>=', Carbon::now()->format('Y-m-d'))->first();
        return apiResponse($is_elm_verified, 'Success', 200);
    }

    public function switchToHost(Request $request)
    {
        Session::put('user_mode', $request->sessionValue);
        if ($request->sessionValue == "host") {
            Session::put('switchtohost', $request->sessionValue);
        }
        return response()->json(["message" => "Success"]);
    }

    public function clearSwitchToHostSession()
    {
        Session::forget('switchtohost');
        return response()->json(['success' => true]);
    }

    public function cancellation_policies()
    {
        return view('home.cancellation_policies');
    }

    public function staticPages(Request $request)
    {
        $pages = Page::where(['url' => $request->name, 'status' => 'Active']);
        if (!$pages->count()) {
            abort('404');
        }
        $pages = $pages->first();
        $data['content'] = str_replace(['SITE_NAME', 'SITE_URL'], [SITE_NAME, url('/')], $pages->content);
        $data['title'] = $pages->url;
        $data['url'] = url('/') . '/';
        $data['img'] = $data['url'] . 'public/images/2222hotel_room2.jpg';

        return view('home.static_pages', $data);
    }


    public function activateDebugger()
    {
        setcookie('debugger', 0);
    }

    public function walletUser(Request $request)
    {

        $users = User::all();
        $wallet = Wallet::all();

        // SELECT users.first_name, users.last_name FROM users WHERE id NOT IN (SELECT user_id FROM wallets);

        if (!$users->isEmpty() && $wallet->isEmpty()) {
            foreach ($users as $key => $user) {

                Wallet::create([
                    'user_id' => $user->id,
                    'currency_id' => 1,
                    'balance' => 0,
                    'is_active' => 0
                ]);
            }
        }

        return redirect('/');
    }

    public function privacy_policy(Request $request)
    {
        $data['title'] = customTrans('footer.privacy_policy');
        $data['lang'] = isset($request->lang) ? $request->lang : 'en';
        // $request->session()->put('language', $data['lang']);
        $data['mobile'] = isset($request->mobile) ? $request->mobile : 0;
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.privacy_policy', $data);
        } else {
            return view('pages.privacy_policy', $data);
        }
    }

    public function term_condition(Request $request)
    {
        $data['title'] = customTrans('sign_up.term_condition');
        $data['lang'] = isset($request->lang) ? $request->lang : 'en';
        // $request->session()->put('language', $data['lang']);
        $data['mobile'] = isset($request->mobile) ? $request->mobile : 0;
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.term_condition', $data);
        } else {
            return view('pages.term_condition', $data);
        }
    }

    public function insurance(Request $request)
    {
        $data['title'] = customTrans('insurance.i_insurance');
        $data['lang'] = isset($request->lang) ? $request->lang : 'en';
        $data['mobile'] = isset($request->mobile) ? $request->mobile : 0;
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.insurance', $data);
        } else {
            return view('pages.insurance', $data);
        }
    }

    public function ilm_yaqeen(Request $request)
    {
        $data['title'] = customTrans('Ilm Yaqeen');
        $data['lang'] = isset($request->lang) ? $request->lang : 'en';
        if (auth()->check()) {
            if (auth()->user()->is_elm_verified) {
                return redirect()->route('guest_reservation');
            }
        }
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.ilm_yaqeen', $data);
        } else {
            return view('pages.ilm_yaqeen', $data);
        }

    }

    public function insurance_policy(Request $request)
    {
        $data['title'] = customTrans('insurance_policy.i_insurance_policy');
        $data['lang'] = isset($request->lang) ? $request->lang : 'ar';
        $data['mobile'] = isset($request->mobile) ? $request->mobile : 0;
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.insurance_policy', $data);
        } else {
            return view('pages.insurance_policy', $data);
        }
    }

    public function account_delete(Request $request)
    {
        $data['title'] = customTrans('sign_up.account_delete');
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.account_delete', $data);
        } else {
            return view('pages.account_delete', $data);
        }
    }

    // faq guest
    public function faq_guest(Request $request)
    {
        $data['lang'] = isset($request->lang) ? $request->lang : 'en';
        // $request->session()->put('language', $data['lang']);
        $data['mobile'] = isset($request->mobile) ? $request->mobile : 0;
        $data['title'] = 'Frequently Asked Questions';
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.faq_guest', $data);
        } else {
            return view('pages.faq_guest', $data);
        }
    }

//  faq host
    public function faq_host(Request $request)
    {
        $data['lang'] = isset($request->lang) ? $request->lang : 'en';
        // $request->session()->put('language', $data['lang']);
        $data['mobile'] = isset($request->mobile) ? $request->mobile : 0;
        $data['title'] = 'Frequently Asked Questions';
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.faq_host', $data);
        } else {
            return view('pages.faq_host', $data);
        }
    }

    //  instruction page
    public function instruction(Request $request)
    {
        $data['lang'] = isset($request->lang) ? $request->lang : \Session::get('language');
        \App::setLocale($data['lang']);
        $data['mobile'] = isset($request->mobile) ? $request->mobile : 0;
        $data['title'] = 'Instruction';
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.instruction', $data);
        } else {
            return view('managehost.instruction', $data);
        }
    }


    public function about(Request $request)
    {
        $data['title'] = 'About';
        $data['lang'] = isset($request->lang) ? $request->lang : 'en';
        // $request->session()->put('language', $data['lang']);
        $data['mobile'] = isset($request->mobile) ? $request->mobile : 0;
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.about', $data);
        } else {
            return view('pages.about', $data);
        }
    }

    public function announcement(Request $request)
    {
        $data['title'] = 'Blog';
        $data['lang'] = isset($request->lang) ? $request->lang : 'ar';
        $data['mobile'] = isset($request->mobile) ? $request->mobile : 0;
        // $request->session()->put('language', $data['lang']);
        // $lang = 'ar';
        //  app()->setLocale($lang);
        //     $_SESSION['language'] = $lang;
        // $data['mobile'] = isset($request->mobile) ? $request->mobile : 0;
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.announcement', $data);
        } else {
            return view('pages.announcement', $data);
        }
    }

    public function contact_host(Request $request)
    {
        $data['title'] = 'Contact Host';
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.contact_host', $data);
        } else {
            return view('pages.contact_host', $data);
        }
    }
    // public function day(Request $request)
    // {
    //     $data['title'] = 'Day';
    //     if ($this->helper->isRequestFromMobile($request)) {
    //         return view('mobile.managehost.day', $data);
    //     } else {
    //         return view('managehost.day', $data);
    //     }
    // }
    public function landingPage(Request $request)
    {
        $data['title'] = 'Landing Page';
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.landing-page', $data);
        } else {
            return view('pages.landing-page', $data);
        }
    }

    public function trackLandingVisit(Request $request)
    {
        try{

            $guest_uuid = request('guest_uuid')
            ?? request()->cookie('guest_uuid')
            ?? $_COOKIE['guest_uuid']
            ?? Str::uuid();

            $record = LandingPageVisit::firstOrNew(['guest_uuid' => $guest_uuid]);
            if (!empty($request->cta)) {
                $record->cta = $request->cta;
            }
            if (!empty($request->url)) {
                $record->page_url = $request->url;
            }

            $record->save();

            return response()->json(['status' => 'success', 'record' => $record]);
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'record' => null]);
        }
    }

    // checkout page
    public function checkoutPage(Request $request)
    {
        $data['lang'] = isset($request->lang) ? $request->lang : 'en';
        // $request->session()->put('language', $data['lang']);
        $data['mobile'] = isset($request->mobile) ? $request->mobile : 0;
        $data['title'] = 'Checkout';
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.faq_guest', $data);
        } else {
            return view('property.checkout', $data);
        }
    }

    public function incoming_mail(Request $request)
    {
        $data['title'] = 'Incoming Mail';
        return view('managehost.incoming_mail', $data);
    }

    public function bookingHistory(Request $request)
    {
        switch ($request->status) {
            case 'Expired':
                $params = [['created_at', '<', Carbon::yesterday()], ['status', '!=', 'Accepted']];
                break;
            case 'Current':
                $params = [['start_date', '<=', date('Y-m-d')], ['end_date', '>=', date('Y-m-d')], ['status', 'Accepted']];
                break;
            case 'Upcoming':
                $params = [['start_date', '>', date('Y-m-d')], ['status', 'Accepted']];
                break;
            case 'Completed':
                $params = [['end_date', '<', date('Y-m-d')], ['status', 'Accepted']];
                break;
            case 'Pending':
                $params = [['created_at', '>', Carbon::yesterday()], ['status', $request->status]];
                break;
            case 'Unpaid':
                $params = [['created_at', '>', Carbon::yesterday()], ['status', $request->status]];
                break;
            default:
                $params = [];
                break;
        }
        $propertyFees = PropertyFees::pluck('value', 'field');
        $data['guest_service_charge'] = $propertyFees['guest_service_charge'];
        $data['yesterday'] = Carbon::yesterday();
        $data['status'] = $request->status;
        $data['title'] = 'Booking History';
        $data['bookings'] = Bookings::with('host', 'properties')
            ->where('user_id', Auth::user()->id)
            ->where($params)->orderBy('id', 'desc')
            ->paginate(10);

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.booking_history', $data);
        } else {
            return view('pages.booking_history', $data);
        }
        return view('pages.booking_history', $data);
    }

    public function payment()
    {
        $data['title'] = 'payment';
        return view('payment.payment', $data);
    }


    public function transactions()
    {
        $data['title'] = 'transactions';
        return view('payment.transactions', $data);
    }

    public function new_wishlist()
    {
        $data['title'] = customTrans('wishlist.wishlist');
        return view('wishlist.new_wishlist', $data);
    }

    public function wishlist_listing()
    {
        $data['title'] = customTrans('wishlist.wishlist_listing');
        return view('wishlist.wishlist_listing', $data);
    }


    public function booking_calender()
    {
        $data['title'] = 'booking_calender';
        return view('property.booking_calender', $data);
    }

    public function modals()
    {
        $data['title'] = 'modals';
        return view('pages.modals', $data);
    }

    public function contactadmin(Request $request)
    {
        $data['title'] = 'Contact Admin';
        $data['bookingid'] = $request->bookingid;

        return view('pages.contactadmin', $data);
    }


    // transactions page
    public function transaction_details(Request $request)
    {
        $data['title'] = 'Transaction Details';
        $data['host_wallet'] = HostWallets::where('host_id', Auth::id())->first();
        $data['host_withdrawl'] = HostWithdrawl::where('user_id', Auth::id())->get();

        // dd($data['host_withdrawl']->sum('withdrawl_amount'));

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.transaction_details', $data);
        } else {
            return view('pages.transaction_details', $data);
        }
    }

    public function reservation(Request $request)
    {
        $data['title'] = customTrans('reservation.reservations');
        $userAgent = $request->header('User-Agent');
        $data['showPayOpts'] = strpos($userAgent, 'Safari') == true && strpos($userAgent, 'Chrome') == false && (strpos($userAgent, 'Macintosh') == true || strpos($userAgent, 'iPhone') == true);
        $data['bookings']['upcoming'] = Bookings::with('users', 'properties')
            ->where('user_id', auth()->id())
            ->whereIn('status', ['Processing', 'Pending', 'Unpaid', 'Accepted'])
            ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
            ->latest()->paginate(1);

        // $data['bookings']['upcoming'] = Bookings::with('users', 'properties')->where('user_id', auth()->id())
        // ->where(fn ($q) => $q->where([['status','!=','Cancelled'],['status','!=','Expired'],['status','!=','Declined']])->whereDate('start_date', '>=', $this->now))->latest()->paginate(1);
        $data['bookings']['upcoming']->withPath(route('paginated.bookings', 'upcoming'));

        $data['reviews'] = Reviews::where('sender_id', Auth::id())->get();
        $data['ranges'] = DB::table('bookings as bs')->selectRaw('"" as title, start_date as start, end_date as end')
            ->join('properties as ps', 'ps.id', 'bs.property_id')->where('user_id', auth()->id())->groupBy('start', 'end')
            ->orderBy('start_date')->distinct()->get();

        // if($request->ended_booking ){
        //     $data['bookings']['ended'] = Bookings::with('users', 'properties')->where('user_id', auth()->id())
        //     ->where(fn ($q) => $q->where('status','Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')))->orderBy('id', 'desc')->paginate(5);
        //     $request->merge(['page' => null]);
        //     // $data['bookings']['ended'] = Bookings::with('users', 'properties')->where('user_id', auth()->id())
        //     // ->where(fn ($q) => $q->where([['status','!=','Cancelled'],['status','!=','Expired'],['status','!=','Declined']])->where('end_date', '<', $this->now))->orderBy('id', 'desc')->paginate(5);
        //     // $request->merge(['page' => null]);
        //     $data['bookings']['upcomingtab'] = Bookings::with('users', 'properties')
        //     ->where('user_id', auth()->id())
        //     ->whereIn('status', ['Processing', 'Pending', 'Unpaid','Accepted'])
        //     ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
        //     ->orderBy('id', 'desc')
        //     ->paginate(5);
        //     return view('payment.reservation', $data);
        // }

        $data['bookings']['upcomingtab'] = Bookings::with('users', 'properties')
            ->where('user_id', auth()->id())
            ->whereIn('status', ['Processing', 'Pending', 'Unpaid', 'Accepted'])
            ->where('start_date', '>=', Carbon::now()->format('Y-m-d'))
            ->orderBy('id', 'desc')
            ->paginate(10);
        // $request->merge(['page' => null]);
        $data['bookings']['ended'] = Bookings::with('users', 'properties')->where('user_id', auth()->id())
            ->where(fn($q) => $q->where('status', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')))->orderBy('id', 'desc')->paginate(10);

        $data['bookings']['ongoing'] = Bookings::with('users', 'properties')
            ->where('user_id', auth()->id())
            ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
            ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
            ->where('status', 'Accepted')
            ->orderBy('updated_at', 'desc')
            ->paginate(10);

        $data['bookings']['cancelled'] = Bookings::with('users', 'properties')->where('user_id', auth()->id())
            ->where(fn($q) => $q->where('status', '=', 'Cancelled')->orWhere('status', '=', 'Declined'))->orderBy('updated_at', 'desc')->paginate(10);
        $data['bookings']['expired'] = Bookings::with('users', 'properties')->where('user_id', auth()->id())
            ->where(fn($q) => $q->where('status', '=', 'Expired'))->orderBy('updated_at', 'desc')->paginate(10);

        $propertyFees = PropertyFees::pluck('value', 'field');
        $data['guest_service_charge'] = $propertyFees['guest_service_charge'];
        // $data['bookings']['ended']->paginate(12);;
        // $data['bookings']['ended']->withPath(route('paginated.bookings', 'ended'));

        // dd($data['ranges']);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.payment.reservation', $data);
        } else {
            return view('payment.reservation', $data);
        }
        return view('payment.reservation', $data);
    }


    public function paginatedBookings(Request $request, $type)
    {
        $listEQ = Bookings::with(['users', 'properties' => fn($q) => $q->with('property_photos', 'property_address', 'property_description')])->where('user_id', auth()->id());
        if ($type == 'upcoming') {
            $pList = $listEQ->whereNull('cancelled_at')->whereNull('expired_at')
                ->whereDate('start_date', '>=', $this->now)->latest()->paginate(1, ['*'], 'page', $request->page);
        } else if ($type == 'ended') {
            $pList = $listEQ->where(fn($q) => $q->whereNotNull('cancelled_at')
                ->orWhereNotNull('expired_at')->orWhereDate('start_date', '<', $this->now))->latest()
                ->paginate(5, ['*'], 'page', $request->page);
        } else {
            $pList = $listEQ->whereNull('cancelled_at')->whereNull('expired_at')
                ->whereDate('start_date', '>=', $this->now)->latest()->paginate(5, ['*'], 'page', $request->page);
        }
        $pList->withPath(route('paginated.bookings', $type));
        return response()->json(['list' => [
            'bookings' => $pList->items(),
            'next_url' => $pList->nextPageUrl(),
            'prev_url' => $pList->previousPageUrl(),
            'type' => $type
        ], 'success' => true], 200);
    }

    public function notification()
    {
        $data['title'] = 'notification';
        return view('pages.notification', $data);
    }

    public function wishList()
    {
        $data['title'] = customTrans('wishlist.wishlist');
        $data['bookings'] = Favourite::with(['properties' => function ($q) {
            $q->with('property_address');
        }])->where(['user_id' => Auth::id(), 'status' => 'Active'])->orderBy('id', 'desc')
            ->paginate(Settings::getAll()->where('name', 'row_per_page')->first()->value);
        return view('pages.wishlist', $data);
    }

    public function receipt(Request $request)
    {
        $data['booking'] = Bookings::leftJoin('promo_code_usage', 'bookings.id', '=', 'promo_code_usage.booking_id')
            ->leftJoin('promo_codes', 'promo_codes.id', '=', 'promo_code_usage.promo_code_id')
            ->leftJoin('users', 'bookings.user_id', '=', 'users.id')
            ->select('bookings.*', 'users.*', 'promo_codes.code as promo_code', 'promo_code_usage.discount_type', 'promo_code_usage.discount_value', 'promo_code_usage.original_amount', 'promo_code_usage.after_discount', 'promo_code_usage.is_used')
            ->where('bookings.code', $request->code)->first();


        // if ($data['booking']->user_id != Auth::user()->id && $data['booking']->host_id != Auth::user()->id) {
        //     abort('404');
        // }
        $data['date_price'] = json_decode($data['booking']->date_with_price);
        $data['title'] = 'Payment receipt for';
        $data['url'] = url('/') . '/';
        $data['additional_title'] = $request->code;
        $data['totalPrice'] = $data['booking']->total_with_discount ? $data['booking']->total_with_discount : $data['booking']->total;
        $data['perNightPrice'] = $data['totalPrice'] / $data['booking']->total_night;
        $data['service_charges'] = $data['booking']->service_charge;
        $property_fees_vat = PropertyFees::where('field', 'vat')->first();
        $data['vat_charges'] = ($property_fees_vat->value * $data['booking']->service_charge) / 100;
        $data['total'] = $data['service_charges'] + $data['vat_charges'];
        $data['qr'] = route('generate.receipt.qr', ['code' => $request->code]);

        // return view('pages.receipt', $data);
        $view = view()->make('pages.receipt', $data);
        $content = $view->render();
        $pdf = new \Mpdf\Mpdf([
            'mode' => 'UTF-8',
            'autoScriptToLang' => true,
            'autoLangToFont' => true,
            'allow_external_images' => true,
        ]);
        $stylesheet1 = file_get_contents(public_path('bootstrap/css/bootstrap-grid.min.css'));
        $stylesheet2 = file_get_contents(public_path('bootstrap/css/bootstrap.min.css'));
        $stylesheet3 = file_get_contents(public_path('cdns/css/all.min.css'));
        $stylesheet4 = file_get_contents(public_path('css/responsive.css'));
        $stylesheet5 = file_get_contents(public_path('css/style.css'));
        $pdf->WriteHTML($stylesheet1, \Mpdf\HTMLParserMode::HEADER_CSS);
        $pdf->WriteHTML($stylesheet2, \Mpdf\HTMLParserMode::HEADER_CSS);
        $pdf->WriteHTML($stylesheet3, \Mpdf\HTMLParserMode::HEADER_CSS);
        $pdf->WriteHTML($stylesheet4, \Mpdf\HTMLParserMode::HEADER_CSS);
        $pdf->WriteHTML($stylesheet5, \Mpdf\HTMLParserMode::HEADER_CSS);
        $pdf->AddPage();
        $pdf->writeHTML($content);
        $pdf->autoScriptToLang = true;
        $pdf->autoLangToFont = true;
        return response()->stream(
            function () use ($pdf) {
                echo((string)$pdf->output('receipt.pdf', 'S'));
            },
            200,
            ['Content-type' => 'application/pdf']
        );
    }

    public function hostReceipt(Request $request)
    {
        $data['booking'] = Bookings::leftJoin('promo_code_usage', 'bookings.id', '=', 'promo_code_usage.booking_id')
            ->leftJoin('promo_codes', 'promo_codes.id', '=', 'promo_code_usage.promo_code_id')
            ->select('bookings.*', 'promo_codes.code', 'promo_code_usage.discount_type', 'promo_code_usage.discount_value', 'promo_code_usage.original_amount', 'promo_code_usage.after_discount', 'promo_code_usage.is_used')
            ->where('bookings.code', $request->code)->first();
        $bookingPaymentDetail = BookingPaymentDetails::where('booking_id', $data['booking']->id)->first();
        $data['date_price'] = json_decode($data['booking']->date_with_price);
        $data['title'] = 'Payment receipt for';
        $data['url'] = url('/') . '/';
        $data['additional_title'] = $request->code;
        $data['service_charges'] = $data['booking']->service_charge;
        // $data['hostComission'] = $data['booking']->host->commission;
        $data['hostComission'] = $bookingPaymentDetail->commission_in_percenatge ?? 0;
        $data['insuranceFees'] = $bookingPaymentDetail->insurance_fees_in_percentage ?? 0;
        $data['vat_charges'] = ($data['hostComission'] * $data['booking']->service_charge) / 100;
        $data['total'] = $data['service_charges'] + $data['vat_charges'];
        $data['qr'] = route('generate.receipt.qr', ['code' => $request->code]);
        $view = view()->make('pages.hostreceipt', $data);
        $content = $view->render();
        $pdf = new \Mpdf\Mpdf([
            'mode' => 'UTF-8',
            'autoScriptToLang' => true,
            'autoLangToFont' => true,
            'allow_external_images' => true,
        ]);
        $stylesheet1 = file_get_contents(public_path('bootstrap/css/bootstrap-grid.min.css'));
        $stylesheet2 = file_get_contents(public_path('bootstrap/css/bootstrap.min.css'));
        $stylesheet3 = file_get_contents(public_path('cdns/css/all.min.css'));
        $stylesheet4 = file_get_contents(public_path('css/responsive.css'));
        $stylesheet5 = file_get_contents(public_path('css/style.css'));
        $pdf->WriteHTML($stylesheet1, \Mpdf\HTMLParserMode::HEADER_CSS);
        $pdf->WriteHTML($stylesheet2, \Mpdf\HTMLParserMode::HEADER_CSS);
        $pdf->WriteHTML($stylesheet3, \Mpdf\HTMLParserMode::HEADER_CSS);
        $pdf->WriteHTML($stylesheet4, \Mpdf\HTMLParserMode::HEADER_CSS);
        $pdf->WriteHTML($stylesheet5, \Mpdf\HTMLParserMode::HEADER_CSS);
        $pdf->AddPage();
        $pdf->writeHTML($content);
        $pdf->autoScriptToLang = true;
        $pdf->autoLangToFont = true;
        return response()->stream(
            function () use ($pdf) {
                echo((string)$pdf->output('receipt.pdf', 'S'));
            },
            200,
            ['Content-type' => 'application/pdf']
        );
    }

    public function ReceiptPDF(Request $request)
    {
        $data['booking'] = Bookings::where('code', $request->code)->first();
        $data['date_price'] = json_decode($data['booking']->date_with_price);
        $data['title'] = 'Payment receipt for';
        $data['url'] = url('/') . '/';
        if ($data['booking']->user_id != Auth::user()->id && $data['booking']->host_id != Auth::user()->id) {
            abort('404');
        }
        $data['additional_title'] = $request->code;

        $pdf = PDF::loadView('pages.receipt', $data)->setOptions(['defaultFont' => 'DIN-NEXT-ARABIC']);

        return $pdf->download('receipt' . time() . '.pdf');
    }

    // accounts page
    public function personalinfo()
    {
        $data['title'] = 'personalinfo';
        return view('account.personalinfo', $data);
    }


    // My Request
    public function myrequest()
    {
        $data['title'] = 'myrequest';
        return view('account.myrequest', $data);
    }


    public function amenities()
    {
        $data['title'] = 'amenities';
        return view('listing.amenities', $data);
    }

    public function addphoto()
    {
        $data['title'] = 'addphoto';
        return view('listing.addphoto', $data);
    }

    public function addtitle()
    {
        $data['title'] = 'addtitle';
        return view('listing.addtitle', $data);
    }

    public function adddescription()
    {
        $data['title'] = 'adddescription';
        return view('listing.adddescription', $data);
    }

    public function addprice()
    {
        $data['title'] = 'addprice';
        return view('listing.addprice', $data);
    }

    public function question()
    {
        $data['title'] = 'question';
        return view('listing.question', $data);
    }

    public function getNotifications(Request $request)
    {

        $offset = $request->offset;
        $notifications = auth()->user()->notifications()->whereJsonContainsKey('data->data->slug')->skip($offset)->take(10)->get()
            ->map(fn($noti) => (object)[
                'message' => $noti->data['messages'][app()->getLocale()] ?? $noti->data['messages']['en'],
                'read_at' => $noti->read_at,
                'link' => $noti->data['link'],
                'created_at' => Carbon::parse($noti->created_at, (auth()->user()->timezone ?? config('app.timezone')))->diffForHumans()
            ]);

        if ($this->helper->isRequestFromMobile($request)) {
            $html = view('mobile.pages.notifications-ajax', ['notifications' => $notifications])->render();
        } else {
            $html = view('pages.notifications-ajax', ['notifications' => $notifications])->render();
        }

        // Return the HTML in JSON format
        return response()->json(['html' => $html]);
    }

    public function notifications(Request $request)
    {
        if (auth()->user()->unreadNotifications()) {
            auth()->user()->unreadNotifications()->update(['read_at' => now()]);
        }


        $data['title'] = 'Notifications';
        $data['notifications'] = auth()->user()->notifications()->whereJsonContainsKey('data->data->slug')->take(10)->get()
            ->map(fn($noti) => (object)[
                'message' => $noti->data['messages'][app()->getLocale()] ?? $noti->data['messages']['en'],
                'read_at' => $noti->read_at,
                'link' => $noti->data['link'],
                'created_at' => Carbon::parse($noti->created_at, (auth()->user()->timezone ?? config('app.timezone')))->diffForHumans()
            ]);

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.pages.notification', $data);
        } else {
            return view('pages.notification', $data);
        }
    }

    public function markNotifications()
    {
        if (auth()->user()->unreadNotifications()) {
            auth()->user()->unreadNotifications()->update(['read_at' => now()]);
        }
        // auth()->user()->unreadNotifications()->update(['read_at' => now()]);
        return response()->json(['success' => true]);
    }

    public function UserReview(Request $request)
    {
        $is_api = $request->is('api/*');


        $reviews = Reviews::reviewsList('guest')
            ->where('receiver_id', auth()->id())
            ->orderBy('ps.id', 'desc')
            ->orderBy('reviews.id', 'desc')->cursorPaginate(10);
        $data['reviewaboutyou'] = $reviews->getCollection()->map(function ($review) {
            $temp['reviewer'] = array_combine(
                ['id', 'name', 'profile_image'],
                array_map(fn($v) => $v == 'profile_image' && !$review->{"reviewer_$v"} ? 'icons/user.svg' : $review->{"reviewer_$v"}, ['id', 'name', 'profile_image'])
            );
            $temp['property'] = array_combine(
                ['id', 'name', 'image'],
                array_map(fn($v) => // $v == 'image' && !$review->{"property_$v"} ? 'icons/user.svg' :
                $review->{"property_$v"}, ['id', 'name', 'image'])
            );
            return ['id' => $review->id, 'is_public' => $review->is_public, 'message' => $review->message, 'rating' => $review->rating, 'created_at' => $review->created_at] + $temp;
        });
        $data['next_reviews_about_you'] = $reviews->nextPageUrl();
        $data['reviewbyyou'] = Reviews::with(['users', 'properties'])->where('sender_id', Auth::id())->orderBy('id', 'desc')->get();
        $data['title'] = 'Reviews';
        if ($is_api) {
            return apiResponse($data);
        }
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.account.review', $data);
        } else {
            return view('account.review', $data);
        }
        return view('account.review', $data);
    }

    public function GuestRequirements(Request $request)
    {
        $data['title'] = 'Guest Requirements';
        $data['user'] = User::find(Auth::id());
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.guest_requirement', $data);
        } else {
            return view('guest_requirement', $data);
        }
    }

    public function Cancellation_Policy(Request $request)
    {
        $data['title'] = customTrans('payment.cancel_policy');
        $data['user'] = User::find(Auth::id());

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.cancellation_policy', $data);
        } else {
            return view('cancellation_policy', $data);
        }
    }

    // setting policy
    public function policySetting(Request $request)
    {
        $data['title'] = customTrans('users_profile.policy_setting');
        $data['user'] = User::find(Auth::id());

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.policySetting', $data);
        } else {
            return view('policySetting', $data);
        }
        return view('policySetting', $data);
    }

    // guest bookings
    public function guestBookings(Request $request)

    {
        $data['title'] = 'Guest Bookings';
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.guestBookings', $data);
        } else {
            return view('guestBookings', $data);
        }
    }

    // Host Bookings
    public function hostBookings(Request $request)
    {
        $data['title'] = 'Host Bookings';
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.hostBookings', $data);
        } else {
            return view('hostBookings', $data);
        }
    }


    // Host bookings
    public function payment_detail()
    {
        $data['title'] = 'payment_detail';
        return view('payment.payment_detail', $data);
    }


    // email template views

    public function booking()
    {
        $data['title'] = 'booking';
        return view('emails.booking', $data);
    }

    // alert mesage view

    public function alert_message()
    {
        $data['title'] = 'alert_message';
        return view('pages.alert_message', $data);
    }

    public function create_admin_tickets()
    {
        $data['title'] = 'create_admin_tickets';
        return view('pages.create_admin_tickets', $data);
    }


    //  edit listing
    public function edit_listing()
    {
        $data['title'] = 'edit_listing';
        return view('pages.edit_listing', $data);
    }


    //  saria appartment
    public function sariaapartments()
    {
        $data['title'] = 'Saria Apartments | Darent';
        return view('home.sariaapartments', $data);
    }

    // new inbox
    public function new_inbox()
    {
        $data['title'] = 'New Inbox';
        return view('inbox.new_inbox', $data);
    }

    // new inbox
    public function new_guest_inbox()
    {
        $data['title'] = 'New Guest Inbox';
        return view('inbox.new_guest_inbox', $data);
    }

    //  saria appartment
    public function transaction_failed()
    {
        $data['title'] = 'Transaction Failed';
        return view('pages.transaction_failed', $data);
    }

    //  user role
    public function createAccountManager(Request $request, UserController $user_controller)
    {
        if ($_POST) {
            $validation = Validator::make($request->all(), [
                'first_name' => 'required|max:255',
                'last_name' => 'required|max:255',
                'email' => 'required|email|max:255|unique:users',
                'phone' => 'required|string|regex:/\d+/|min:9|max:12',
                'password' => 'required|min:6',
                'status' => 'required',
                // 'password_confirmation' => 'required'
            ]);

            if ($validation->fails()) {
                return redirect()->back()->withInput()->withErrors($validation);
            }
            // dd($request->all());

            $insertUser = new User;
            $insertUser->parent_id = Auth::user()->id;
            $insertUser->first_name = $request->first_name;
            $insertUser->last_name = $request->last_name;
            $insertUser->email = $request->email;
            $insertUser->phone = $request->phone;
            $insertUser->formatted_phone = '+966' . $request->phone;
            $insertUser->password = bcrypt($request->password);
            $insertUser->status = $request->status;
            $insertUser->role = "Account Manager";
            $insertUser->save();

            $user = User::where('email', $request->email)->first();
            if ($user) {
                $user_controller->wallet($user->id);
            }
            return redirect('account/manager/list')->with('success', 'Account Manager Created Successfully!');
        }
        $data['title'] = 'User Role';
        return view('user_role.role', $data);
    }

    public function editAccountManager(Request $request)
    {
        if (!$_POST) {
            $data['title'] = 'Edit Account Manager';
            $accountManager = User::where('id', $request->id)->where('role', 'Account Manager')->first();
            if (!$accountManager) {
                return view('user_role.account_not_exist');
            }
            $data['account_manager'] = $accountManager;
            return view('user_role.edit_account_manager', $data);
        }
        $validation = Validator::make($request->all(), [
            'first_name' => 'required|max:255',
            'last_name' => 'required|max:255',
            'phone' => 'required|string|regex:/\d+/|min:9|max:12',
            'status' => 'required',
            // 'password_confirmation' => 'required'
        ]);

        if ($validation->fails()) {
            return redirect()->back()->withInput()->withErrors($validation);
        }
        $insertUser = User::find($request->id);
        if ($insertUser) {
            $insertUser->first_name = $request->first_name;
            $insertUser->last_name = $request->last_name;
            $insertUser->phone = $request->phone;
            $insertUser->formatted_phone = '+966' . $request->phone;
            $insertUser->status = $request->status;
            $insertUser->save();
            return redirect('account/manager/list')->with('success', 'Account Manager Updated Successfully!');
        }
        return redirect('account/manager/list')->with('success', 'Account Manager Does Not Exist!');
    }

    public function deleteAccountManager(Request $request)
    {
        // $data['title'] = 'Edit Account Manager';
        $accountManager = User::where('id', $request->id)->where('role', 'Account Manager')->first();
        if (!$accountManager) {
            return view('user_role.account_not_exist');
        }
        $accountManager->delete();
        return redirect('account/manager/list')->with('success', 'Account Manager Deleted!');
    }

    //  user role
    public function listAccountManager()
    {
        $data['title'] = 'Account Manager List';
        $data['account_managers'] = User::where('parent_id', Auth::id())->get();
        return view('user_role.role_list', $data);
    }

    public function deleteAccount()
    {
        $data['title'] = 'Account Manager List';
        $data['account_managers'] = User::where('parent_id', Auth::id())->get();
        return view('user_role.delete_account', $data);
    }


    // Co-Host
    public function coHost(Request $request)
    {
        $data['title'] = customTrans('cohost.cohost_title');
        $data['is_mob'] = $this->helper->isRequestFromMobile($request);
        $data['property'] = Properties::where('property_code', $request->code)->first();
        return view('managehost.coHost', $data);
    }

    // Primary Host
    public function primaryHost(Request $request)
    {
        $data['title'] = 'Primary Host';
        $data['is_mob'] = $this->helper->isRequestFromMobile($request);
        return view('managehost.primaryHost', $data);
    }

    // Co-Host User
    public function coHostUser(Request $request)
    {
        $data['title'] = 'Co Host';
        $data['is_mob'] = $this->helper->isRequestFromMobile($request);
        return view('managehost.coHostUser', $data);
    }


    //  edit listing
    public function bankAccount(Request $request)
    {
        if ($request->isMethod('post')) {

            // dd($request->all());

            $validate = Validator::make(
                $request->all(),
                [
                    'bank_name' => 'required|string|max:60',
                    'account_title' => [
                        'required',
                        'string',
                        'min:3',
                        'max:40',
                        function ($attribute, $value, $fail) {
                            if (str_word_count($value) < 3) {
                                $fail(__('validation.account_title', ['attribute' => $attribute]));
                            }
                        },
                    ],
                    'account_number' => 'required|numeric|digits_between:18,18',
                    'iban' => 'required|string|min:23|max:24',
                    'phone' => 'required|string|regex:/\d+/',
                ]
            );

            if ($validate->fails()) {
                return redirect()->back()->withInput()->withErrors($validate);
            } else {
                $data = [
                    'user_id' => Auth::id(),
                    'bank_name' => $request->bank_name,
                    'account_title' => $request->account_title,
                    'account_number' => $request->account_number,
                    'iban' => $request->iban,
                    'swift_code' => $request->swift_code ?? null,
                    'phone' => preg_replace("/[\s-]+/", "", $request->phone),
                ];

                Bank::updateOrCreate(['user_id' => Auth::id()], $data);
                return redirect()->back()->with('success', 'Bank Added Successfully!');
            }
        }

        $data['title'] = customTrans('sidenav.bank_account');
        $data['bankdata'] = Bank::where('user_id', Auth::id())->first();

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.account.bankAccount', $data);
        } else {
            return view('account.bankAccount', $data);
        }
    }

    public function createPhoneOtp(Request $request, $again = false)
    {
        $language = Session::get('language');
        App::setLocale($language);
        try {
            $rules=[
                'phone' => 'required|string',
                'g-recaptcha-response' => ['required',new ReCaptchaV3('sendOtpCaptcha', config('services.recaptcha_v3.score'))],
            ];
            if (strtolower(env('APP_ENV')) == 'local' || config('services.recaptcha_v3.enable',true) == false) {
                unset($rules['g-recaptcha-response']);
            }
            $validator = Validator::make( $request->all(), $rules, [
                'phone.required' => __('validation.phone.required'),
            ]);

            $ip = $request->header('cf-connecting-ip');
            $visitor_id = $request->header('X-Fingerprint-Visitor-Id');

            $userAgent = $request->header('User-Agent');

            $device = DESKTOP_APP_KEY_NAME;



            if ($validator->fails()) {
                $this->helper->logAttempt(
                    $request->phone,
                    $ip,
                    $visitor_id,
                    'validation_failed',
                    $validator->errors()->toArray()
                );
                return apiResponse('Failure', $validator->errors(), 422);
            }

            //if (str_starts_with($visitor_id, '94PQLBeh')) {
            //    $this->helper->logAttempt(
            //        $request->phone,
            //        $ip,
            //        $visitor_id,
            //        'Same Visitor Id',
            //        ['message' => 'Too many attempts within 60 minutes']
            //    );
            //    return apiResponse('Failure', ['phone' => ['Try again after 1 hour.']], 406);
            //}

            // $attempts = \Illuminate\Support\Facades\DB::table('phone_verification')
            //     ->selectRaw('COUNT(*) as attempt_count')
            //     ->where('visitor_id', $visitor_id)
            //     ->whereRaw("updated_at >= (NOW() + INTERVAL 3 HOUR) - INTERVAL 60 MINUTE")
            //     ->havingRaw('COUNT(*) >= ?', [3])
            //     ->get();

            $settings = OtpSetting::first();
            $blockDuration = $settings->block_duration ?? 60;

                if (app()->environment() == 'prod') {
                    $attempts = FacadesDB::table('phone_verification')
                    ->selectRaw('COUNT(*) as attempt_count')
                    ->where('visitor_id', $visitor_id)
                    ->whereRaw("updated_at >= NOW() - INTERVAL ? MINUTE", [$blockDuration]) // Uses admin-defined duration
                    ->havingRaw('COUNT(*) >= ?', [$settings->max_attempts]) // Uses max attempts from settings
                    ->get();


    //             Check if there are any matching attempts
               // Check if there are any matching attempts
                //if ($attempts->isNotEmpty()) {
                //    $this->helper->logAttempt(
                //        $request->phone,
                //        $ip,
                //        $visitor_id,
                //        'rate_limited',
                //        ['message' => 'Too many attempts within 60 minutes']
                //    );
                //    return apiResponse('Failure', ['phone' => ['Too many attempts. This number is temporarily blocked.']], 406);
                //}

                }


            $otp = PhoneVerification::generateCode();
            $data = [
                'phone' => $request->phone,
                'otp' => $otp,
                'unique_id' => NULL,
                'is_used' => '0',
                'ip' => $request->header('cf-connecting-ip'),
                'visitor_id' => $visitor_id,
                'device' => $device,
                'browser' => $userAgent,
                'guest_uuid' => request('guest_uuid') ?? $request->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null
            ];

            if ($request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************') {
                $data['otp'] = 8888;

                PhoneVerification::create($data);

                return apiResponseWithUser($request->phone, null, 'Success', 200);
            }

            $createdOtp = PhoneVerification::create($data);
            $createdOtp->formatted_phone = $createdOtp->phone;

            if (Str::startsWith($request->phone, '+966')) {

                $sendOtpResponse = $this->helper->sendOtp($createdOtp->phone, $otp);
                if (!$sendOtpResponse) {
                    $this->helper->logAttempt(
                        $request->phone,
                        $ip,
                        $visitor_id,
                        'send_failed',
                        ['message' => 'Invalid Mobile Number']
                    );
                    return apiResponseWithUser($createdOtp->phone, null, 'Invalid Mobile Number', );
                }
            }



//             $createdOtp->notify(new UserNotify(
//                 'system.send.opt.user',
//                 route('home'),
//                 [':code' => $otp]
//             ));

            // $user  = User::where('formatted_phone', $request->phone)->first();

            return apiResponseWithUser($createdOtp->phone, null, 'Success', 200);
            // return apiResponse($createdOtp->phone,'Success', 200);
        } catch (\Throwable $th) {
            $errorType = 'system_error';
            if ($th->getCode() == 483) {
                $errorType = 'messaging_service_error';
            }
            $this->helper->logAttempt(
                $request->phone ?? null,
                $ip ?? null,
                $visitor_id ?? null,
                $errorType,
                ['message' => $th->getMessage()]
            );
            if ($th->getCode() == 483) {
                return apiResponse('Failure', ['phone' => ['Unable to send message try another login method.']], 422);
            }
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function verifyPhoneOtp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'phone' => 'required|string|exists:phone_verification,phone',
                'otp' => 'required'
            ], [
                'phone.required' => 'Mobile number is required.',
                'otp.required' => 'Otp is required.',
                'phone.exists' => 'Mobile Number does not exists.'
            ]);

            if ($validator->fails()) {
                return apiResponse('Failure', $validator->errors(), 422);
            }

            // dd(isset($request->cohost) && $request->cohost != "null");
            $checkOtp = PhoneVerification::where('phone', $request->phone)
                ->where('otp', $request->otp)
          ##      ->where('ip', $request->header('cf-connecting-ip'))
                ->latest('updated_at') // Retrieve the latest record based on created_at
                ->first();
            if (isset($checkOtp)) {
                $checkOtp->unique_id = Str::random(10);
                $checkOtp->is_used = '1';
                $checkOtp->ip = $request->header('cf-connecting-ip');
                $checkOtp->save();

                $userPhone = $checkOtp->phone;
                $user = User::where('formatted_phone', $userPhone)->first();

                if ($user) {
                    $accountDeleteCheck = AccountDeleteRequest::where('user_id', $user->id)->where('status', '!=', AccountDeleteRequest::PENDING)->exists();
                    if ($accountDeleteCheck) {
                        $data = [
                            'user' => null,
                            'token' => $checkOtp->unique_id,
                            'cohost' => $request->cohost != "null" ? $request->cohost : null
                        ];

                        return apiResponse('Failure', ["otp" => ["User is not found"]], 422);
                    }

                    $user_verification = UsersVerification::firstOrCreate(['user_id' => $user->id]);
                    $user_verification->phone = 'yes';
                    $user_verification->save();

                    if (isset($request->cohost) && $request->cohost != "null") {

                        $checkCohostToken = CoHostRequest::where('token', $request->cohost)->first();
                        if (isset($checkCohostToken) && $user->formatted_phone == $checkCohostToken->formatted_phone) {
                            $coHost = new PropertiesCohost;
                            $coHost->property_id = $checkCohostToken->property_id;
                            $coHost->host_id = $checkCohostToken->host_id;
                            $coHost->co_host_id = $user->id;
                            $coHost->co_host_request_id = $checkCohostToken->id;
                            $coHost->save();

                            $checkCohostToken->expire_at = Carbon::now();
                            $checkCohostToken->status = "Accepted";
                            $checkCohostToken->save();
                        }
                    }

                    $data = [
                        'user' => $user,
                        'token' => $user->createToken('API Token')->accessToken,
                    ];

                    Auth::login($user);
                    return apiResponse($data, 'Success', 200);
                }
                else{
                    $checkForceSignUp = Settings::where('name','force_signup')->first();
                    if($checkForceSignUp->value == "0") {
                        $signupData['first_name'] = "New";
                        $signupData['last_name'] = "User";
                        $signupData['is_guest'] = true;
                        $signupData['token'] = $checkOtp->unique_id;

                        $request = request()->merge($signupData);
                        $user_controller = app(UserController::class);
                        $email_controller = app(EmailController::class);

                        $signUpResponse = $this->SignUp($request, $user_controller, $email_controller);
                        $responseData = json_decode($signUpResponse->getContent());

                        if ($responseData->status === 200) {
                            $userAfterSignUp = User::where('formatted_phone', $checkOtp->phone)->first();
                            $userAfterSignUp->last_name = "User-".$userAfterSignUp->id;
                            $userAfterSignUp->save();
                            if ($userAfterSignUp) {
                                Auth::login($userAfterSignUp);
                                $data = [
                                    'user' => $userAfterSignUp,
                                    'token' => $checkOtp->unique_id,
                                    'cohost' => $request->cohost != "null" ? $request->cohost : null
                                ];
                            } else {
                                return apiResponse('Failure', ["otp" => ["User is not found"]], 422);
                            }
                        }
                    }else{

                        $data = [
                            'user' => null,
                            'token' => $checkOtp->unique_id,
                            'cohost' => $request->cohost != "null" ? $request->cohost : null
                        ];

                        return apiResponse($data, 'Success', 200);

                    }

                }

            } else {
                return apiResponse('Failure', ["otp" => ["Otp Does Not Matched"]], 422);
            }
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function verifyPhoneChangeOtp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'otp'  => 'required|exists:phone_verification,otp',
                'phone' => 'required|string|exists:phone_verification,phone',

            ], [
                'phone.required' => 'Mobile number is required.',
                'otp.required' => 'Otp is required.',
                'phone.exists' => 'Mobile Number does not exists.'
            ]);
            if ($validator->fails()) {
                return apiResponse('Failure', $validator->errors(), 422);
            }

            $checkOtp = PhoneVerification::where('phone', $request->phone)->latest()->first();
            if ($checkOtp->otp == $request->otp) {
                $checkOtp->unique_id = Str::random(10);
                $checkOtp->is_used = '1';
                $checkOtp->save();

                $user = auth()->user();
                $user->phone = $checkOtp->phone;
                $user->formatted_phone = $checkOtp->phone;
                $user->save();

                $user_verification = UsersVerification::firstOrCreate(['user_id' => $user->id]);
                $user_verification->phone = 'yes';
                $user_verification->save();

                return apiResponse(null, 'Success', 200);
            } else {
                return apiResponse("Failure", "Otp does not matched", 422);
            }
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getTrace(), 500);
        }
    }

    public function SignUp(Request $request, UserController $user_controller, EmailController $email_controller)
    {
        DB::beginTransaction();
        try {
            $validator = Validator::make($request->all(), [
                'first_name' => 'required|max:255',
                'last_name' => 'required|max:255',
                'email' => 'email|max:255|nullable|unique:users,email',
                'token' => 'required',
                'password' => 'confirmed',
                'password_confirmation' => ''
            ], [
                'first_name.required' => 'First name is required.',
                'last_name.required' => 'Last name is required.',
                'email.required' => 'Email is required.',
                'token.required' => 'Token is required.',
                'password.required' => 'Password is required.',
            ]);

            if ($validator->fails()) {
                return apiResponse('Failure', $validator->errors(), 422);
            }
            // $checkEmailExist = User::where('email', $request->email)->where('is_email_verified', 1)->first();
            // if (isset($checkEmailExist)) {
            //     return apiResponse('Failure', ["email" => ["Email Already Exist"]], 422);
            // }
            // dd(isset($request->cohost));
            $phoneVerificationUpdate = PhoneVerification::where('unique_id', $request->token)->first();

            if (!isset($phoneVerificationUpdate)) {
                return apiResponse('Failure', "Invalid Token", 422);
            }

            $user = new User();
            $user->first_name = $request->first_name;
            $user->last_name = $request->last_name;
            $user->email = isset($request->email) ? $request->email : null;
            $user->is_guest = isset($request->is_guest) ? true : false;
            $user->phone = $phoneVerificationUpdate->phone;
            $user->profile_image = "";
            $user->formatted_phone = $phoneVerificationUpdate->phone;
            // $user->password = bcrypt($request->password);
            $user->password = isset($request->password) ? bcrypt($request->password) : bcrypt('darent@2020');
            $user->is_email_verified = 1;
            $user->save();

            $user = $user->fresh();
            if ($phoneVerificationUpdate) {
                $phoneVerificationUpdate->unique_id = null;
                $phoneVerificationUpdate->save();
            }
            if (isset($request->cohost)) {
                $checkCohostToken = CoHostRequest::where('token', $request->cohost)->first();

                if (isset($checkCohostToken) && $user->formatted_phone == $checkCohostToken->formatted_phone) {

                    $coHost = new PropertiesCohost;
                    $coHost->property_id = $checkCohostToken->property_id;
                    $coHost->host_id = $checkCohostToken->host_id;
                    $coHost->co_host_id = $user->id;
                    $coHost->co_host_request_id = $checkCohostToken->id;
                    $coHost->save();

                    $checkCohostToken->expire_at = Carbon::now();
                    $checkCohostToken->status = "Accepted";
                    $checkCohostToken->save();
                }
            }

            $user_verification = UsersVerification::firstOrCreate(['user_id' => $user->id]);
            $user_verification->phone = 'yes';
            $user_verification->save();

            if (isset($request->email)) {
                $otp = EmailVerification::generateCode();

                $emailVerifyData = [
                    'email' => $user->email,
                    'otp' => $otp,
                    'is_used' => '0',
                ];
                $createdOtp = EmailVerification::updateOrCreate(['email' => $user->email], $emailVerifyData);
            }

            DB::commit();
            // $user_controller->wallet($user->id);
            if (isset($request->noToken)) {
                $loggedIn = Auth::login($user);
                return apiResponse($loggedIn, 'Success', 200);
            }

            $token = $user->createToken('API Token')->accessToken;

            $data = [
                'user' => $user,
                'token' => $token,
                'email_otp' => isset($createdOtp->otp) ? $createdOtp->otp : null,
            ];
            Auth::login($user);
            if (isset($request->email)) {
                SendOtpEmailJob::dispatch($user->email, $otp)->onQueue('emails');
            }
            // Mail::to($user->email)->send(new OtpMail($otp));
            return apiResponse($data, 'Success', 200);
        } catch (\Throwable $th) {
            DB::rollback();
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function emailVerifyOtp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'otp' => 'required'
            ], [

                'otp.required' => 'Otp is required.',
            ]);

            if ($validator->fails()) {
                return apiResponse('Failure', $validator->errors(), 422);
            }
            $checkOtp = EmailVerification::where('email', Auth::guard('web')->user()->email)->where('otp', $request->otp)->first();
            if ($checkOtp) {
                $checkOtp->is_used = '1';
                $checkOtp->save();

                $user = User::find(Auth::guard('web')->user()->id);
                $user->is_email_verified = 1;
                $user->save();

                $user_verification = UsersVerification::firstOrCreate(['user_id' => $user->id]);
                $user_verification->email = 'yes';
                $user_verification->save();

                return apiResponse(null, 'Success', 200);
            } else {
                return apiResponse("Failure", "Otp does not matched", 422);
            }
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function verifyEmailChangeOtp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'otp' => 'required|exists:email_verification,otp',
                'email' => "required|email|exists:email_verification,email,otp,$request->otp|unique:users,email," . auth()->id()
            ], [

                'otp.required' => 'Otp is required.',
                'email.exists' => 'Invalid otp given.'
            ]);
            if ($validator->fails()) {
                return apiResponse('Failure', $validator->errors(), 422);
            }
            $checkOtp = EmailVerification::where('otp', $request->otp)->first();
            if ($checkOtp) {
                $checkOtp->is_used = '1';
                $checkOtp->save();

                $user = User::find(Auth::guard('web')->user()->id);
                $user->email = $checkOtp->email;
                $user->is_email_verified = 1;
                $user->save();

                $user_verification = UsersVerification::firstOrCreate(['user_id' => $user->id]);
                $user_verification->email = 'yes';
                $user_verification->save();

                return apiResponse(null, 'Success', 200);
            } else {
                return apiResponse("Failure", "Otp does not matched", 422);
            }
        } catch (\Throwable $th) {
            return apiResponse('Failure', $th->getTrace(), 500);
        }
    }

    public function resendEmailOtp(Request $request)
    {
        try {
            $otp = EmailVerification::generateCode();

            $userEmail = Auth::guard('web')->user()->email;
            // dd($userEmail);
            $emailVerifyData = [
                'email' => $userEmail,
                'otp' => $otp,
                'is_used' => 0,
            ];

            $createdOtp = EmailVerification::updateOrCreate(['email' => $userEmail], $emailVerifyData);
            SendOtpEmailJob::dispatch($userEmail, $otp)->onQueue('emails');
            // Mail::to($userEmail)->send(new OtpMail($otp));
            return apiResponse(null, 'Success', 200);
        } catch (\Throwable $th) {
            DB::rollback();
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }


    // host listing
    public function changeEmail(Request $request)
    {
        try {
            $user = auth()->user();
            $validator = Validator::make($request->all(), [
                'email' => "required|email|max:255|unique:users,email,{$user->id}"
            ]);

            if ($validator->fails()) {
                return apiResponse('Failure', $validator->errors(), 422);
            }
            $otp = EmailVerification::generateCode();

            $email = strtolower($request->email);
            if ($user->email == $email) {
                return apiResponse(null, 'Success', 204);
            }
            $emailVerifyData = [
                'email' => $email,
                'otp' => $otp,
                'is_used' => 0,
            ];

            EmailVerification::updateOrCreate(['email' => $email], $emailVerifyData);
            SendOtpEmailJob::dispatch($email, $otp)->onQueue('emails');
            return apiResponse(null, 'Success', 200);
        } catch (\Throwable $th) {
            DB::rollback();
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }

    public function changePhone(Request $request)
    {
        try {

            $user = auth()->user();
            $validator = Validator::make($request->all(), [
                'phone' => 'required|string',
            ]);

            if ($validator->fails()) {
                return apiResponse('Failure', $validator->errors(), 422);
            }
            $request->phone = $request->code.$request->phone;
            $otp = PhoneVerification::generateCode();
            if ($request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************' || $request->phone == '+************') {
                $otp = 8888;
            }

            $data = [
                'phone' => $request->phone,
                'otp'  => $otp,
                'unique_id' => NULL,
                'is_used' => '0',
                'ip' => $request->ip(),
                'guest_uuid' => request('guest_uuid') ?? $request->cookie('guest_uuid') ?? $_COOKIE['guest_uuid'] ?? null
            ];

            $createdOtp = PhoneVerification::create($data);
            $code = $createdOtp->otp;
            $createdOtp->formatted_phone = $createdOtp->phone;
            $createdOtp->notify(new UserNotify(
                'system.send.opt.user',
                route('home'),
                [':code' => $code]
            ));

            // $user  = User::where('formatted_phone', $request->phone)->first();

            // return apiResponseWithUser($createdOtp->phone, $user != null ? $user : null, 'Success', 200);
            return apiResponse($createdOtp->phone,'Success', 200);
        } catch (\Throwable $th) {
            DB::rollback();
            return apiResponse('Failure', $th->getMessage(), 500);
        }
    }


    public function all_reservation(Request $request)
    {
        $data['title'] = customTrans('host_dashboard.reservation');
        $data['is_mob'] = $this->helper->isRequestFromMobile($request);
        $data['lang'] = LocalizationKeyword::select(app()->getLocale() . '_value AS value')->where('parent_key', 'host_reservation')->first();
        $data['lang'] = !!$data['lang'] ? $data['lang']->value : '{}';
        return view('managehost.all_reservation', $data);
    }

    public function host_listings_backup(Request $request)
    {
        $data['title'] = 'All Reservation';
        return view('managehost.host_listings_backup', $data);
    }

    public function host_listings_details(HostReservationDataTable $dataTable, Request $request)
    {
        $data['title'] = customTrans('host_dashboard.host_listing_details');

        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.host_listings_details', $data);
        } else {
            return view('managehost.host_listings_details', $data);
        }
    }


    public function host_transaction_history()
    {
        $data['title'] = 'Host Transaction History';
        return view('managehost.host_transaction_history', $data);
    }


    public function host_settings(Request $request)
    {
        $data['title'] = customTrans('host_dashboard.host_setting');
        // customTrans('host_dashboard.host_setting')
        // return view('managehost.host_settings', $data);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.host_settings', $data);
        } else {
            return view('managehost.host_settings', $data);
        }
    }

    public function payments_and_payouts(Request $request)
    {
        $data['title'] = customTrans('host_dashboard.payment_and_payouts');
        // return view('managehost.payments_and_payouts', $data);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.payments_and_payouts', $data);
        } else {
            return view('managehost.payments_and_payouts', $data);
        }
    }


    public function login_and_security()
    {
        $data['title'] = customTrans('host_dashboard.host_listing_details');
        return view('managehost.login_and_security', $data);
    }

    public function payment_and_payouts_main(Request $request)
    {
        $data['title'] = customTrans('host_dashboard.payment_and_payouts');
        // return view('managehost.host_notifications', $data);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.payment_and_payouts_main', $data);
        } else {
            return view('managehost.payment_and_payouts_main', $data);
        }
    }

    public function payment_methods(Request $request)
    {
        $data['title'] = customTrans('wallet.payment_method');
        return view('mobile.managehost.payment_methods', $data);
    }


    public function host_notifications(Request $request)
    {
        $data['title'] = 'Host Notifications';
        // return view('managehost.host_notifications', $data);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.host_notifications', $data);
        } else {
            return view('managehost.host_notifications', $data);
        }
    }

    public function privacy_and_sharing(Request $request)
    {
        $data['title'] = 'Privacy and Sharing';
        return view('managehost.privacy_and_sharing', $data);
    }

    public function global_preferences(Request $request)
    {
        $data['title'] = customTrans('host_dashboard.global_preferences');
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.global_preferences', $data);
        } else {
            return view('managehost.global_preferences', $data);
        }
    }

    public function host_translation(Request $request)
    {
        $data['title'] = 'Host Translation';
        // return view('managehost.host_translation', $data);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.host_translation', $data);
        } else {
            return view('managehost.host_translation', $data);
        }
    }

    public function payouts(Request $request)
    {
        $data['title'] = 'Payments & Payout';
        // return view('managehost.payouts', $data);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.payouts', $data);
        } else {
            return view('managehost.payouts', $data);
        }
    }


    public function transaction_history(Request $request)
    {
        $data['title'] = 'Transaction history';
        // return view('managehost.transaction_history', $data);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.transaction_history', $data);
        } else {
            return view('managehost.transaction_history', $data);
        }
    }

    public function taxes(Request $request)
    {
        $data['title'] = 'Taxes';
        // return view('managehost.taxes', $data);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.taxes', $data);
        } else {
            return view('managehost.taxes', $data);
        }
    }

    public function privacy_and_sharing_inner(Request $request)
    {
        $data['title'] = 'Privacy and Sharing';
        // return view('managehost.privacy_and_sharing_inner', $data);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.privacy_and_sharing_inner', $data);
        } else {
            return view('managehost.privacy_and_sharing_inner', $data);
        }
    }

    public function payment_return(Request $request)
    {
        $data['title'] = 'Payment Return';
        // return view('managehost.payment_return', $data);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.payment_return', $data);
        } else {
            return view('managehost.payment_return', $data);
        }
    }

    public function your_payments(Request $request)
    {
        $data['title'] = 'Your Payment';
        // return view('managehost.your_payments', $data);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.your_payments', $data);
        } else {
            return view('managehost.your_payments', $data);
        }
    }

    public function host_calendar(Request $request)
    {
        $data['lang'] = json_encode(LocalizationKeyword::select(app()->getLocale() . '_value AS value')->whereIn('parent_key', ['host_listing', 'host_reservation'])->get()->map(fn($l) => json_decode($l->value, true))->collapse());
        $data['title'] = customTrans('contact_host.listing_calendar');
        $data['is_mob'] = $this->helper->isRequestFromMobile($request);
        return view('managehost.host_calendar', $data);
    }

    public function photos(Request $request)
    {
        $data['title'] = 'Your Payment';
        // return view('managehost.photos', $data);
        if ($this->helper->isRequestFromMobile($request)) {
            return view('mobile.managehost.photos', $data);
        } else {
            return view('managehost.photos', $data);
        }
    }


    public function host_listings_mobile(Request $request)
    {
        $data['title'] = customTrans('host_dashboard.host_listing_details');
        return view('mobile.managehost.host_listings_mobile', $data);
    }


    function newReviews()
    {

        try {
            // Execute the SQL query
            $results = DB::table('reviews')
                ->select(DB::raw('AVG(rating) as avg_rating'), 'property_id')
                ->whereDate('created_at', now()->toDateString()) // Filter for today's date
                ->groupBy('property_id')
                ->get();

            dump(count($results));

            // Update the rating_avg column for each property in a loop
            $update = [];
            foreach ($results as $result) {
                $update[] = DB::table('properties')
                    ->where('id', $result->property_id)
                    ->update(['rating_avg' => round($result->avg_rating, 1)]);
            }
            dump(count($update));


            // Optionally, you can return a response or redirect as needed
            return response()->json(['message' => 'Rating averages updated successfully']);
        } catch (\Exception $e) {
            // Handle any exceptions or errors here
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    function check()
    {
        return view('check');
    }
    //     $users = [
    //         [
    //             "first_name" => "abdullah",
    //             "last_name" => "amjad",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "Nasser",
    //             "last_name" => "amjad",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "Shrouq",
    //             "last_name" => "Anzi",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "محمد",
    //             "last_name" => "قنطار",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "ايمان",
    //             "last_name" => "احمد",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "حكايه",
    //             "last_name" => "احمد",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "Mustufa",
    //             "last_name" => "Waleed",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "sharukh",
    //             "last_name" => "qazi",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "rawan",
    //             "last_name" => "yahia",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "ابو",
    //             "last_name" => "خالد",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "صباح",
    //             "last_name" => "عيضه",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "abu",
    //             "last_name" => "nawaf",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "زهرة",
    //             "last_name" => "الصبار",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "Sham",
    //             "last_name" => "Alord",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "Abdul",
    //             "last_name" => "Rehman",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "بندر",
    //             "last_name" => "الصبار",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "Saleem",
    //             "last_name" => "Khalid",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "mohammed",
    //             "last_name" => "ali",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "nada",
    //             "last_name" => "khan",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "saleh",
    //             "last_name" => "ahmed",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "احمد",
    //             "last_name" => "الصبار",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "فوفو",
    //             "last_name" => "الصبار",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "منال",
    //             "last_name" => "الصبار",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [

    //             "first_name" => "فهد",
    //             "last_name" => "الصبار",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "امل",
    //             "last_name" => "فهد",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "Tulip",
    //             "last_name" => "Shaikh",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "farah",
    //             "last_name" => "qaazmi",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "noora",
    //             "last_name" => "sheikh",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "abu",
    //             "last_name" => "bilal",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "jamal",
    //             "last_name" => "qazi",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ],
    //         [
    //             "first_name" => "fahad",
    //             "last_name" => "zoha",
    //             "email" => "<EMAIL>",
    //             "carrier_code" => "+966",
    //             "status" => 1
    //         ]
    //     ];
    //     $reviews = [
    //         [
    //             "property_code" => "6LE-756",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "المضيف لطيف جدا والمسكن واسع ونظيف ويستحق القيمة المحددة بالعرض"
    //         ],
    //         [
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "الشقة نظيفة في مجمع سكني متكامل(نادي-سوبر ماركت-كافيه-صالة ألعاب)\nالموقع جيد بعيد عن ضوضاء المدينة\nصاحب الشقة متعاون جدا وخلوق ومضياف\nمنطقة جديدة ومبنى جديد وهذا شي مناسب بالنسبة لي"
    //         ],
    //         [
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "هذي هي الإقامه الثانية في نفس الشقة. كل شي جميل ونظيف في المكان والتجربه رائعة. شكرا للمضيف Darent على حسن الضيافه."
    //         ],
    //         [
    //             "property_code" => "M7X-223",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "من افضل الشاليهات اللي رحت لها نظافه وريحة المنظفات ماشاء الله وتعاملهم جداً راقي وجميل مو اخر زياره ❤️😘 …"
    //         ],
    //         [
    //             "cleanliness" => 3,
    //             "accuracy" => 4,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 4,
    //             "message" => "أخذنا الشاليه تكيف المجلس العربي سيء جدا الثلاجة خربانة المسبح الداخلي جميل"
    //         ],
    //         [
    //             "property_code" => "JUX-434",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "مكان جميل لقضاء وقت ترفيهي\nيتميز بتصميم ملموم وانيق مناسب لعائلة صغيرة يمتاز بالنظافة وتعامل راقي من الطاقم\nاغلب اثاثه من ايكيا  مدروسه تفاصيل توزيع الاثاث والمساحات بشكل ملفت\nيوجد غلايه وثلاجه وفرن كهربا وعيون كهربا ومسبح  وجلسه خارجيه وداخليه مع تلفزيون وغرفه نوم ومغسله وحمام\nمن المقترحات التي نقترحها على المالك هي اضافة فتحات تصريف حوالين المسبح لضمان عدم انزلاقيه الارض المحيطه  وتوفير استشوار حمام  وصيانه تهويه دورة المياه ووضع ادوات امان  اسفنجيه على حواف الاثاث لضمان سلامة الصغار وانقاص برودة التكييف قليلا"
    //         ],
    //         [
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 3,
    //             "location" => 2,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "لايوجد لديهم حجز مسبق الا بالحضور للمكتب اللي يبعد عني قرابة الاربعين دقيقه! ولا رقم حساب بنكي لتحويل المبلغ لتأكيد الحجز او عن طريق تطبيقات اللي ما حصلت حجز فيه."
    //         ],
    //         [
    //             "property_code" => "3ZW-176",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "المكان نظيف ومرتب مريح وموقع امن وهوست ما قصر يسهل كل شي وسرعة استجابة"
    //         ],
    //         [
    //             "property_code" => "0QW-031",
    //             "cleanliness" => 3,
    //             "accuracy" => 3,
    //             "communication" => 2,
    //             "location" => 3,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 3,
    //             "message" => "اللي ناوين يستأجرون الشاليه رقم ١ بوضح لكم كم نقطه.\nالشاليه بشكل عام نظيف لكن\n١- المسبح غير نظيف والباب حقه مكسور يعني لو معكم اطفال وتبغون مسبح مقفل لا تاخذونه\n٢- جميع الحمامات الله يعزكم بيبانها م تتقفل وفيه حمام بابه خربان ومقفلينه\n٣- ثلاجة الشاليه صغيره بشكل م تتخيلونه حتى صينية حلا م تدخل فيها\n٤- الغرفه الخارجيه بابها مكسور والتلفزيون اللي فيها غير موجود وباب غرفة النوم بعد مكسور من الجنب\n٥-المكيفات بشكل عام ما تبرد مره\nوفوق هذا كله كان موعد دخولنا للشاليه الساعه ٣ وحضرنا ع الوقت وصاحب الشاليه تأخر علينا م يقارب ١٥-٢٠ دقيقه على بال ما دخلنا ! تركنا ننتظر بالشمس بدون سبب"
    //         ],
    //         [
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "اخذته قبل اربع ايام كلمة يجننن قليل والله يهبل والحارس  و صاحبة الشاليه اسلوب يهبل راقي ويستاهل كل ريال الصراحه"
    //         ],
    //         [
    //             "property_code" => "ZVZ-281",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "ما شاء الله الشالية نظيف و مرتب وواسع واسعارة ممتازة ويستاهل نكرر الزيارة ومشكور ابو فيصل قمه الذوق و الاحترام"
    //         ],
    //         [
    //             "property_code" => "ZVZ-281",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "شاليه جميل مسبح صغير يناسب الاطفال اكثر وراعي شاليه والمنسق محترمين ان شالله اكررر تجربه فيه"
    //         ],
    //         [
    //             "property_code" => "93W-178",
    //             "cleanliness" => 4,
    //             "accuracy" => 2,
    //             "communication" => 3,
    //             "location" => 1,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 4,
    //             "message" => "جيد ولكن الموقع بعيد ومعانه من ضعف الشبكه"
    //         ],
    //         [
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "المكان جميل"
    //         ],
    //         [
    //             "property_code" => "SWN-635",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "شاليه مرتب كتير وحلو ونضيف والحارس كتير متعاون معنا وتعامله حلو ماشاءالله ربي يرزقكم ويبارك لكم بس شبكة زين ضعيفة سوا حلوة"
    //         ],
    //         [
    //             "property_code" => "VDB-633",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "رائع وجميل اشكر المضيف "
    //         ],
    //         [
    //             "property_code" => "4TJ-877",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "مكان جميل جداً ونظيف للامانه اتمنى للجميع الزيارة"
    //         ],
    //         [
    //             "cleanliness" => 3,
    //             "accuracy" => 3,
    //             "communication" => 3,
    //             "location" => 3,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 3,
    //             "message" => "جميله واثاثها نظيف يتوفر فيها اغلب الخدمات من ثلاجه وفرن كهربائي ومايكرويف وسماعات كبير ...الملاحظه اللي ازعجتنا في الشاليه الجاكوزي خربان و المسبح الداخلي مويته ترجع على الصاله والممرات اذا فيه احد يسبح ..\n.المفترض فيه صفايات كبيره وسياج زجاجي حول المسبح بدل الاعمده الحديديه هذي النقطه ازعجتنا كثيييير وسببت مشاكل انزلاقات للاطفال"
    //         ],
    //         [
    //             "cleanliness" => 4,
    //             "accuracy" => 4,
    //             "communication" => 4,
    //             "location" => 4,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 4,
    //             "message" => "واسع وجميل وهادئ ونظيف ولكن مبالغ في السعر رغم بعده عن الرياض"
    //         ],
    //         [
    //             "property_code" => "BKH-681",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "شقه نظيفه وجميله اشكر المضيف على الحسن الاستقبال"
    //         ],
    //         [
    //             "property_code" => "TLA-490",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "good"
    //         ],
    //         [
    //             "property_code" => "ZBR-179",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "الشاليه جميل جداً من ناحيه النظافة 10\/10 و المكان شرح  والاثاث نظيف .. الألعاب المائيه جداً مناسبه للأطفال و لا ينخاف عليهم .. وتصميمه رايق ورائحته جميلة يتوفر فيه ألعاب أطفال وألعاب مائيه\nوتعامل صاحب الشالية  جداً راقي ومتواصل معنا حتى وصلنا واستلمنا الشاليه و متعاون الله يبارك له  ويرزقه ويزيده من فضله يارب ..شكراً لصاحب الشاليه .. من ناحيه التعامل ✅  راح أكرر الزيارة بإذن الله 👍🏼 وانصح الجميع وبقوه وعلى مسؤوليتي 👌🏻"
    //         ],
    //         [
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "المكان جدا نظيف و مرتب  حجزتها كم مره ولاغلطه بالمكان والمسبح مره نظيف وفيه رذاذ جميل ومن ناحيه الاسعار مرررره مناسبه"
    //         ],
    //         [
    //             "cleanliness" => 3,
    //             "accuracy" => 3,
    //             "communication" => 3,
    //             "location" => 3,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 3,
    //             "message" => "جداً سيئ بصراحه حجزت قبل كم يوم المكان قذر بمعنى الكلمه الحمامات المسبح كلور مع مويا وسخه المكان يوجد فيه ارواح جن مرتين يطلع علينا طفل صغير في غرفة الالعاب والمراجيح تتحرك بشكل مخيف مع صراخ طفل صغير"
    //         ],
    //         [
    //             "property_code" => "KVY-421",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "الشقه جميله وهادئ "
    //         ],
    //         [
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "جد اشكر المضيفه على حسن الاستقبال وهذه مو اخر زياره "
    //         ],
    //         [
    //             "property_code" => "GMM-927",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "شاليه يفتح النفس نظيف وجديد واثاث رااايق اهنيهم عليه"
    //         ],
    //         [
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "جميييل وتعامل جمييييل 👍🏻 …"
    //         ],
    //         [
    //             "property_code" => "SBE-717",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "الشاليه ماشاء الله جميل وكبير ومرتب ونظيف ويصلح للعوائل الكبيره"
    //         ],
    //         [
    //             "cleanliness" => 3,
    //             "accuracy" => 3,
    //             "communication" => 3,
    //             "location" => 3,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 3,
    //             "message" => "جيد جدا"
    //         ],
    //         [
    //             "cleanliness" => 4,
    //             "accuracy" => 4,
    //             "communication" => 4,
    //             "location" => 4,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 4,
    //             "message" => "شالية جميلة وطبيعية ودية"
    //         ],
    //         [
    //             "property_code" => "O2C-118",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "المكان نفس الصور ونظيف"
    //         ],
    //         [
    //             "property_code" => "ST7-249",
    //             "cleanliness" => 4,
    //             "accuracy" => 4,
    //             "communication" => 4,
    //             "location" => 4,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 4,
    //             "message" => "ممتاز جدا"
    //         ],
    //         [
    //             "property_code" => "XKJ-405",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "اشكر المضيف على حسن الاستقبال وسرعة التجاوب "
    //         ],
    //         [
    //             "property_code" => "7XX-550",
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "منتجع جميل الصراحه واهتمام من المالك واضح بالمكان ، يستحق الزيارة"
    //         ],
    //         [
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "مكان نظيف ومرتب ومتعوب عليها يستاهل اكثر من خمس نجوم وتعاملهم جداً راقي\nمشكورين اتمنى يحافظون على نفس المستوى من نظافه وترتيب وتعامل الله يرزقهم"
    //         ],
    //         [
    //             "cleanliness" => 5,
    //             "accuracy" => 5,
    //             "communication" => 5,
    //             "location" => 5,
    //             "booking_id" => 0,
    //             "ispublic" => 1,
    //             "reviewer" => "guest",
    //             "checkin" => 5,
    //             "message" => "المكان شرح و مرتب و أنيق ، و صاحب المنتجع جدا راقي في التعامل و محترم 👌🏼 …"
    //         ]
    //     ];
    //     if (!!User::where('first_name', 'noora')->where('last_name', 'sheikh')->first()) {
    //         return 'already created';
    //     }
    //     $reviews = array_filter($reviews, fn ($r) => !!isset($r['property_code']));

    //     if (count($reviews) < 1) {
    //         return 'no review';
    //     }
    //     $codes = array_map(fn ($review) => $review['property_code'], $reviews);
    //     if (Properties::whereIn('property_code', array_values($codes))->count() < 1) {
    //         return 'properties not found.';
    //     }
    //     foreach ($users as $key => $user) {
    //         $users[$key] = User::create($user);
    //     }
    //     $new_reviews = [];
    //     foreach ($reviews as $review) {
    //         $pro = Properties::where('property_code', $review['property_code'])->first();
    //         if (!!$pro) {
    //             unset($review['property_code']);
    //             $user = $this->getUser($new_reviews, $users, rand(0, 30));
    //             $new_reviews[] = [
    //                 ...$review,
    //                 'receiver_id' => $pro->host_id,
    //                 'property_id' => $pro->id,
    //                 'sender_id' => $user->id,
    //                 'rating' => ($review['cleanliness'] + $review['location']) / 2
    //             ];
    //         }
    //     }
    //     // 6662
    //     Log::debug('check', ['reviews' => $new_reviews]);
    //     if (count($new_reviews) < 1) {
    //         $ids = array_map(fn ($user) => $user->id, $users);
    //         User::whereIn('id', $ids)->delete();
    //     }
    //     Reviews::insert($new_reviews);
    //     return 'done';
    // }
    // function getUser($reviews, $users, $key)
    // {
    //     $user = $users[$key];
    //     $filter = array_filter($reviews, fn ($review) => !!isset($review['sender_id']) && $review['sender_id'] == $user->id);
    //     if (count($filter) > 3) {
    //         $this->getUser($reviews, $users, rand(0, 30));
    //     }
    //     return $user;
    // }

    public function getCountries()
    {
        return response()->json([
            'countries' => Country::all(),
            'success' => 'true'
        ]);
    }

    public function clearCache()
    {
        \Illuminate\Support\Facades\Artisan::call('cache:clear');
    }

    public function getDistricts($id)
    {
        return response()->json([
            'districts' => District::where('city_id', $id)->get(),
            'success' => 'true'
        ]);
    }

    public function fetchAmenitiesBaseonIds(Request $request)
    {
        $uniqueData = array_unique($request->selectedAmenities);
        return response()->json([
            'amenities' => Amenities::whereIn('id', $uniqueData)->distinct()->get(),
            'success' => 'true'
        ]);
    }

    public function getReservations(Request $request)
    {
        $user = $request->type == 'customer' ? 'user_id' : 'host_id';
        return response()->json([
            'cancelled_reservations' => Bookings::with('users', 'properties')->where($user, auth()->id())
                ->where(fn($q) => $q->where('status', '=', 'Cancelled')->orWhere('status', '=', 'Declined'))->count(),
            'current_reservations' => Bookings::with('users', 'properties')
                ->where($user, auth()->id())
                ->where('start_date', '<=', Carbon::now()->format('Y-m-d'))
                ->where('end_date', '>=', Carbon::now()->format('Y-m-d'))
                ->where('status', 'Accepted')
                ->count(),
            'past_reservations' => Bookings::with('users', 'properties')->where($user, auth()->id())
                ->where(fn($q) => $q->where('status', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')))->count(),
            'total_price' => Bookings::with('users', 'properties')
                ->where($user, auth()->id())
                ->where(fn($q) => $q->where('status', '=', 'Accepted')->where('end_date', '<', Carbon::now()->format('Y-m-d')))
                ->sum('total'),
            'success' => 'true'
        ]);
    }

    public function previousDataAlter()
    {
        return 'done';
        $bookingPaymentDetail = BookingPaymentDetails::all();
        foreach ($bookingPaymentDetail as $key => $bookingPaymentDetails) {
            $booking = Bookings::where('id', $bookingPaymentDetails->booking_id)->first();
            if ($booking) {
                $securityMoney = $booking->security_money;
                $basePrice = $booking->base_price;
                $cleaningFee = $booking->cleaning_charge;

                $totalBookingPrice = $basePrice + $cleaningFee + $securityMoney;

                $darentDiscount = 0;
                if ($booking->promoCodeUsage !== null) {
                    $darentDiscountPercentage = $booking->promoCodeUsage->promoCode;
                    $discount = $darentDiscountPercentage->percenatge;
                    $darentDiscountUpto = $darentDiscountPercentage->discount_upto;

                    $darentDiscount = $totalBookingPrice * ($discount / 100);
                    if ($darentDiscount > $darentDiscountUpto) {
                        $darentDiscount = $darentDiscountUpto;
                    }
                }

                $netSales = $totalBookingPrice - $darentDiscount;
                $percent = $booking->host->commission; // Dynamic for every host default(15%)

                if ($booking->referal_code != null) { //IF REFERAL LINK WAS USED WHILE BOOKING
                    $property_fees = PropertyFees::where('field', PropertyFees::DARENT_COMMISSION)->first();
                    $percent = (int)$property_fees->value;
                }

                $darentCommissionAmount = ($netSales + $darentDiscount) * ($percent / 100);
                BookingPaymentDetails::where('id', $bookingPaymentDetails->id)->update([
                    'commission' => $darentCommissionAmount,
                    'commission_in_percenatge' => $percent,
                    'host_pay' => round(($netSales + $darentDiscount) - $darentCommissionAmount, 2)
                ]);
            }
        }

        return 'done';
    }

    public function previousUserUuidGenerator()
    {
        // $users = User::all();
        // foreach ($users as $user) {
        //     $uuid = Str::uuid();
        //     $uuidString = $uuid->toString();
        //     User::where('id', $user->id)->update([
        //         'uuid' => $uuidString
        //     ]);
        // }
        return 'done';
    }

    public function previousDarentDiscount()
    {
        return 'done';
        $bookingPaymentDetails = BookingPaymentDetails::all();
        foreach ($bookingPaymentDetails as $bookingPaymentDetail) {
            try {
                $total_booking_price = 0;
                $booking = Bookings::find($bookingPaymentDetail->booking_id);
                // $prices = json_decode($booking->date_with_price);

                // if (json_last_error() !== JSON_ERROR_NONE) {
                //     throw new Exception("Error decoding JSON");
                // }

                // foreach ($prices as $priceObject) {
                //     $total_booking_price += $priceObject->price;
                // }
                // $total_booking_price;

                $total_booking_price = $booking->base_price;

                $total_discount = 0;
                if ($booking->promoCodeUsage !== null) {
                    $promoCodeObj = $booking->promoCodeUsage->promoCode;
                    $promoCode = $promoCodeObj->percentage;
                    $discountUpto = $promoCodeObj->discount_upto;

                    $total_discount = $total_booking_price * ($promoCode / 100);
                    if ($total_discount > $discountUpto) {
                        $total_discount = $discountUpto;
                    }
                }

                BookingPaymentDetails::where('booking_id', $booking->id)->update([
                    'darent_discount' => $total_discount

                ]);
            } catch (Exception $e) {
                Log::error('JSON decoding error: ' . $e->getMessage());
            }
        }
    }

    public function deleteIqamaByUrl(Request $request)
    {
        if ($request->iqama_number && $request->iqama_number != "null") {
            $checkIqama = ElmDocument::where("code", $request->iqama_number)->delete();
            if ($checkIqama) {
                return "deleted Successfully";
            } else {
                return "Iqama Not Found";
            }
        } else {
            abort('404');
        }
    }


}
