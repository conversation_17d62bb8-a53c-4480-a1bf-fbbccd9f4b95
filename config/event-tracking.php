<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Event Tracking Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the unified event tracking system.
    | You can enable/disable specific tracking platforms and configure them here.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Enable Event Tracking
    |--------------------------------------------------------------------------
    |
    | This option controls whether event tracking is enabled globally.
    | When set to false, no events will be sent to any platform.
    |
    */
    'enable' => env('EVENT_TRACKING_ENABLE', true),

    /*
    |--------------------------------------------------------------------------
    | Queue Configuration
    |--------------------------------------------------------------------------
    |
    | Event tracking jobs use the default Laravel queue configuration.
    | Configure your queues in config/queue.php.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Debug Mode
    |--------------------------------------------------------------------------
    |
    | When debug mode is enabled, additional logging will be performed.
    | This is useful for development and debugging.
    |
    */
    'debug' => env('EVENT_TRACKING_DEBUG', env('APP_DEBUG', false)),

    /*
    |--------------------------------------------------------------------------
    | Tracking Platforms
    |--------------------------------------------------------------------------
    |
    | Configure the tracking platforms to use. Each platform can be enabled
    | or disabled individually.
    |
    */
    'platforms' => [
        /*
        |--------------------------------------------------------------------------
        | MoEngage
        |--------------------------------------------------------------------------
        */
        'moengage' => [
            'enable' => env('MOENGAGE_ENABLE', true),
            'app_id' => env('MOENGAGE_APP_ID'),
            'campaign_events_key' => env('MOENGAGE_CAMPAIGN_EVENTS_KEY'),
            'base_url' => env('MOENGAGE_API_BASE_URL', 'https://api-02.moengage.com'),
        ],

        /*
        |--------------------------------------------------------------------------
        | AppsFlyer
        |--------------------------------------------------------------------------
        */
        'appsflyer' => [
            'enable' => env('APPSFLYER_ENABLE', false),
            'dev_key' => env('APPSFLYER_DEV_KEY'),
            'app_id' => env('APPSFLYER_APP_ID'),
            'base_url' => env('APPSFLYER_API_BASE_URL', 'https://api2.appsflyer.com'),
        ],
    ],

];
