<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => 'https://newweb.darent.dev/googleAuthenticate',
        'stateless' => true, // Add this line

    ],

    'ga4' => [
        'key_file' => storage_path('serviceKey.json'),
        'property_id' => '329139193',
    ],

    'google_analytics' => [
        'enable' => env('GOOGLE_ANALYTICS_ENABLE', false),
        'id' => env('GOOGLE_ANALYTICS_ID'),
    ],
    'snapchat' => [
        'enable' => env('SNAPCHAT_ENABLE', false),
        'id' => env('SNAPCHAT_ID'),
    ],

    'tiktok' => [
        'enable' => env('TIKTOK_ENABLE', false),
        'id' => env('TIKTOK_ID'),
    ],

    'meta_pixel' => [
        'enable' => env('META_PIXEL_ENABLE', false),
        'id' => env('META_PIXEL_ID'),
    ],

    'unifonic' => [
        'env' => env('UNIFONIC_ENV', 'production'),
        'base_url' => env('UNIFONIC_BASE'),
        'app_sid' => env('UNIFONIC_APP'),
        'sender_id' => env('UNIFONIC_SENDER')
    ],

    // Configure Google ReCAPTCHA v3 key+secret
    'recaptcha_v3' => [
        'enable' => env('RECAPTCHA_ENABLE', true),
        'siteKey' => env('RECAPTCHA_V3_SITE_KEY'),
        'secretKey' => env('RECAPTCHA_V3_SECRET_KEY'),
        'score' => env('RECAPTCHA_V3_SC0RE'),
        'permission' => env('RECAPTCHA_V3_ALLOW'),
    ],


    'hyperpay' => [
        'url' => env('HYPERPAY_URL'),
        'entity_id' => env('HYPERPAY_ENTITY_ID'),
        'entity_mada_id' => env('HYPERPAY_ENTITY_MADA_ID'),
        'authorization' => env('HYPERPAY_AUTHORIZATION'),
        'key' => env('HYPERPAY_KEY'),
        'notification_key' => env('HYPERPAY_NOTIFICATION_KEY'),
        'config_id' => env('HYPERPAY_CONFIG_ID'),
        'entity_apple_id' => env('HYPERPAY_ENTITY_APPLE_ID'),
    ],

    'hyperbill' => [
        'key' => env('HYPERBILL_KEY'),
    ],

    'hostaway' => [
        'base_url' => env('HOSTAWAY_API_BASE_URL'),
        'client_id' => env('HOSTAWAY_API_CLIENT_ID'),
        'client_secret' => env('HOSTAWAY_API_CLIENT_SECRET'),
    ],

    'inventory' => [
        'base_url' => env('INVENTORY_API_BASE_URL', 'http://inventory.test/api/v1'),
        'sync' => [
            'locations' => '/sync/locations',
            'properties' => '/sync/properties',
            'types' => '/sync/types',
            'amenities' => '/sync/amenities',
        ],
    ],
    'kease' => [
        'base_url' => env('KEASE_API_BASE_URL', 'https://kease.io'),
        'token' => env('KEASE_API_TOKEN', 'your_access_token_here'),
        'client_id' => env('KEASE_API_CLIENT_ID', '4'),
        'client_secret' => env('KEASE_API_CLIENT_SECRET', 'wYzOpR59O8oHYQ0vtaBOwcGjj3SgKvV5PjsAPNMa'),
        'username' => env('KEASE_API_USERNAME', '<EMAIL>'),
        'password' => env('KEASE_API_PASSWORD', 'D4SF$bjs45%sh'),
        'webhook_secret' => env('KEASE_WEBHOOK_SECRET', 'KEASE_WEBHOOK_SECRET'),
    ],
];
