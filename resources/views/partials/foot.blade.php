<script rel="preload" src="{{ asset('js/jQuery/jquery-2.2.4.min.js') }}" as="script"></script>
<script rel="preload" src="{{ asset('js/theme-lib.js') }}" as="script"></script>
<script rel="preload" src="{{ asset('js/theme-fun.js') }}?v={{ $currentAppVersion}}" as="script"></script>
<script rel="preload" src="{{ asset('js/local_country.js') }}?v={{ $currentAppVersion}}" as="script"></script>

@stack('jslibs')
<script rel="preload" src='https://maps.google.com/maps/api/js?key={{ @$map_key }}&libraries=places' as="script">
</script>
<script rel="preload" src="{{ asset('js/locationpicker.jquery.min.js') }}" as="script"></script>

{{-- ============ All CDN Local Start ============ --}}


<script rel="preload" src="{{ asset('cdns/js/jquery.lazyload.min.js') }}" as="script"></script>
<script rel="preload" src="{{ asset('cdns/js/aos.js') }}" as="script"></script>
<script rel="preload" src="{{ asset('cdns/js/slick.min.js') }}" as="script"></script>
<script rel="preload" src="{{ asset('cdns/js/swiper.js') }}" as="script"></script>
<script rel="preload" src="{{ asset('cdns/js/swiper-bundle.min.js') }}" as="script"></script>
{{-- <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script> --}}
<script rel="preload" src="{{ asset('cdns/js/bootstrap-select.min.js') }}" as="script"></script>
{{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/js/bootstrap-select.min.js"></script> --}}


{{-- ============ All CDN Local End ============ --}}


<script rel="preload" src="{{ asset('bootstrap/js/bootstrap.bundle.min.js') }}" as="script"></script>
<script rel="preload" src="{{ asset('js/moment2.min.js') }}" as="script"></script>
<script rel="preload" src="{{ asset('js/daterangepicker2.min.js') }}" as="script"></script>
<script rel="preload" src="{{ asset('js/front.min.js') }}" as="script"></script>

<script rel="preload" src="{{ asset('cdns/js/swiper.js') }}" as="script"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Swiper/10.3.1/swiper-bundle.min.js"
    integrity="sha512-2w85qGM9apXW9EgevsY4S4fnJIUz6U6mXlLbgDKphBuwh7jPQNad70Ll5W+pcIrJ6rIMGpjP0CxYGQwKsynIaQ=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
{{-- <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script> --}}
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/js/bootstrap-select.min.js"></script>

@vite('resources/js/app.js')
@stack('scripts')

<script type="text/javascript">
    // ----------------------------------------------------------------------------Constants definition Starts-------------------------------------------------------------------------------
    const CUSTOMER_ATTRIBUTES = 'User Attribute for Customer';
    const HOST_ATTRIBUTES = 'User Attribute for Hosts';
    const DEFAULT_USER = 'Customer';
    const CURRENCY_SELECTOR = 'Currency Selected';
    const OTP_VERIFICATION_STARTED = 'OTP Verification Started';
    const OTP_VERIFICATION_COMPLETED = 'OTP Verification Completed';
    const NUMBER_OF_GUEST_ENTERED = 'Number of Guest Entered';
    const ARRIVAL_DEPARTURE_DATE = 'Arrival Date & Departure Date';
    const DESTINATION = 'Destination';
    const NUMBER_OF_NIGHTS = 'Number of Nights';
    const LANGUAGE_SELECTOR = 'Language Selected';
    const CATEGORY_SELECTED = 'Category Selected';
    const PLACE_SELECTED = 'Place Selected';
    const MARK_AS_WISHLIST = 'Mark as Wishlist';
    const PERSONAL_INFO_UPDATED = 'Personal Information Updated';
    const MOBILE_NUMBER_ENTERED = 'Mobile Number Entered';
    const PROPERTY_TYPE = 'Property Type';
    const PROPERTY_CATEGORY = 'Property Category';
    const HOST_REGISTERATION_STARTED = 'Host Registration Started';
    const NO_OF_APPARTMENTS = 'Number of Apartments';
    const LOCATION = 'Location';
    const SINGLE_BEDS = 'Single Beds';
    const DOUBLE_BEDS = 'Double Beds';
    const BATHROOMS = 'Bathrooms';
    const PREMIUM_AMENITIES_SELECTED = 'Premium Amenities Selected';
    const SAFETY_ITEMS = 'Safety Items';
    const HOUSE_RULES = 'House Rules';
    const UPLOAD_PHOTOS = 'Upload Photos';
    const ADDRESS_ENTERED = 'Address Entered';
    const HOST_REGISTERATION_COMPLETED = 'Host Registration Completed';
    const FAMILY = 'Family';
    const CURRENT_RESERVATIONS_VIEWED = 'Current Reservations Viewed';
    const ALL_RESERVATIONS_VIEWED = 'All Reservations Viewed';
    const CANCELLED_RESERVATIONS_VIEWED = 'Canceled Reservations Viewed';
    const SIGNUP = 'Singup';
    const GUEST_CHECKING_OUT_VIEWED = 'Guests Checking out Viewed';
    const GUEST_ARRIVING_SOON_VIEWED = 'Guest Arriving Soon Viewed';
    const MESSAGE_THE_HOST = 'Message the Host';
    const PLACE_RESERVATION_STARTED = 'Place Reservation Started';
    const PLACE_RESERVATION_COMPLETED = 'Place Reservation Completed';
    const HOST_PAYMENT_RECIEVED = 'Host Payment Received';
    const HOST_BOOKING_REQUEST = 'Host Booking Request';
    const HOST_EDIT_PROPERTY = 'Host Edit Property';
    const HOST_PROPERTY_DATA_INCOMPLETE = 'Host Property Data Incomplete';
    const REQUEST_BOOKING = 'Request Booking';
    const CANCEL_BOOKING = 'Cancel Booking';
    const YAQEEN_VERIFICATION = 'Yaqeen Verification';
    // ----------------------------------------------------------------------------Constants definition Ends-------------------------------------------------------------------------------
    $('.selectpicker').selectpicker({
        liveSearchIcon: 'fas fa-search',
    });
    $(window).on('load', function() {
        $('.loadskull').removeClass('loadskull');
        $('.numberInput').on('keydown', function(event) {
            if (event.key === 'Enter') {
                console.log('trigger');
                $('.sendotpbtn').click(); // Use click() instead of trigger()
            }
        });

    });
    $(document).ready(function() {
        $('#saveExitButton').on('click', function() {
            // Update input "normal "to "Save & Exit"
            $('#purpose').val('saveAndExit');
            $('#currentStepForm').submit();
        });
        $('.guestUser').on('click', function() {
            $('#staticBackdrop').modal('show');
        });
    });

    // $(document).ready(function( $ ){
    //         $('.on-report').click(function() {
    //             $('.report-panel').toggle();
    //         });
    //     });



    $(document).ready(function() {
        $('.numberInput').on('wheel', function(event) {
            event.preventDefault();
            console.log('test');
        });
    });

    function consolelog(content) {
        var debug = "{{ isset($_GET['debug-console']) }}";
        if (debug) {
            console.log(content);
        }
    }
    document.addEventListener("DOMContentLoaded", function() {
        var lazyloadImages = document.querySelectorAll("img.lazy");
        var lazyloadThrottleTimeout;

        function lazyload() {
            if (lazyloadThrottleTimeout) {
                clearTimeout(lazyloadThrottleTimeout);
            }

            lazyloadThrottleTimeout = setTimeout(function() {
                var scrollTop = window.pageYOffset;
                lazyloadImages.forEach(function(img) {
                    if (img.offsetTop < (window.innerHeight + scrollTop)) {
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                    }
                });
                if (lazyloadImages.length == 8) {
                    document.removeEventListener("scroll", lazyload);
                    window.removeEventListener("resize", lazyload);
                    window.removeEventListener("orientationChange", lazyload);

                }
            }, 20);
        }
        lazyload();

    });

    $(document).ready(function() {
        $('#signup').on('hidden.bs.modal', function() {
            $('#signup form')[0].reset();
        });
    });
    $(document).ready(function() {
        $('#enterotp').on('hidden.bs.modal', function() {
            $('#enterotp form')[0].reset();
        });
    });
    $(document).ready(function() {
        $('#staticBackdrop').on('hidden.bs.modal', function() {
            $('#staticBackdrop form')[0].reset();
        });
    });
    $(document).ready(function() {
        $('#enteremailotp').on('hidden.bs.modal', function() {
            $('#enteremailotp form')[0].reset();
        });
    });
</script>
<script type="text/javascript">
    // mobileOnlySlider(".side-inner", true, false, 900);

    // function mobileOnlySlider($slidername, $dots, $arrows) {
    //     if (slider.find('li').length > 3) {
    //     var slider = $($slidername);
    //     var settings = {
    //         mobileFirst: true,
    //         dots: false,
    //         infinite: false,
    //         arrows: $arrows,
    //         slidesToShow: 3,
    //         slidesToScroll: 1,

    //     };
    //     slider.slick(settings);
    // }

    //     slider.slick(settings);

    //     $(window).on("resize", function() {
    //         if ($(window).width() > 600) {
    //             return;
    //         }
    //         if (!slider.hasClass("slick-initialized")) {
    //             return slider.slick(settings);
    //         }
    //     });
    // } // Mobile Only Slider
</script>


<script rel="preload" type="text/javascript" as="script">
    $(function() {
        $('input[name="headerdaterange"]').daterangepicker({
            opens: 'center',
            minDate: moment().format('MM-DD-YYYY'),
            locale: {
                monthNames: calendarContent.monthNames,
                daysOfWeek: calendarContent.daysOfWeek,
                applyLabel: calendarLabels.applyLabel,
                cancelLabel: calendarLabels.cancelLabel,
            },
        }, function(start, end, label) {
            console.log("A new date selection was made: " + start.format('DD-MM-YYYY') + ' to ' + end
                .format('DD-MM-YYYY'));
        });
        $('input[name="headerdaterange"]').on('show.daterangepicker', function(ev, picker) {
            // Add your custom class to the parent div
            $('.daterangepicker').addClass('cust-double-rangepicker');
        });
        $('.iqama-close-btn').on('click', function(e) {
            window.location.reload(true);
        });
        // $('.applyBtn').on('click', function(ev) {
        //     let picker = $('input[name="headerdaterange"]').data('daterangepicker');
        //     if (picker.startDate && !picker.endDate) {
        //         let nextDay = picker.startDate.clone().add(1, 'day');
        //         picker.endDate = nextDay;
        //         $('input[name="headerdaterange"]').data('daterangepicker').endDate = picker.endDate;
        //         $('input[name="headerdaterange"]').val(picker.startDate.format('MM-DD-YYYY') + ' - ' +
        //             picker.endDate.format('MM-DD-YYYY'));
        //     }

        // });

        $('input[name="date_of_birth"]').daterangepicker({
            drops: 'down',
            showDropdowns: true,
            singleDatePicker: true,
            autoApply: true,
            timePicker: false,
            minimumNights: 0,
            minYear: 1901,
            locale: {
                format: 'DD-MM-YYYY',
                monthNames: calendarContent.monthNames,
                daysOfWeek: calendarContent.daysOfWeek,
                applyLabel: calendarLabels.applyLabel,
                cancelLabel: calendarLabels.cancelLabel,
            },
            changeMonth: true,
            changeYear: true,
            maxYear: parseInt(moment().format('YYYY'), 10),
        }).focus();

    });

    //   for account date of birth
    //   for account date of birth

    $(document).on('click', '.dt-rng', function() {
        $(this).daterangepicker({

            drops: 'down',
            showDropdowns: true,
            singleDatePicker: true,
            autoApply: true,
            timePicker: false,
            minimumNights: 0,
            minYear: 1901,
            maxDate: moment(),
            locale: {
                format: 'DD-MM-YYYY',
                monthNames: calendarContent.monthNames,
                daysOfWeek: calendarContent.daysOfWeek,
                applyLabel: calendarLabels.applyLabel,
                cancelLabel: calendarLabels.cancelLabel,
            },
            changeMonth: true,
            changeYear: true,
            maxYear: parseInt(moment().format('YYYY'), 10),
        }).focus();
    });





    var startDate;
    var endDate;

    $(document).ready(function() {
        let authcheck = "{{ auth()->check() }}";
        let login = "{{ isset($_GET['login']) }}";
        let inactive = "{{ isset($_GET['inactive']) }}";
        if (login && !authcheck) {
            $('#staticBackdrop').modal('show');
            $('#staticBackdrop').modal('show');
        }
        if (inactive && !authcheck) {
            $('#inactiveuser').modal('show');
            $('#inactiveuser').modal('show');
        }
    });
    $('#saveBtn').click(function() {
        console.log(startDate.format('D MMMM YYYY') + ' - ' + endDate.format('D MMMM YYYY'));
    });

    var authcheck = "{{ auth()->check() }}";

    if (authcheck) {
        document.getElementById('updateUserMode')?.addEventListener('click', function() {
            // Get the new value you want to set in the session (e.g., from an input field)


            var sessionValue = $(this).data('usermode'); // Replace this with the actual new

            // Send an AJAX request to update the session value
            $.ajax({
                url: "{{ route('switchToHost') }}",
                type: 'POST',
                data: {
                    '_token': '{{ csrf_token() }}',
                    'sessionValue': sessionValue
                },
                success: function(response) {
                    if (sessionValue == 'host') {
                        fetch("{{ route('getReservations', ['type' => 'host']) }}")
                            .then(reservationResponse => {
                                if (reservationResponse.success) {
                                    webengage.user.setAttribute('we_first_name', userObj
                                        .first_name);
                                    webengage.user.setAttribute('we_last_name', userObj
                                        .last_name);
                                    webengage.user.setAttribute('we_gender', userObj.gender);
                                    webengage.user.setAttribute('we_phone', userObj.phone);
                                    webengage.user.setAttribute('we_email', userObj.email ==
                                        null ? '' : userObj.email);
                                    webengage.user.setAttribute('Current Reservations',
                                        reservationResponse
                                        .current_reservations);
                                    webengage.user.setAttribute('Past Reservations',
                                        reservationResponse
                                        .past_reservations);
                                    webengage.user.setAttribute('Cancelled Reservations',
                                        reservationResponse
                                        .cancelled_reservations);
                                    webengage.user.setAttribute('User', 'Host');
                                    webengage.user.setAttribute('Total Price', parseFloat(
                                        reservationResponse
                                        .total_price.toFixed(2)));
                                } else {
                                    console.error('Failed to get reservations');
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                            });
                    }
                    window.location.href = "{{ route('home') }}";
                },
                error: function(xhr, status, error) {
                    // Error occurred during the AJAX request
                    console.error(error);
                }
            });
        });
    }
</script>



<script type="text/javascript">
    var APP_URL = "{{ url('/') }}";
    var USER_ID = "{{ isset(Auth::user()->id) ? Auth::user()->id : '' }}";
    var sessionDate = '{!! Session::get('date_format_type') !!}';

    $(".currency_footer").on('click', function() {
        var currency = $(this).data('curr');
        $.ajax({
            type: "POST",
            url: APP_URL + "/set_session",
            data: {
                "_token": "{{ csrf_token() }}",
                'currency': currency
            },
            success: function(msg) {
                location.reload()
            },
        });
    });

    $(".language_footer").on('click', function() {
        var language = $(this).data('lang');
        $.ajax({
                type: "POST",
                url: APP_URL + "/set_session",
                data: {
                    "_token": "{{ csrf_token() }}",
                    'language': language
                },
                success: function(msg) { //WebEngage
                    //-----------WebEngage Integration------------(Verified)
                    let language = "{{ session('language_name') }}";
                    if (language == 'English') {
                        language = 'Arab'
                    } else {
                        language = 'English'
                    }
                    let user = DEFAULT_USER
                    let authcheck = '{{ auth()->check() }}';
                    if (authcheck) {
                        @auth
                        var isHost = @json(auth()->user()->is_host);
                    @endauth
                    user = isHost == true ? 'Host' : DEFAULT_USER
                }
                let payload = {
                    "User": user,
                    "Language": language
                }
                webEngageTracking(LANGUAGE_SELECTOR, payload)
                //-----------WebEngage Integration------------
                const lang = @json(app()->getLocale());
                window.location.replace((window.location.href).replace("/" + lang, "/" + (lang == 'en' ?
                    'ar' :
                    'en')))
            },
        });
    });

    let mode = ""
    let oldForm = ""
    async function onLogin(e) {
        e.preventDefault();
        const smbBtnEl = e.submitter

        smbBtnEl.disabled = true
        $(".loadergif").removeClass('d-none');
        $(".signinbtn").addClass('d-none');


        let url = "{{ route('user.login', ['noToken' => true]) }}"
        const formData = new FormData(e.target)
        if (formData.has("phone")) {
            if (!!formData.get("phone")) {
                formData.set("phone", formData.get("phone"))
            }
            url = "{{ route('user.create.token') }}"
            mode = "phone"
        } else if (formData.has("otp")) {
            url = "{{ route('user.verify.token', ['noToken' => true]) }}"
            formData.set("code", formData.getAll("otp").join(""))
            formData.delete("otp")
        }
        try {
            const response = await fetch(url, {
                headers: {
                    'accept': 'application/json'
                },
                method: "POST",
                body: formData
            });
            const data = await response.json();
            removeErrors()
            if (!response.ok) throw data;
            if (data.success) {
                var timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                $.ajax({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')

                    },
                    type: 'POST',
                    url: '/save-timezone',
                    data: {
                        timezone: timezone
                    },
                    success: function(data) {
                        console.log(data);
                    }

                });

                const temp = document.getElementById("fields")
                oldForm = temp.innerHTML
                if (mode == "phone") {
                    temp.innerHTML = `<div class="verification-code" id="code">
                        <div class="verification-code--inputs">
                            <input type="number"  id="otp-1" name="otpfield"
                            placeholder="-" maxlength="1" />
                        <input type="number"  id="otp-2" name="otpfield"
                            placeholder="-" maxlength="1" />
                        <input type="number"  id="otp-3" name="otpfield"
                            placeholder="-" maxlength="1" />
                        <input type="number"  id="otp-4" name="otpfield"
                            placeholder="-" maxlength="1" />
                        </div>
                        <input type="hidden" name="phone" id="otp_phone" />
                        <input type="hidden" name="otp" id="otp" />
                        </div>`
                    mode = "otp"
                } else if (mode != "phone") {

                    //if (data.data) {
                    //    window.location = "{{ route('yourlisting') }}"

                    // } else {
                    //window.location = "{{ route('home') }}"

                    location.reload();
                    //window.history.back()
                    // }
                }
            }

        } catch (errorRes) {
            if (errorRes.hasOwnProperty("error")) {
                for (const [key, errors] of Object.entries(errorRes.error)) {
                    setErrors(key, errors);
                }
            }
            $(".loadergif").addClass('d-none');
            $(".signinbtn").removeClass('d-none');
            $(".loadergif").addClass('d-none');
            $(".signinbtn").removeClass('d-none');
        } finally {
            smbBtnEl.disabled = false
            $(".loadergif").addClass('d-none');
            $(".signinbtn").removeClass('d-none');
        }
    }

    async function onRegister(e) {
        e.preventDefault();
        const formData = new FormData(e.target)
        const smbBtnEl = e.submitter
        smbBtnEl.disabled = true
        formData.set("code", document.getElementById("phone-prepend-reg").value)
        try {
            const response = await fetch("{{ route('user.register', ['noToken' => true]) }}", {
                headers: {
                    'accept': 'application/json'
                },
                method: "POST",
                body: formData
            });
            const data = await response.json();
            removeErrors()
            if (!response.ok) throw data;
            if (data.success) {
                location.reload()
            }

        } catch (errorRes) {
            if (errorRes.hasOwnProperty("error")) {
                for (const [key, errors] of Object.entries(errorRes.error)) {
                    setErrors(`reg-${key}`, errors);
                }
            }
        } finally {
            smbBtnEl.disabled = false
        }
    }
    async function sendOtp(e) {
        e.preventDefault();
        const smbBtnEl = e.submitter;

        let fingerPrintRequestId = null;
        const fingerPrintVisitorId = '94PQLBeh';

        smbBtnEl.disabled = true;
        $(".loadergif").removeClass('d-none');
        $(".sendotpbtn").addClass('d-none');

        // Country Change Impact on phone field
        const selectElement = document.getElementById("phone-prepend");
        countryCode = selectElement.value;

        let url = "";
        const formData = new FormData(e.target);
        if (formData.has("phone") && !formData.has("otp")) {
            if (!!formData.get("phone")) {
                formData.set("phone", "+" + countryCode + formData.get("phone"));
            }
            url = "{{ route('createOtp') }}";
            mode = "phone";
        }

        try {
            @if(config('services.recaptcha_v3.enable'))
            var reCaptchaTokenPromise = new Promise(function(resolve, reject) {
                grecaptcha.ready(function() {
                    grecaptcha.execute("{{ config('services.recaptcha_v3.siteKey') }}", {
                        action: 'sendOtpCaptcha'
                    }).then(function(token) {
                        resolve(token); // Resolve the promise with the token
                    }).catch(function(error) {
                        reject(error); // Reject the promise if there's an error
                    });
                });
            });

            reCaptchaTokenPromise.then(async function(token) {
                // Check if g-recaptcha-response already exists in formData
                if (formData.has('g-recaptcha-response')) {
                    // Update its value
                    formData.set('g-recaptcha-response', token);
                } else {
                    // If it doesn't exist, append it
                    formData.append('g-recaptcha-response', token);
                }
            @else
            // reCAPTCHA is disabled, continue without token
            var reCaptchaTokenPromise = Promise.resolve(null);

            reCaptchaTokenPromise.then(async function(token) {
            @endif
                try {
                    const response = await fetch(url, {
                        headers: {
                            'accept': 'application/json',
                            'X-Fingerprint-Request-Id': fingerPrintRequestId || '',
                            'X-Fingerprint-Visitor-Id': fingerPrintVisitorId || '',
                        },
                        method: "POST",
                        body: formData
                    });
                    const data = await response.json();
                    removeErrors();
                    if (data.status == 422) {
                        document.getElementById('phoneError').textContent = data.message['phone'][0];
                    }
                    if (data.status == 200) {
                        $('#staticBackdrop').modal('hide');
                        $('#enterotp').modal('show');
                        let phone = data.data;
                        document.getElementById("otp_phone").value = phone;
                        document.getElementById("display_phone").textContent = phone;
                        document.getElementById('phone-prepend-reg-value').value = "+" + countryCode;
                        document.getElementById('phone-prepend-reg-value').text = "+" + countryCode;

                        //-----------WebEngage Integration------------
                        let user = DEFAULT_USER;
                        if (data.user != null) {
                            user = data.user.is_host == true ? 'Host' : DEFAULT_USER;
                        }

                        let payload = {
                            "User": user
                        };
                        webEngageTracking(OTP_VERIFICATION_STARTED, payload);
                        webEngageTracking(MOBILE_NUMBER_ENTERED, payload);
                        //-----------WebEngage Integration------------
                    }
                } catch (errorRes) {
                    $(".loadergif").addClass('d-none');
                    $(".sendotpbtn").removeClass('d-none');
                } finally {
                    smbBtnEl.disabled = false;
                    $(".loadergif").addClass('d-none');
                    $(".sendotpbtn").removeClass('d-none');
                }
            }).catch(function(error) {
                // Handle errors if any
                console.error("Error getting token:", error);
            });
        } catch (error) {
            console.error("Error:", error);
        }
    }

    async function verifyOtp(e) {
        e.preventDefault();
        const smbBtnEl = e.submitter
        const queryParams = new URLSearchParams(window.location.search);
        const cohost = queryParams.get('cohost');

        smbBtnEl.disabled = true
        $(".loadergif").removeClass('d-none');
        $(".otpverify").addClass('d-none');
        // console.log(e.target);


        // let url = "{{ route('createOtp', ['noToken' => true]) }}"
        let url = "";
        const formData = new FormData(e.target);
        var phoneNumber = formData.get("phone");

        if (formData.has("otpfield")) {

            url = "{{ route('verifyPhoneOtp') }}"
            formData.set("otp", formData.getAll("otpfield").join(""))
            formData.set("phone", formData.get("phone"))
            formData.set("cohost", cohost)
            // formData.delete("otpfield")
        }
        try {
            const response = await fetch(url, {
                headers: {
                    'accept': 'application/json'
                },
                method: "POST",
                body: formData
            });
            const data = await response.json();
            removeErrors()
            if (data.status == 422) {
                document.getElementById('otpError').textContent = data.message['otp'][0];
                //-----------WebEngage Integration------------(All VERIFIED)
                let payload = {
                    "User": DEFAULT_USER,
                }
                webEngageTracking(OTP_VERIFICATION_COMPLETED, payload)
                //-----------WebEngage Integration------------
            }
            if (data.status == 200) {
                //-----------WebEngage Integration------------
                if (data.data.user == null) {
                    $('#enterotp').modal('hide');
                    $('#signup').modal('show');
                    var cleanedPhoneNumber = phoneNumber.substring(4);
                    // $('#reg-phone').val(phoneNumber);
                    $('#reg-phone').val(cleanedPhoneNumber);
                    $('#phone_token').val(data.data.token);
                    $('#cohost_token').val(data.data.cohost);

                    //-----------WebEngage Integration------------
                    let payload = {
                        "User": DEFAULT_USER,
                        // comment: DEFAULT_USER
                    }
                    webEngageTracking(OTP_VERIFICATION_COMPLETED, payload)
                    //-----------WebEngage Integration------------

                } else {
                    //-----------WebEngage Integration------------
                    let userEngage = DEFAULT_USER;
                    // let comment = DEFAULT_USER;
                    if (data.data.user !== null) {
                        // user_id = data.data.user.id
                        userEngage = data.data.user.is_host == true ? 'Host' : DEFAULT_USER
                    }
                    let payload = {
                        "User": userEngage,
                    }
                    webEngageTracking(OTP_VERIFICATION_COMPLETED, payload)

                    // -------------USER ATTRIBUTES---------------
                    let userObj = data.data.user
                    webengage.user.login(userObj.uuid); // WebEngage User Logged In
                    fetch("{{ route('getReservations', ['type' => 'customer']) }}")
                        .then(response => {
                            if (!response.ok) {
                                throw new Error("Network response was not ok");
                            }
                            return response.json();
                        })
                        .then(reservationResponse => {
                            if (reservationResponse.success) {
                                webengage.user.setAttribute('we_first_name', userObj.first_name);
                                webengage.user.setAttribute('we_last_name', userObj.last_name);
                                webengage.user.setAttribute('we_gender', userObj.gender);
                                webengage.user.setAttribute('we_phone', userObj.phone);
                                webengage.user.setAttribute('we_email', userObj.email == null ? '' :
                                    userObj
                                    .email);
                                webengage.user.setAttribute('Current Reservations', reservationResponse
                                    .current_reservations);
                                webengage.user.setAttribute('Past Reservations', reservationResponse
                                    .past_reservations);
                                webengage.user.setAttribute('Cancelled Reservations',
                                    reservationResponse
                                    .cancelled_reservations);
                                webengage.user.setAttribute('User', userEngage);
                                webengage.user.setAttribute('Total Price', parseFloat(
                                    reservationResponse
                                    .total_price.toFixed(2)));
                            } else {
                                console.error('Failed to get reservations');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                        });
                    //-----------WebEngage Integration------------
                    var user = @json(Auth::guard('users')->user());
                    // localStorage.setItem('apiToken', data.data.token);
                    var timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                    $.ajax({
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')

                        },
                        type: 'POST',
                        url: '/save-timezone',
                        data: {
                            timezone: timezone
                        },
                        success: function(data) {
                            // if(data.data == null){
                            //     $('#enterotp').modal('hide');
                            //     // $('#verifyelm').modal('show');
                            // }else{
                            window.location.reload(true);
                            // }

                        }

                    });

                }

            }

        } catch (errorRes) {
            if (errorRes.hasOwnProperty("error")) {
                for (const [key, errors] of Object.entries(errorRes.error)) {
                    setErrors(key, errors);
                }
            }
            $(".loadergif").addClass('d-none');
            $(".otpverify").removeClass('d-none');
        } finally {
            smbBtnEl.disabled = false
            $(".loadergif").addClass('d-none');
            $(".otpverify").removeClass('d-none');
        }
    }

    function changeFields() {
        const temp = document.getElementById("fields")
        const btnEl = document.getElementById("btn-txt")

        if (mode != 'email') {
            if (!mode) {
                oldForm = temp.innerHTML
            }
            mode = "email"
            temp.innerHTML = `<div class="row">
                    <div class="col-md-12 mb-3">
                        <input type="email" id="email" name="email" class="form-control md-input" placeholder="Your Email">
                    </div>
                    <div class="col-md-12 mb-3">
                        <input type="password" id="password" name="password" class="form-control md-input" placeholder="Password">
                    </div>

                </div>`
            btnEl.innerText = "{{ app()->getlocale() == 'ar' ? ' هاتف' : 'Phone' }}";
            btnEl.innerText = "{{ app()->getlocale() == 'ar' ? ' هاتف' : 'Phone' }}";
        } else {
            temp.innerHTML = `<div class="after-end">
            <div class="phone-number js" id="phone">
                                    <select class="" id="phone-prepend" aria-label="Default select example">
                                        <option value="966" selected>KSA (+966)</option>
                                        {{-- <option value="977">KSA (+977)</option>
                                        <option value="998">KSA (+988)</option>
                                        <option value="999">KSA (+999)</option> --}}
                                    </select>
                                    <input type="number" name="phone"  class="numberInput" placeholder="000 000 000" onKeyDown="if(this.value.length==9 && event.keyCode!=8) return false;">
                                </div>
                              <span class="invalid-feedback">phone is required.</span></div>`;
            oldForm = ""
            mode = ""
            btnEl.innerText = "{{ app()->getlocale() == 'ar' ? 'بالبريد الالكتروني' : 'Email' }}";
            btnEl.innerText = "{{ app()->getlocale() == 'ar' ? 'بالبريد الالكتروني' : 'Email' }}";
        }
        $('.invalid-feedback').remove();
    }

    function verifyEmailOtp(e) {
        e.preventDefault();


        $(".loadergif").removeClass('d-none');
        $(".emailotpverify").addClass('d-none');

        try {
            const otpFields = document.querySelectorAll('input[name="emailotpfield[]"]');
            const otpValue = Array.from(otpFields).map(input => input.value).join('');

            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            $.ajax({
                url: '{{ route('verifyemailotp') }}',
                type: 'POST',
                data: {
                    otp: otpValue
                },

                success: function(data) {
                    $('#enteremailotp').modal('hide');
                    window.location.reload(true);
                    // window.location.href = "{{ route('home') }}"
                },
                error: function(xhr, status, error) {
                    if (xhr.responseJSON && xhr.responseJSON.message && xhr.responseJSON.message
                        .otp) {
                        const otpErrors = xhr.responseJSON.message.otp;
                        document.getElementById('emailOtpError').textContent = otpErrors[0];
                    } else {
                        // const otpErrors = ;
                        document.getElementById('emailOtpError').textContent = xhr.responseJSON
                            .message;
                    }

                }
            });

        } catch (errorRes) {
            if (errorRes.hasOwnProperty("error")) {
                for (const [key, errors] of Object.entries(errorRes.error)) {
                    setErrors(key, errors);
                }
            }
            $(".loadergif").addClass('d-none');
            $(".emailotpverify").removeClass('d-none');
        } finally {

            $(".loadergif").addClass('d-none');
            $(".emailotpverify").removeClass('d-none');
        }
    }

    $(document).on('submit', '#signUpForm', function(e) {
        try {
            // alert("hello");
            e.preventDefault();

            // const smbBtnEl = e.submitter
            // console.log(smbBtnEl);
            // smbBtnEl.disabled = true
            $(".loadergif").removeClass('d-none');
            $(".signup_submit").addClass('d-none');
            // Get the form element
            var form = document.getElementById('signUpForm');

            // Create a new FormData object
            var formData = new FormData(form);
            // Make an AJAX request
            $.ajax({
                url: '{{ route('signup') }}',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#signup').modal('hide');
                    // $('#enteremailotp').modal('show');
                    window.location.reload(true);

                    // window.location.href = "{{ route('home') }}";

                },
                error: function(xhr, status, error) {
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        if (xhr.responseJSON.message.email) {

                            const otpErrors = xhr.responseJSON.message.email;
                            document.getElementById('reg-emailError').textContent =
                                otpErrors[0];
                        } else {
                            document.getElementById('reg-emailError').textContent = '';

                        }

                        if (xhr.responseJSON.message.first_name) {
                            const otpErrors = xhr.responseJSON.message.first_name;
                            document.getElementById('reg-first-nameError').textContent =
                                otpErrors[
                                    0];
                        } else {
                            document.getElementById('reg-first-nameError').textContent = '';

                        }
                        if (xhr.responseJSON.message.last_name) {
                            const otpErrors = xhr.responseJSON.message.last_name;
                            document.getElementById('reg-last-nameError').textContent =
                                otpErrors[
                                    0];
                        } else {
                            document.getElementById('reg-last-nameError').textContent = '';
                        }
                        if (xhr.responseJSON.message.password) {
                            const otpErrors = xhr.responseJSON.message.password;
                            document.getElementById('reg-passwordError').textContent =
                                otpErrors[0];
                        } else {
                            document.getElementById('reg-passwordError').textContent = '';
                        }
                        if (xhr.responseJSON.message.password_confirmation) {
                            const otpErrors = xhr.responseJSON.message
                                .password_confirmation;
                            document.getElementById('reg-passwordConfirmError')
                                .textContent =
                                otpErrors[0];
                        } else {
                            document.getElementById('reg-passwordConfirmError')
                                .textContent = '';

                        }
                        if (xhr.responseJSON.message.token) {
                            $('#signup').modal('hide');
                            $('#staticBackdrop').modal('show');
                        }

                    } else {
                        console.log('Submit Form Carefully');
                    }

                }
            });


        } catch (errorRes) {
            if (errorRes.hasOwnProperty("error")) {
                for (const [key, errors] of Object.entries(errorRes.error)) {
                    setErrors(key, errors);
                }
            }
            $(".loadergif").addClass('d-none');
            $(".signup_submit").removeClass('d-none');
        } finally {
            // smbBtnEl.disabled = false
            $(".loadergif").addClass('d-none');
            $(".signup_submit").removeClass('d-none');
        }

    });

    $(document).on('click', '#resendEmailOtpBtn', function(e) {
        e.preventDefault();
        var $button = $(this); // Store a reference to the button

        $button.prop('disabled', true);
        $.ajax({
            url: '{{ route('resend.emailOtp') }}',
            type: 'GET',
            // data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log(response);
                $('#enteremailotp').modal('show');
                $button.prop('disabled', false);

            },
            error: function(xhr, status, error) {
                console.log('Submit Form Carefully');
                $button.prop('disabled', false);
            }
        });

    });

    function setErrors(el, errors, pEl = document, is_parent = false) {
        el = el.replace("_", "-")
        let inputEl = pEl.querySelector("#" + el)
        if (inputEl != null && inputEl != "undefined") {
            if (is_parent) inputEl = inputEl.parentElement;
            inputEl.classList.add("is-invalid")
            for (const error of errors) {
                inputEl.insertAdjacentHTML("afterend", `<div class="invalid-feedback">${error}</span>`)
            }
        }
    }



    function removeErrors() {
        const classEls = document.querySelectorAll(".is-invalid")
        const errorEls = document.querySelectorAll(".invalid-feedback")
        if (classEls != null || classEls.length > 0) {
            for (const classEl of classEls) {
                classEl.classList.remove("is-invalid")
            }
        }
        if (errorEls != null || errorEls.length > 0) {
            for (const errorEl of errorEls) {
                errorEl.remove()
            }
        }
    }

    async function readAll(e) {
        try {
            let bellEl = e.target
            if (bellEl.tagName != 'A') {
                bellEl = bellEl.parentElement
            }
            if (!!bellEl.dataset.read) {
                const formData = new FormData()
                formData.append("_token", "{{ csrf_token() }}")
                const response = await fetch("{{ route('user.notifications.read') }}", {
                    headers: {
                        'accept': 'application/json'
                    },
                    method: "POST",
                    body: formData
                });
                const data = await response.json();
                removeErrors()
                if (!response.ok) throw data;
                if (data.success) {
                    bellEl.removeAttribute("data-read")
                    const redEl = bellEl.querySelector(".notify")
                    if (!!redEl) {
                        redEl.remove()
                    }
                }
            }
        } catch (errorRes) {
            console.log(errorRes);
        }
    }

    document.addEventListener("keyup", function(e) {
        if (e.target.id.indexOf("otp-") >= 0) {
            if (e.keyCode === 8 || e.keyCode === 37) {
                const prev = e.target.previousElementSibling
                if (!!prev) {
                    prev.select();
                }
            } else if ((e.keyCode >= 48 && e.keyCode <= 57) || (e.keyCode >= 65 && e.keyCode <= 90) || (
                    e.keyCode >= 96 && e.keyCode <= 105) || e.keyCode === 39) {
                const next = e.target.nextElementSibling;
                if (!!next) {
                    next.select();
                }
            }
        }
    })
</script>
@auth
    <script type="module">
        const notEl = document.getElementById("notifications")
        if (!!notEl) {
            Echo.channel("App.Models.User.{{ auth()->id() }}")
                .notification((notification) => {
                    if (!!notification.extra) {
                        //-----------WebEngage Integration------------
                        let payload = {
                            "Host ID": notification.data['host_id'],
                            "Unit Code": notification.data['property_code']
                        }
                        webEngageTracking(HOST_PROPERTY_DATA_INCOMPLETE, payload);
                        //-----------WebEngage Integration------------
                    }
                    if (!!notification) {
                        if (notEl.children.length > 4 || notEl.hasAttribute("data-list")) {
                            if (notEl.hasAttribute("data-list")) {
                                notEl.removeAttribute("data-list")
                            }
                            if (notification.count > 5 && !document.querySelector(".ntf-see")) {
                                notEl.insertAdjacentHTML("afterend",
                                    `<div class="ntf-see"><a href="{{ route('user.notifications') }}">See all</a></div>`
                                )
                            }
                            notEl.lastElementChild.remove()
                        }
                        notEl.insertAdjacentHTML("beforeend", `<li>
                    <a href="${notification.link}" class="dropdown-item ntf-item">
                        <div class="ntf-image"><img src="{{ asset('icons/user.svg') }}" height="auto" width="auto" alt=""></div>
                            <div class="ntf-content">
                                <div class="sender-msg">
                                    <p>${notification.message}</p>
                                </div>
                            </div>
                        </div>
                    </a>
                </li>`)
                        const bellEl = document.getElementById("ntf-dropdown")
                        if (!!bellEl && !bellEl.querySelector(".notify")) {
                            bellEl.setAttribute("data-read", 1)
                            bellEl.insertAdjacentHTML("afterbegin", "<div class='notify'></div>")
                        }
                        document.querySelector(".total-ntf").textContent = notification.count
                    }
                });
        }
    </script>
    <script type="module">
        const noEl = document.getElementById("notifications")
        if (!!noEl) {
            Echo.channel("App.Models.User.{{ auth()->id() }}").notification((notification) => {
                if (!!notification.extra) {
                    //-----------WebEngage Integration------------
                    let payload = {
                        "Host ID": notification.data['host_id'],
                        "Unit Code": notification.data['property_code']
                    }
                    webEngageTracking(HOST_PROPERTY_DATA_INCOMPLETE, payload);
                    //-----------WebEngage Integration------------
                }
                if (noEl.children.length == 5 || noEl.firstElementChild.firstElementChild.href ==
                    "javascript:void(0)") {
                    noEl.removeChild(noEl.lastElementChild)
                }
                noEl.insertAdjacentHTML('afterbegin',
                    `<li><a class="dropdown-item" href="${notification.link}">${notification.message}</a></li>`)
            });
        }
    </script>
@endauth

<script>
    $(document).on('click', '.book_mark_change', function(event) {
        let authcheck = '{{ auth()->check() }}';

        if (authcheck) {
            event.preventDefault();
            var property_id = $(this).data("id");
            var property_status = $(this).data("status");
            var user_id = "{{ Auth::id() }}";
            var dataURL = APP_URL + '/add-edit-book-mark';
            var that = this;
            $.ajax({
                url: dataURL,
                data: {
                    "_token": "{{ csrf_token() }}",
                    'id': property_id,
                    'user_id': user_id,
                },
                type: 'post',
                dataType: 'json',
                success: function(data) {
                    if (appEnvironment == 'prod') {
                        if (data.addWishlist) {
                            $(that).removeData('status')
                            if (data.favourite.status == 'Active') {
                                $(that).addClass('active');
                                $(that).attr("data-status", 1);
                            } else {
                                $(that).removeClass('active');
                                $(that).attr("data-status", 0);

                            }
                        }
                    }
                }
            });
        } else {
            window.location = "{{ url('/?login') }}"
        }
    });

    $(document).on('click', '.book_mark_remove_only', function(event) {
        event.preventDefault();
        var property_id = $(this).data("id");
        var property_status = $(this).data("status");
        var user_id = "{{ Auth::id() }}";
        var dataURL = APP_URL + '/add-edit-book-mark';
        var that = this;
        $.ajax({
            url: dataURL,
            data: {
                "_token": "{{ csrf_token() }}",
                'id': property_id,
                'user_id': user_id,
            },
            type: 'post',
            dataType: 'json',
            success: function(data) {
                $(that).removeData('status')
                if (data.favourite.status == 'Active') {

                } else {
                    $(that).attr("data-status", 0);
                    $(that).closest('.col-lg-3').remove();
                    swal('success',
                            '{{ customTrans('success.favourite_remove_success') }}')
                        .then(function() {
                            location.reload();
                        });
                    swal('success',
                            '{{ customTrans('success.favourite_remove_success') }}')
                        .then(function() {
                            location.reload();
                        });
                }
            }
        });
    });

    // for page loader
    $(window).on('load', function() {
        $('body').css('overflow-x', 'hidden');
        $('.loader-bg').hide();
    });

    $('.popularcity').on('click', function() {
        $('.popularcity').removeClass('location-act');
        $(this).addClass('location-act');
        let city = $(this).data('cityname');
        $('#front-search-field').val(city);
        // $(".heade-date.date-modal").trigger("click");
    });

    $(".flash-container").fadeTo(20000, 500).slideUp(500, function() {
        $(".flash-container").slideUp(500);
    });

    $(document).ready(function() {
        $('.chat-pop').each(function() {
            if ($(this).text().trim() === '') {
                $(this).parent().remove();
            }
        });
        $('.chat-pop').each(function() {
            if ($(this).text().trim() === '') {
                $(this).parent().remove();
            }
        });
    });

    $('#currency_selector').click(function() { // Cuurency Selector onclick event
        //-----------WebEngage Integration------------ (Verified)
        let user_id = DEFAULT_USER
        let authcheck = '{{ auth()->check() }}';
        if (authcheck) {
            // user_id = "{{ Auth::id() }}";
            @auth
            var isHost = @json(auth()->user()->is_host);
        @endauth
        user_id = isHost == true ? 'Host' : DEFAULT_USER

    }
    payload = {
        "User": user_id
    }
    webEngageTracking(CURRENCY_SELECTOR, payload);
    //-----------WebEngage Integration------------
    });

    $('#search-filter-button').click(function() { // Search Filter onclick event
        //-----------WebEngage Integration------------(Verified)
        let authcheck = '{{ auth()->check() }}';
        let destination = $('#front-search-field').val()
        let startDate = $('#startDate').val()
        let endDate = $('#endDate').val()
        let adult_guest = $('#adult_guest').val()
        let child_guest = $('#child_guest').val()

        const date1 = new Date(endDate);
        const date2 = new Date(startDate);

        trackEvent('search', {
            search_term: destination,
            search_month: date2.toLocaleString('default', { month: 'long' }),
            search_duration: Math.round((date1.getTime() - date2.getTime()) / (1000 * 3600 * 24)),
            email: '{{ auth()->user()?->email }}',
            phone: '{{ auth()->user()?->phone }}'
        }, 'ga')

        trackEvent('Search', {
            contents: [],
            currency: '{{Session::get('currency')}}',
            query: destination,
            search_month: date2.toLocaleString('default', { month: 'long' }),
            search_duration: Math.round((date1.getTime() - date2.getTime()) / (1000 * 3600 * 24)),
            email: '{{ auth()->user()?->email }}',
            phone: '{{ auth()->user()?->phone }}'
        }, 'tik')

        trackEvent('SEARCH', {
            search_month: date2.toLocaleString('default', { month: 'long' }),
            search_duration: Math.round((date1.getTime() - date2.getTime()) / (1000 * 3600 * 24)),
            item_category: 'product',
            search_string: destination,
            user_email: '{{ auth()->user()?->email }}',
            user_phone_number: '{{ auth()->user()?->phone }}'
        }, ['snap'])

        startDate = dateConvertion(startDate)
        endDate = dateConvertion(endDate)

        // let user_id = UNAUTHENTIC_USER
        let User = DEFAULT_USER

        if (authcheck) {
            // user_id = "{{ Auth::id() }}";
            @auth
            var isHost = @json(auth()->user()->is_host);
        @endauth
        user = isHost == true ? 'Host' : DEFAULT_USER

    }

    guestData = {
        "Number of Adults": Number(adult_guest),
        "Number of Children": Number(child_guest),
        "User": user
    }
    webEngageTracking(NUMBER_OF_GUEST_ENTERED, guestData);

    destinationData = {
        "Destination Name": destination,
        "User": user
    }
    webEngageTracking(DESTINATION, destinationData);

    dateData = {
        "Checkin Date": startDate,
        "Checkout Date": endDate,
        "User": user
    }
    webEngageTracking(ARRIVAL_DEPARTURE_DATE, dateData);

    nightsData = {
        "Total Nights": num_of_nights,
        "User": user
    }
    webEngageTracking(NUMBER_OF_NIGHTS, nightsData);

    //-----------WebEngage Integration------------
    });

    function webEngageTracking(event, data) { //WEBENGAGE HELPER FUNCTION
        // console.log(event, data);
        webengage.track(event, data);
    }

    function dateConvertion(dateString) {
        var parts = dateString.split('/'); // Split the string by '/'
        var month = parts[0];
        var day = parts[1];
        var year = parts[2];

        // Create a formatted date string in "YYYY-MM-DD" format
        return year + '-' + month + '-' + day;
    }

    function webEngageTemplate(event, entity) {
        //-----------WebEngage Integration------------
        let user = DEFAULT_USER
        let authcheck = '{{ auth()->check() }}';
        if (authcheck) {
            @auth
            var isHost = @json(auth()->user()->is_host);
        @endauth
        user = isHost == true ? 'Host' : DEFAULT_USER

    }
    payload = {
        "User": user,
        "Value Selected": entity
    }
    webEngageTracking(event, payload);
    }

    formatDate(date) {
        let day = String(date.getDate()).padStart(2, '0');
        let month = String(date.getMonth() + 1).padStart(2, '0');
        let year = date.getFullYear();
        return `${year}-${month}-${day}`;
    }

    function isCurrentDateGreaterThan(targetDate) {
        let today = formatDate(new Date());
        const todayParts = today.split('-');
        const targetDateParts = targetDate.split('-');
        if (parseInt(todayParts[0], 10) > parseInt(targetDateParts[0], 10) || parseInt(todayParts[1], 10) > parseInt(
                targetDateParts[1], 10) || parseInt(todayParts[2], 10) > parseInt(targetDateParts[2], 10)) {
            return true
        }
        return false
    }



    // $('#wallet-pay').change(handleWalletPayChange);
    $(document).on('change', '#wallet-pay', handleWalletPayChange);


    function formatNumber(number) {
        return number.toLocaleString(undefined, {
            minimumFractionDigits: 2
        });
    }

    function handleWalletPayChange() {
        let price = $(document).find('#total').data('wallet-total');
        if ($(document).find('#wallet-pay').is(':checked')) {
            console.log('Checkbox is checked');
            var walletBalance = $(document).find('#wallet-pay').data('value'); // Retrieve the data-value attribute
            $(document).find('#wallet-value').text(-walletBalance);
            // Calculate the result of (price - walletBalance)
            var difference = price - walletBalance;
            // Check if the result is less than zero
            if (difference < 0) {
                // If it's less than zero, set formattedNumber to "0 ریال"
                var formattedNumber = "{{ customTrans('utility.sar')}} 0" ;
                if (walletBalance > price) {
                    // let updatedWallet =  walletBalance - price;
                    $(document).find('#wallet-value').text(-price);
                }
            } else {
                // If it's greater than or equal to zero, format the result
                var formattedNumber = "{{ customTrans('utility.sar')}} " + formatNumber(difference);
            }
            // Update the text of the element with id "total"
            $(document).find('#total').text(formattedNumber);
            // Do something here when the checkbox is checked
        } else {

            $(document).find('#wallet-value').text(0);
            $(document).find('#total').text("{{ customTrans('utility.sar')}} " +formatNumber(price));
            // Do something here when the checkbox is unchecked
        }
    }
</script>

</body>

</html>
