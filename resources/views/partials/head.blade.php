<!DOCTYPE html>
<html {{ Session::get('language') == 'ar' ? 'lang=ar dir=rtl' : 'lang=en dir=ltr' }}>

<head>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-MWWQT4N');</script>
    <!-- End Google Tag Manager -->
    <meta name="google-site-verification" content="HgzAaJPFG-8s5KBUHQY-eLxTyoHcGUf0t2Hj8ITIr3Y" />

    @if (isset($en_description) && Session::get('language') == 'en')
        <meta name="description" content="{{ $en_description }}">
    @elseif (isset($ar_description) && Session::get('language') == 'ar')
        <meta name="description" content="{{ $ar_description }}">
    @else
    @endif
    @if (isset($en_keywords) && Session::get('language') == 'en')
        <meta name="keywords" content="{{ $en_keywords }}">
    @elseif (isset($ar_keywords) && Session::get('language') == 'ar')
        <meta name="keywords" content="{{ $ar_keywords }}">
    @else
    @endif

    @if (isset($meta_canonical) && $meta_canonical !== 'NULL' && $meta_canonical !== '')
        <link rel="canonical" href="{{ $meta_canonical }}">
    @else
        <link rel="canonical" href="{{ url()->current() }}">
    @endif

    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- Metas For sharing property in social media -->
    <meta property="og:url" content="{{ isset($shareLink) ? $shareLink : url('/') }}" />
    <meta property="og:type" content="article" />
    {{-- <meta property="og:title"
        content="{{ $title ?? Helpers::meta(!isset($exception) ? Route::current()->uri() : '', 'title') }}" /> --}}
    <meta property="og:description"
        content="{{ isset($result->property_description->summary) ? $result->property_description->summary : Helpers::meta(!isset($exception) ? Route::current()->uri() : '', 'description') }}" />
    <meta property="og:image"
        content="{{ isset($property_id) && !empty($property_id && isset($property_photos[0]->photo)) ? asset('images/property/' . $property_id . '/' . $property_photos[0]->photo) : (defined('BANNER_URL') ? BANNER_URL : '') }}" />

    <meta name="facebook-domain-verification" content="sk256mnffjvrdijn292cylpopyiyc7" />
    <meta name="csrf-token" content="{{ csrf_token() }}">



    <link rel="stylesheet preload" href="{{ asset('css/bootstrap-slider.min.css') }}" as="style" type="text/css" />

    @if (!empty($favicon))
        <link rel="shortcut icon" href="{{ $favicon }}" />
    @endif

    {{-- <title>{{ $title ?? Helpers::meta(!isset($exception) ? Route::current()->uri() : '', 'title') }}
        {{ $additional_title ?? '' }} </title> --}}
    @if (isset($en_title) && Session::get('language') == 'en')
        <title>{{ $en_title }}</title>
    @elseif (isset($ar_title) && Session::get('language') == 'ar')
        <title>{{ $ar_title }}</title>
    @else
        <title>{{ $title ?? Helpers::meta(!isset($exception) ? Route::current()->uri() : '', 'title') }}
            {{ $additional_title ?? '' }} </title>
    @endif
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />

    <!-- CSS  new version start-->
    {{-- @vite('resources/js/app.js') --}}

    @stack('css')
    <link rel="shortcut icon" type="image/png" href="{{ asset('fav-icon/favicon.png') }}" />
    <link rel="stylesheet preload" href="{{ asset('bootstrap/css/bootstrap-grid.min.css') }}" as="style"
        crossorigin />
    <link rel="stylesheet preload" href="{{ asset('bootstrap/css/bootstrap.min.css') }}" as="style" crossorigin />
    <link rel="stylesheet preload" href="{{ asset('css/daterangepicker.css') }}" as="style" crossorigin />
    <link rel="stylesheet preload" href="{{ asset('css/slick.css') }}" as="style" crossorigin />
    <link rel="stylesheet preload" href="{{ asset('css/slick-theme.css') }}" as="style" crossorigin />
    <link rel="stylesheet preload" href="{{ asset('cdns/css/all.min.css') }}" as="style" crossorigin />


    @php
        $helper = new App\Http\Helpers\Common();
    @endphp

  {{-- ============ All CDN Local Start ============ --}}

    <link rel="stylesheet preload" href="{{ asset('cdns/css/aos.css') }}" as="style" crossorigin />
    <link rel="stylesheet preload" href="{{ asset('cdns/css/remixicon.css') }}" as="style" crossorigin />
    <link rel="stylesheet preload" href="{{ asset('cdns/css/swipper-bundle.min.css') }}" as="style" crossorigin />
    {{-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" /> --}}
    <link rel="stylesheet preload" href="{{ asset('cdns/css/dropzone.min.css') }}" as="style" crossorigin />
    {{-- <link rel="stylesheet" href="https://unpkg.com/dropzone@5/dist/min/dropzone.min.css" type="text/css" /> --}}
    <link rel="stylesheet preload" href="{{ asset('cdns/css/bootstrap-select.min.css') }}" as="style" crossorigin />
    {{-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/css/bootstrap-select.min.css"> --}}

    {{-- ============ All CDN Local End ============ --}}


    <link rel="stylesheet preload" href="{{ asset('css/responsive.css') }}?v=5" as="style" crossorigin />

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Swiper/10.3.1/swiper-bundle.css" integrity="sha512-cAtZ0Luj6XlQ7YGgi5mPW0szI2z/2+btPjOqVEqK3z4h1/qojUwvQyTcocgKKOFv8noUFH5GOuhheX7PeDwwPA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet preload" href="{{ asset('css/style.css') }}?v={{ $currentAppVersion}}" as="style"
        crossorigin />


    @if(config('services.recaptcha_v3.enable'))
    <script src="https://www.google.com/recaptcha/api.js?render={{config('services.recaptcha_v3.siteKey')}}"></script>
    @endif


    <!--CSS new version end-->

    @if (app()->getLocale() == 'ar')
        <link rel="stylesheet preload" href="{{ asset('css/style_arabic.css') }}?v={{ $currentAppVersion}}" as="style"
            crossorigin />
    @endif
    <noscript>
        <img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id=868683851060478&ev=PageView&noscript=1" />
    </noscript>

    <script>
        let citySelection;
        let TypeName;

        let appEnvironment = "{{ app()->environment() }}";
    </script>
</head>

<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MWWQT4N"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
