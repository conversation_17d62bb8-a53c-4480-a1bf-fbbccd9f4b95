@extends('mobile.template')
@push('css')
    <link rel="stylesheet" href="{{ asset('css/fancy-box.css') }}">
    <style>
        header {
            display: none !important;
        }
    </style>
@endpush
@php
    use App\Models\Settings;
    use App\Models\Properties;
    use App\Models\WishlistName;
    use Carbon\Carbon;

    $res_modal = true;
    // $checkin_session = Carbon::parse(Session::get('header_checkin'));
    // $checkout_session = Carbon::parse(Session::get('header_checkout'));
    // $checkout_session = $checkin_session->eq($checkout_session) ? $checkout_session->addDay() : $checkout_session;

    $checkin_session = request()->get('checkin') ? Carbon::parse(request()->get('checkin')) : Carbon::now();

    $checkout_session = request()->get('checkout')
        ? Carbon::parse(request()->get('checkout'))
        : Carbon::now()->addDays();

    // Ensure checkout is not the same as check-in
    if ($checkin_session->equalTo($checkout_session)) {
        $checkout_session->addDay();
    }

    // Format the dates after comparison
    $checkin_session = $checkin_session->format('m/d/Y');
    $checkout_session = $checkout_session->format('m/d/Y');

    $child_guest_session = Session::get('child_guest_session');
    $adult_guest_session = Session::get('adult_guest_session');

    // IF SESSION CLEARED
    if (!$adult_guest_session) {
        $adult_guest_session = 1;
    }
    if (!$child_guest_session) {
        $child_guest_session = 0;
    }
    $wishlist_modal = true;

    $wishlistExist = WishlistName::where('creater_id', Auth::id())->get();
    if (isset($wishlistExist)) {
        $existWishlistModal = true;
        $wishlist_modal = true;
    } else {
        $existWishlistModal = false;
        $wishlist_modal = true;
    }
@endphp
@section('main')
    <div class="property-gallery">
        <a href="javascript:void(0);" onclick="customGoBack(event)" class="floated-dt-back" id="goBack">

            <img src="{{ asset('icons/go-back.svg') }}" alt="">
        </a>
        <a href="javascript:" class="float-wishlist">
            <div class="fav-icon" data-bs-toggle="modal"
                @auth
@if ($result->wishlist == true) id="toggleWishlist"
                 @else
                     data-bs-target="{{ isset($wishlistExist) ? '#whishlist-listing' : '#create-whishlist' }}"
                 @endif @endauth
                @guest
id="guestWishlist" @endguest data-item-id="{{ $result->id }}" alt="">

                <div class="fav-in {{ $result->wishlist == true ? 'active' : '' }}  heart_icon"
                    data-status="{{ $result->book_mark }}" data-id="{{ $result->id }}">
                    <img src="{{ asset('icons/dt-heart.svg') }}" alt="" class="fav-blank">
                    <img src="{{ asset('icons/dt-heart-fill.svg') }}" alt="" class="fav-fill">

                </div>
            </div>
        </a>
        <div class="pg-slide">
            <div class="mb-slid2" id="pr-sld" data-locale="{{ app()->getLocale() }}">
                @for ($i = 0; $i < count($property_photos); $i++)
                    <div class="mb-sl-in">
                        <div class="inner-image">
                            {{-- @php
                        $imagepath = file_exists('images/property/' . $property_id . '/' .
                        $property_photos[$i]->photo)?
                        'images/property/'.$property_id . '/' . $property_photos[$i]->photo :
                        'images/default-image.png';
                        @endphp --}}
                            <a data-fancybox="gallery-mobile"
                                href="{{ asset($property_photos[$i]['photo']) }}?t={{ time() }}">
                                <img src="{{ asset('images/loader.gif') }}"
                                    data-src="{{ asset($property_photos[$i]['photo']) }}?t={{ time() }}"
                                    class="inner-image image-show lazy" rel="album-2" alt="">
                            </a>
                        </div>
                    </div>
                @endfor

            </div>
            <span class="pagingInfo"></span>
        </div>
    </div>
    <div class="property-detail">
        @if (session('error'))
            <div class=" alert alert-success">
                {!! session('error') !!}
            </div>
        @endif
        <div class="container">
            <div class="head-content">
                {{-- propertyTitle(propid, proptypeid) FROM HELPER FUNCTION / MAKE CUSTOM TITLE --APR --}}
                <h4 class="fw-500 mb-2 fs-lg">{{ propertyTitleForListing($grid_property) }}
                    @if ($result->property_discount)
                        <span class="inner-discount">{{ $result->property_discount->discount }}%</span>
                    @endif
                </h4>
                <h5 class="mb-0 fw-400">{{ app()->getLocale() == 'ar' ? $result->name_ar : $result->name }}</h5>
                <p class="mb-0">
                    {{ isset($result->property_address->city) ? transStateCity($result->property_address->city, app()->getlocale()) . ', ' : '' }}
                    {{ isset($result->property_address->state) ? transStateCity($result->property_address->state, app()->getlocale()) . ', ' : '' }}
                    {{ isset($result->property_address->countries->name) ? (app()->getlocale() == 'ar' ? $result->property_address->countries->name_ar : $result->property_address->countries->name) : '' }}

                </p>
                <p class="unit-code d-flex m-0 p-0">
                    {{ customTrans('home.unit_code') }}(<span>{{ $result->property_code }}</span>)</p>
                <ul class="d-flex m-0 p-0">
                    <li>{{ digitsToArabic($result->bedrooms) . ' ' . trans('messages.property_single.bedroom') }},
                    </li>
                    <li>&nbsp;{{ digitsToArabic($result->beds) . ' ' . trans('messages.property_single.bed') }},
                    </li>
                    <li>&nbsp;
                        {{ digitsToArabic($result->adult_guest) . ' ' . trans('messages.property_single.guest') }},
                    </li>
                    <li>&nbsp;
                        {{ digitsToArabic($result->bathrooms) . ' ' . trans('messages.property_single.bathroom') }},
                    </li>
                </ul>
                @isset($result['license_no'])
                    <p class="display-license-num">
                        <img src="{{ asset('icons/verified.svg') }}" alt="">
                        {{ customTrans('listing.license_number') }} : <span> {{ $result['license_no'] }} </span>
                    </p>
                @endisset
                <div class="property-inner-detail">
                    <div class="property-description">
                        {{-- <h5 class="mb-0 fw-400">{{ $result->name }}</h5> --}}
                        <p class="product-rate">
                            <i class="ri-star-fill"></i>
                            <span dir="ltr">{{ $result->average_rating ?? 0 }} /</span>
                            <span dir="ltr"> 5 </span>
                            {{-- {{ number_format($reviews_avg['avg_total'], $reviews_avg['avg_total'] != intval($reviews_avg['avg_total']) ? 1 : 0) ?? 0 }}/<span>5</span> --}}
                            {{-- {{ number_format($result['rating_avg'], $result['rating_avg'] != intval($result['rating_avg']) ? 1 : 0) ?? 0 }} --}}
                            {{-- /<span>5</span> --}}
                        </p>
                    </div>

                </div>
            </div>

            {{-- overview --}}
            <div class="overview">
                <h4 class="fw-400">{{ customTrans('property_single.overveiw') }}</h4>
                <p>{{ getDescription($result->property_description) }}</p>
            </div>
            {{-- over view end --}}

            {{-- user and calculator --}}
            <div>
                @if (Auth::check())
                    @if ($result->host_id != Auth::user()->id)
                        <div class="col-xl-4 col-lg-4">
                            <div class="user singal-user">
                                <div class="us-dt">
                                    <img class="profile-user"
                                        src="{{ asset($result->users?->profile_src ?? 'images/pro-img.png') }}"
                                        alt="">
                                    <div class="user-detail">
                                        <h6 class="mb-0 fw-400">{{ $result->users->full_name }}</h6>
                                        <p> {{ customTrans('property_single.joined') . ' ' . $result->users->account_since }}
                                        </p>
                                    </div>
                                </div>
                                @auth
                                    <button id="inquiry-btn" type="button" class="d-none theme-btn"
                                        onclick="getInquiry({{ $property_id }})">
                                        {{ customTrans('property_single.chat_with_the_host') }}
                                    </button>
                                @endauth
                            </div>
                            <div class="property-pricing">
                                <div class="pricing-inner">
                                    <div class="pricing d-flex align-items-baseline justify-content-between mb-4">
                                        <h4 class="fw-400 mb-0 fs-lg" id="nightPrice"></h4>
                                    </div>
                                    <form accept-charset="UTF-8" method="post" action="{{ route('payment.summary') }}"
                                        id="booking_form">
                                        @csrf
                                        <div class="row">
                                            <input type="hidden" name="property_id" value="{{ $property_id }}">

                                            <div class="col-xl-12">
                                                <label
                                                    for="">{{ customTrans('property_single.check_in_out') }}</label>
                                                <div class="single-check">
                                                    <div class="check-icon cy-positon">
                                                        <img src="{{ asset('icons/calender.svg') }}" alt="">
                                                    </div>
                                                    @php
                                                        $checkin = carbonDate($checkin_session);
                                                        $checkout = carbonDate($checkout_session);
                                                        $checkout = adDaysinDate($checkin, $checkout, 1); //if checkin and checkout same then add days(3rd parameter) in checkout
                                                    @endphp
                                                    <input type="text" name="daterange" placeholder="Date"
                                                        class="date-ui date-modal form-control pd-date text-center"
                                                        value="{{ $checkin_session . '-' . $checkout_session }}"
                                                        readonly="readonly" />
                                                </div>
                                                <div class="view-calendar cust-double-rangepicker"></div>
                                            </div>

                                            <div class="col-xl-12">
                                                @if ($result->property_price?->weekly_discount != 0 && $result->property_price?->monthly_discount != 0)
                                                    <div class="mw-discount">
                                                        {{ customTrans('property_single.monthly_and_weekly_discount') }}
                                                    </div>
                                                @elseif($result->property_price?->monthly_discount != 0)
                                                    <div class="mw-discount">
                                                        {{ customTrans('property_single.monthly_discount_available') }}
                                                    </div>
                                                @elseif($result->property_price?->weekly_discount != 0)
                                                    <div class="mw-discount">
                                                        {{ customTrans('property_single.weekly_discount_available') }}
                                                    </div>
                                                @else
                                                @endif
                                            </div>

                                            <input type="hidden" name="property_slug" id="property_slug"
                                                value="{{ Request::segment(2) }}">
                                            <input type="hidden" name="property_id" id="property_id"
                                                value="{{ $property_id }}">
                                            <input type="hidden" id="room_blocked_dates" value="">
                                            <input type="hidden" id="calendar_available_price" value="">
                                            <input type="hidden" id="room_available_price" value="">
                                            <input type="hidden" id="price_tooltip" value="">
                                            <input type="hidden" name="url_checkin" id="url_checkin"
                                                value="{{ $checkin_session }}">
                                            <input type="hidden" name="url_checkout" id="url_checkout"
                                                value="{{ $checkout_session }}">
                                            <input type="hidden" name="number_of_guests" id="number_of_guests" value=1>
                                            <input type="hidden" name="booking_type" id="booking_type"
                                                value="{{ $result->booking_type }}">
                                            <input type="hidden" name="booking_type_1" id="booking_type_1"
                                                value="0">
                                            <input type="hidden" name="paymentMethodId" id="paymentMethodId"
                                                value="2">

                                            <div class="col-xl-12 text-center d-none" id="book_it_disabled">
                                                <div class="not-available-detail-tag-main" id="book_it_disabled_message">
                                                    <div class="not-available-detail-tag">
                                                        {{ customTrans('property_single.date_not_available') }}</div>
                                                </div>

                                                <a href="{{ URL::to('/') }}/search?location={{ $result->property_address->city }}"
                                                    class="btn btn-large btn-block text-14"
                                                    id="view_other_listings_button">
                                                    {{ customTrans('property_single.view_other_list') }}
                                                </a>
                                            </div>

                                            <div class="mt-4 col-md-12 text-center" style="display:none">
                                                <label for=""><strong>{{ customTrans('property_single.guest') }}
                                                        <span id="totalguest"></span> </strong></label>
                                            </div>
                                            <div class="col-6 my-4 gst" style="display:none">
                                                <p class="mb-0 d-flex align-items-center h-100">
                                                    {{ customTrans('header.adult') }}</p>
                                            </div>

                                            <div class="col-6 my-4 gst" style="display:none">
                                                <div class="guest-counter justify-content-end">
                                                    <span class="guest_counter" data-function="decrease"> <img
                                                            src="{{ asset('icons/minus.svg') }}" alt=""></span>
                                                    <input type="text" readonly name="guest_adult"
                                                        value="{{ $adult_guest_session > $result->adult_guest ? $result->adult_guest ?? 1 : $adult_guest_session ?? 1 }}" />
                                                    <span class="guest_counter" data-function="increase"
                                                        data-limit="{{ $result->accommodates - $result->children_guest ?? 0 }}">
                                                        <img src="{{ asset('icons/plus.svg') }}" alt=""></span>
                                                </div>
                                            </div>

                                            {{-- Child Guest --}}
                                            <div class="col-6 my-2 gst" style="display:none">
                                                <p class="mb-0 d-flex align-items-center h-100">
                                                    {{ customTrans('header.children') }}</p>
                                            </div>
                                            <div class="col-6 my-2 gst" style="display:none">
                                                <div class="guest-counter justify-content-end">
                                                    <span class="guest_counter" data-function="decrease"><img
                                                            src="{{ asset('icons/minus.svg') }}" alt=""></span>
                                                    <input type="text" readonly name="guest_child"
                                                        value="{{ $child_guest_session > $result->children_guest ? $result->children_guest ?? 0 : $child_guest_session ?? 0 }}" />
                                                    <span class="guest_counter" data-function="increase"
                                                        data-limit="{{ $result->children_guest ?? 0 }}"><img
                                                            src="{{ asset('icons/plus.svg') }}" alt=""></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="book_it" class="mt-4 property-list">
                                            <div class="js-subtotal-container booking-subtotal panel-padding-fit">
                                                <div id="loader" class="display-off single-load">
                                                    <div class="spinner"></div>
                                                </div>
                                                <div class="h-100">
                                                    <table class="table table-bordered price_table" id="booking_table">
                                                        <tbody>
                                                            <div id="append_date">

                                                            </div>
                                                            <tr>
                                                                <td class="pl-4 w-50">
                                                                    <span id="total_night_count" value="">0</span>
                                                                    {{ customTrans('property_single.night') }}
                                                                    <span dir="ltr">x</span>
                                                                    <span id="per_night_price" value="">0</span>
                                                                </td>
                                                                <td class="pl-4 text-right">
                                                                    <span id="total_night_price" value=""> 0 </span>
                                                                    <span id="custom_price"
                                                                        class="fa fa-info-circle secondary-text-color"
                                                                        data-html="true" data-toggle="tooltip"
                                                                        data-placement="top" title=""></span>
                                                                </td>
                                                            </tr>

                                                            <tr>
                                                                <td class="pl-4">
                                                                    {{ customTrans('property_single.service_fee') }}
                                                                </td>
                                                                <td class="pl-4 text-right"><span id="service_fee"
                                                                        value=""> 0
                                                                    </span></td>
                                                            </tr>


                                                            <tr class="additional_price">
                                                                <td class="pl-4">
                                                                    {{ customTrans('property_single.additional_guest_fee') }}
                                                                </td>
                                                                <td class="pl-4 text-right"><span id="additional_guest"
                                                                        value=""> 0
                                                                    </span>
                                                                </td>
                                                            </tr>

                                                            <tr class="security_price">
                                                                <td class="pl-4">
                                                                    {{ customTrans('property_single.security_fee') }}
                                                                </td>
                                                                <td class="pl-4 text-right"><span id="security_fee"
                                                                        value=""> 0
                                                                    </span></td>
                                                            </tr>

                                                            <tr class="cleaning_price">
                                                                <td class="pl-4">
                                                                    {{ customTrans('property_single.cleaning_fee') }}
                                                                </td>
                                                                <td class="pl-4 text-right"><span id="cleaning_fee"
                                                                        value=""> 0
                                                                    </span></td>
                                                            </tr>

                                                            <tr class="iva_tax">
                                                                <td class="pl-4">
                                                                    {{ customTrans('property_single.iva_tax') }}
                                                                </td>
                                                                <td class="pl-4 text-right"> <span id="iva_tax"
                                                                        value=""> 0 </span>
                                                                </td>
                                                            </tr>


                                                            <tr class="accomodation_tax">
                                                                <td class="pl-4">
                                                                    {{ customTrans('property_single.accommodatiton_tax') }}
                                                                </td>
                                                                <td class="pl-4 text-right"> <span id="accomodation_tax"
                                                                        value=""> 0
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                            <tr class="iva_tax">
                                                                <td class="pl-4">
                                                                    {{ customTrans('property_single.iva_tax') }}
                                                                </td>
                                                                <td class="pl-4 text-right"> <span id="iva_tax"
                                                                        value=""> 0 </span>
                                                                </td>
                                                            </tr>


                                                            <tr id="yousaved_main" style="display:none">
                                                                <td class="pl-4">
                                                                    @if ($result->property_price?->weekly_discount != 0 && $result->property_price?->monthly_discount != 0)
                                                                        {{ customTrans('property_single.monthly_and_weekly_discount_mw') }}
                                                                    @elseif($result->property_price?->monthly_discount != 0)
                                                                        {{ customTrans('property_single.monthly_discount_available_mw') }}
                                                                    @elseif($result->property_price?->weekly_discount != 0)
                                                                        {{ customTrans('property_single.weekly_discount_available_mw') }}
                                                                    @else
                                                                    @endif
                                                                </td>
                                                                <th class="pl-4 text-right text-danger"><span
                                                                        class="text-danger">-</span><span id="yousaved"
                                                                        data-total="" value="">0</span></th>
                                                            </tr>

                                                            <tr class="d-none disamount_row">
                                                                <th class="pl-4">
                                                                    {{ customTrans('property_single.you_saved') }}</th>
                                                                <th class="pl-4 text-right text-danger">
                                                                    <span class="text-danger">-</span>
                                                                    <span id="disvalue" class="text-danger">0</span>
                                                                </th>
                                                            </tr>
                                                            <tr>
                                                                <th class="pl-4">
                                                                    Wallet
                                                                </th>
                                                                <th class="pl-4 text-right" style="color:red"><span
                                                                        id="wallet-value" value="">0</span>
                                                                </th>
                                                            </tr>
                                                            <tr>
                                                                <th class="pl-4">
                                                                    {{ customTrans('property_single.total_include_vat') }}
                                                                </th>
                                                                <th class="pl-4 text-right"><span id="total"
                                                                        data-total="" data-wallet-total=''
                                                                        value="">0</span></th>
                                                            </tr>
                                                            <tr class="d-none finalamount_row">
                                                                <th class="pl-4">
                                                                    {{ customTrans('property_single.total_final_amount') }}
                                                                </th>
                                                                <th class="pl-4 text-right"><span id="final_amount"
                                                                        data-total="" value="">0</span></th>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    <span class="vat-msg">
                                                        All prices include VAT

                                                    </span>
                                                    <p class="vat-msg m-0 text-center">
                                                        {{ customTrans('property_single.price_vary_on_weekends') }}
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="pay-sec" style="display: flex; justify-content: space-around;align-items: center;margin:20px 0;">
                                                <div class="pay-sl-content">
                                                    <img src="{{ asset('images/visa-master-pay.png') }}"
                                                         alt="">
                                                </div>
                                                <div class="pay-sl-content">
                                                    <img src="{{ asset('images/stc-pay.png') }}"
                                                         alt="">
                                                </div>
                                                <div class="pay-sl-content">
                                                    <img src="{{ asset('images/mada-pay.png') }}"
                                                         alt="">
                                                </div>
                                                @if ($showPayOpts)
                                                    <div class="pay-sl-content">
                                                        <img src="{{ asset('images/apple_pay.png') }}"
                                                             alt="">
                                                    </div>
                                                @endif
                                                <div class="pay-sl-content">
                                                    <img src="{{ asset('icons/tabby-logo-en.svg') }}"
                                                         alt="">
                                                </div>
                                            </div>
                                            <div id="min_nights" class="text-center d-none">
                                                <p class="icon-rausch text-danger">
                                                    Booking Nights must be greater than or equal to Min nights <span
                                                        id="min_night_message"></span>
                                                </p>

                                            </div>
                                            <div id="max_nights" class="text-center d-none">
                                                <p class="icon-rausch text-danger">
                                                    Booking Nights must be less than or equal to Max nights <span
                                                        id="max_night_message"></span>
                                                </p>

                                            </div>

                                            <div id="minimum_disabled" class="text-center d-none">
                                                <p class="icon-rausch text-danger">
                                                    {{ customTrans('property_single.you_have_book') }} <span
                                                        id="minimum_disabled_message"></span>
                                                    {{ customTrans('property_single.night_dates') }}
                                                </p>
                                                <a href="{{ URL::to('/') }}/search?location={{ $result->property_address->city }}"
                                                    class="btn btn-large btn-block text-14"
                                                    id="view_other_listings_button">
                                                    {{ customTrans('property_single.view_other_list') }}
                                                </a>
                                            </div>
                                            <div class="book_btn col-md-12 text-center">
                                                <!-- <small class="text-warning cardfields-note">{{ customTrans('payment.enter_card_detail_you_do_not_have') }}</small>
                                                            <div class="row cardfields-div mt-3">
                                                                <div class="col-md-12 mb-3">
                                                                    <input type="text" name="name" id="name"
                                                                        placeholder="{{ customTrans('payment.card_name') }}"
                                                                        class="form-control cardfields card_name only-character-valid">
                                                                </div>
                                                                <div class="col-md-8 mb-3">
                                                                    <input type="number" name="number" amxlength="14" required
                                                                        id="card"
                                                                        placeholder="{{ customTrans('payment.card_number') }}"
                                                                        class="form-control cardfields card_number"
                                                                        onKeyDown="if(this.value.length==16 && event.keyCode!=8) return false;">
                                                                </div>
                                                                <div class="col-md-4 mb-3">
                                                                    <input type="number" name="cvv" id="cvv"
                                                                        placeholder="{{ customTrans('payment.cvv') }}"
                                                                        class="form-control cardfields cvv"
                                                                        onKeyDown="if(this.value.length==4 && event.keyCode!=8) return false;">
                                                                </div>
                                                                <div class="col-md-12 mb-3">
                                                                    <div class="row en-position">
                                                                        <div class="col-6">
                                                                            <input type="text" name="month" maxlength="2"
                                                                                pattern="" id="expiry_month"
                                                                                placeholder="{{ customTrans('payment.mm') }}"class="form-control cardfields cardMonth">
                                                                        </div>
                                                                        <div class="col-6">
                                                                            <input type="text" name="year" maxlength="2"
                                                                                pattern="" id="expiry_year"
                                                                                placeholder="{{ customTrans('payment.yy') }}"
                                                                                class="form-control cardfields year">
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div> -->
                                                <div class="col-xl-12">
                                                    {{-- <table class="table table-bordered discountTable d-none ">
                                                        <tr>
                                                            <th class="text-left">Discount Type: </th>
                                                            <td id="distype"></td>
                                                        </tr>
                                                        <tr>
                                                            <th class="text-left">Discount Value: </th>
                                                            <td id="disvalue"></td>
                                                        </tr>
                                                        <tr>
                                                            <th class="text-left">Amount after Discount: </th>
                                                            <td id="afteramount"></td>
                                                        </tr>
                                                    </table> --}}
                                                    {{-- @if (auth()->user()->is_email_verified == 1) --}}
                                                    <button type="button" class="theme-btn reserve" id="go_to_payment" disabled>
                                                        {{ $result->booking_type != 'instant'
                                                            ? trans('messages.property_single.request_book')
                                                            : trans('messages.property_single.instant_book') }}
                                                    </button>
                                                    <button type="button" class="theme-btn reserve mt-1" style="background-color: #25D366; border-color: #25D366;" id="whatsapp_booking" onclick="openWhatsAppChat()">
                                                    <i class="fab fa-whatsapp" style="margin-right: 8px;"></i>
                                                    {{ $result->booking_type != 'instant'
                                                        ? customTrans('property_single.request_book')
                                                        : customTrans('property_single.instant_book') }}
                                                    
                                                </button>

                                        
                                                    {{-- @else
                                                    <button type="button" class="theme-btn sendEmailOtp">
                                                        {{ $result->booking_type != 'instant'
                                                            ? trans('messages.property_single.request_book')
                                                            : trans('messages.property_single.instant_book') }}
                                                    </button>
                                                @endif --}}

                                             
                                                </div>
                                            </div>
                                            <p class="col-md-12 text-center mt-3">
                                                {{ customTrans('property_single.review_of_pay') }}</p>
                                            <div class="tabby">
                                                <a href="javascript:" class="btn-tabby tabby-instal-btn">
                                                    <span><img src="{{ asset('icons/tabby-logo-en.svg') }}"
                                                            alt=""></span><span>{{ customTrans('tabby.payin_interest') }}</span>
                                                </a>
                                            </div>
                                        </div>

                                        <input id="hosting_id" name="hosting_id" type="hidden"
                                            value="{{ $result->host_id }}">
                                    </form>
                                </div>
                            </div>
                        </div>
                    @endif
                @else
                    <div class="col-xl-4 col-lg-4">
                        <div class="user singal-user">
                            <div class="us-dt">
                                <img class="profile-user"
                                    src="{{ asset($result->users?->profile_src ?? 'images/pro-img.png') }}"
                                    alt="">
                                <div class="user-detail">
                                    <h6 class="mb-0 fw-400">{{ $result->users->full_name }}</h6>
                                    <p> {{ customTrans('property_single.joined') . ' ' . $result->users->account_since }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="property-pricing">
                            <div class="pricing-inner">
                                <div class="pricing d-flex align-items-baseline justify-content-between mb-4">
                                    <h4 class="fw-400 mb-0 fs-lg" id="nightPrice"></h4>
                                </div>
                                <form accept-charset="UTF-8" method="post" action="{{ route('payment.summary') }}"
                                    id="booking_form">
                                    @csrf
                                    <div class="row">
                                        <input type="hidden" name="property_id" value="{{ $property_id }}">


                                        <div class="col-xl-12">
                                            <label
                                                for="">{{ customTrans('property_single.check_in_out') }}</label>
                                            <div class="single-check">
                                                <div class="check-icon cy-positon">
                                                    <img src="{{ asset('icons/calender.svg') }}" alt="">
                                                </div>
                                                @php
                                                    $checkin = carbonDate($checkin_session); //carbonDate - if given is not set it will return current date
                                                    $checkout = carbonDate($checkout_session);
                                                    $checkout = adDaysinDate($checkin, $checkout, 1); //adDaysinDate - if checkin and checkout same then add days(3rd parameter) in checkout
                                                @endphp
                                                <input type="text" name="daterange" placeholder="Date"
                                                    class="date-ui date-modal form-control text-center pd-date"
                                                    value="{{ $checkin_session . '-' . $checkout_session }}"
                                                    readonly="readonly" />
                                            </div>
                                            <div class="view-calendar cust-double-rangepicker"></div>
                                        </div>

                                        <div class="col-xl-12">
                                            @if ($result->property_price?->weekly_discount != 0 && $result->property_price?->monthly_discount != 0)
                                                <div class="mw-discount">
                                                    {{ customTrans('property_single.monthly_and_weekly_discount') }}
                                                </div>
                                            @elseif($result->property_price?->monthly_discount != 0)
                                                <div class="mw-discount">
                                                    {{ customTrans('property_single.monthly_discount_available') }}</div>
                                            @elseif($result->property_price?->weekly_discount != 0)
                                                <div class="mw-discount">
                                                    {{ customTrans('property_single.weekly_discount_available') }}</div>
                                            @else
                                            @endif
                                        </div>

                                        <input type="hidden" name="property_slug" id="property_slug"
                                            value="{{ Request::segment(2) }}">
                                        <input type="hidden" name="property_id" id="property_id"
                                            value="{{ $property_id }}">
                                        <input type="hidden" id="room_blocked_dates" value="">
                                        <input type="hidden" id="calendar_available_price" value="">
                                        <input type="hidden" id="room_available_price" value="">
                                        <input type="hidden" id="price_tooltip" value="">
                                        <input type="hidden" name="url_checkin" id="url_checkin"
                                            value="{{ $checkin_session }}">
                                        <input type="hidden" name="url_checkout" id="url_checkout"
                                            value="{{ $checkout_session }}">
                                        <input type="hidden" name="number_of_guests" id="number_of_guests"
                                            value="{{ $adult_guest_session > $result->adult_guest ? $result->adult_guest ?? 1 : $adult_guest_session ?? 1 }}">
                                        {{-- <input type="hidden" name="url_guests" id="url_guests" value="{{ $guests }}"> --}}
                                        <input type="hidden" name="booking_type" id="booking_type"
                                            value="{{ $result->booking_type }}">
                                        <input type="hidden" name="booking_type_1" id="booking_type_1" value="0">
                                        <input type="hidden" name="paymentMethodId" id="paymentMethodId"
                                            value="2">

                                        <div class="col-xl-12 text-center d-none" id="book_it_disabled">
                                            <div class="not-available-detail-tag-main" id="book_it_disabled_message">
                                                <div class="not-available-detail-tag">
                                                    {{ customTrans('property_single.date_not_available') }}</div>
                                            </div>

                                            <a href="{{ URL::to('/') }}/search?location={{ $result->property_address->city }}"
                                                class="btn btn-large btn-block text-14" id="view_other_listings_button">
                                                {{ customTrans('property_single.view_other_list') }}
                                            </a>
                                        </div>

                                        <div class="mt-4 col-md-12 text-center" style="display:none">
                                            <label for=""><strong>{{ customTrans('property_single.guest') }}
                                                    <span id="totalguest">0</span> </strong></label>
                                        </div>
                                        <div class="col-6 my-4 gst" style="display:none">
                                            <p class="mb-0 d-flex align-items-center h-100">
                                                {{ customTrans('header.adult') }}</p>
                                        </div>

                                        <div class="col-6 my-4 gst" style="display:none">
                                            <div class="guest-counter justify-content-end">
                                                <span class="guest_counter" data-function="decrease"> <img
                                                        src="{{ asset('icons/minus.svg') }}" alt=""></span>
                                                <input type="text" readonly name="guest_adult"
                                                    value="{{ $adult_guest_session > $result->accommodates - $result->children_guest ? $result->accommodates - $result->children_guest : $adult_guest_session }}" />
                                                <span class="guest_counter" data-function="increase"
                                                    data-limit="{{ $result->accommodates - $result->children_guest ?? 0 }}">
                                                    <img src="{{ asset('icons/plus.svg') }}" alt=""></span>
                                            </div>
                                        </div>

                                        {{-- Child Guest --}}
                                        <div class="col-6 my-2 gst" style="display:none">
                                            <p class="mb-0 d-flex align-items-center h-100">
                                                {{ customTrans('header.children') }}</p>
                                        </div>

                                        <div class="col-6 my-2 gst" style="display:none">
                                            <div class="guest-counter justify-content-end">
                                                <span class="guest_counter" data-function="decrease"><img
                                                        src="{{ asset('icons/minus.svg') }}" alt=""></span>
                                                <input type="text" readonly name="guest_child"
                                                    value="{{ $child_guest_session > $result->children_guest ? $result->children_guest ?? 0 : $child_guest_session ?? 0 }}" />
                                                <span class="guest_counter" data-function="increase"
                                                    data-limit="{{ $result->children_guest ?? 0 }}"><img
                                                        src="{{ asset('icons/plus.svg') }}" alt=""></span>
                                            </div>
                                        </div>
                                        {{-- @if (!$result->children_guest)
                                            <small class="text-warning child-error">Childrens are not allowed in this
                                                property</small>
                                        @endif --}}
                                    </div>

                                    <div id="book_it" class="mt-4 property-list">
                                        <div class="js-subtotal-container booking-subtotal panel-padding-fit">
                                            <div id="loader" class="display-off single-load">
                                                <div class="spinner"></div>
                                            </div>
                                            <div class="h-100">
                                                <table class="table table-bordered price_table" id="booking_table">
                                                    <tbody>
                                                        <div id="append_date">

                                                        </div>
                                                        <tr>
                                                            <td class="pl-4 w-50">
                                                                <span id="total_night_count" value="">0</span>
                                                                {{ customTrans('property_single.night') }}
                                                                <span dir="ltr">x</span>
                                                                <span id="per_night_price" value="">0</span>
                                                            </td>
                                                            <td class="pl-4 text-right"><span id="total_night_price"
                                                                    value=""> 0
                                                                </span>
                                                                <span id="custom_price"
                                                                    class="fa fa-info-circle secondary-text-color"
                                                                    data-html="true" data-toggle="tooltip"
                                                                    data-placement="top" title=""></span>
                                                            </td>
                                                        </tr>
                                                        <tr class="additional_price">
                                                            <td class="pl-4">
                                                                {{ customTrans('property_single.additional_guest_fee') }}
                                                            </td>
                                                            <td class="pl-4 text-right"><span id="additional_guest"
                                                                    value=""> 0
                                                                </span>
                                                            </td>
                                                        </tr>

                                                        <tr class="security_price">
                                                            <td class="pl-4">
                                                                {{ customTrans('property_single.security_fee') }}
                                                            </td>
                                                            <td class="pl-4 text-right"><span id="security_fee"
                                                                    value=""> 0
                                                                </span></td>
                                                        </tr>

                                                        <tr class="cleaning_price">
                                                            <td class="pl-4">
                                                                {{ customTrans('property_single.cleaning_fee') }}
                                                            </td>
                                                            <td class="pl-4 text-right"><span id="cleaning_fee"
                                                                    value=""> 0
                                                                </span></td>
                                                        </tr>

                                                        <tr class="iva_tax">
                                                            <td class="pl-4">
                                                                {{ customTrans('property_single.iva_tax') }}
                                                            </td>
                                                            <td class="pl-4 text-right"> <span id="iva_tax"
                                                                    value=""> 0 </span>
                                                            </td>
                                                        </tr>

                                                        <tr class="accomodation_tax">
                                                            <td class="pl-4">
                                                                {{ customTrans('property_single.accommodatiton_tax') }}
                                                            </td>
                                                            <td class="pl-4 text-right"> <span id="accomodation_tax"
                                                                    value=""> 0
                                                                </span>
                                                            </td>
                                                        </tr>

                                                        <tr>
                                                            <td class="pl-4">
                                                                {{ customTrans('property_single.service_fee') }}
                                                            </td>
                                                            <td class="pl-4 text-right"><span id="service_fee"
                                                                    value=""> 0
                                                                </span></td>
                                                        </tr>

                                                        <tr id="yousaved_main" style="display: none">
                                                            <td class="pl-4">
                                                                @if ($result->property_price?->weekly_discount != 0 && $result->property_price?->monthly_discount != 0)
                                                                    {{ customTrans('property_single.monthly_and_weekly_discount_mw') }}
                                                                @elseif($result->property_price?->monthly_discount != 0)
                                                                    {{ customTrans('property_single.monthly_discount_available_mw') }}
                                                                @elseif($result->property_price?->weekly_discount != 0)
                                                                    {{ customTrans('property_single.weekly_discount_available_mw') }}
                                                                @else
                                                                @endif
                                                            </td>
                                                            <th class="pl-4 text-right text-danger"><span
                                                                    class="text-danger">-</span><span id="yousaved"
                                                                    data-total="" value="">0</span></th>
                                                        </tr>

                                                        <tr class="d-none disamount_row">
                                                            <th class="pl-4">
                                                                {{ customTrans('property_single.you_saved') }}</th>
                                                            <th class="pl-4 text-right text-danger">
                                                                <span class="text-danger">-</span>
                                                                <span id="disvalue" class="text-danger">0</span>
                                                            </th>
                                                        </tr>
                                                        <tr>
                                                            <th class="pl-4">
                                                                {{ customTrans('property_single.total_include_vat') }}
                                                            </th>
                                                            <th class="pl-4 text-right"><span id="total"
                                                                    data-total="" value="">0</span></th>
                                                        </tr>

                                                        <tr class="d-none finalamount_row">
                                                            <th class="pl-4">
                                                                {{ customTrans('property_single.total_final_amount') }}
                                                            </th>
                                                            <th class="pl-4 text-right"><span id="final_amount"
                                                                    data-total="" value="">0</span></th>
                                                        </tr>


                                                    </tbody>
                                                </table>
                                                <span class="vat-msg">
                                                    All prices include VAT

                                                </span>
                                                <p class="vat-msg m-0 text-center">
                                                    {{ customTrans('property_single.price_vary_on_weekends') }}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="pay-sec" style="display: flex; justify-content: space-around;align-items: center;margin:20px 0;">
                                            <div class="pay-sl-content">
                                                <img src="{{ asset('images/visa-master-pay.png') }}"
                                                     alt="">
                                            </div>
                                            <div class="pay-sl-content">
                                                <img src="{{ asset('images/stc-pay.png') }}"
                                                     alt="">
                                            </div>
                                            <div class="pay-sl-content">
                                                <img src="{{ asset('images/mada-pay.png') }}"
                                                     alt="">
                                            </div>
                                            @if ($showPayOpts)
                                                <div class="pay-sl-content">
                                                    <img src="{{ asset('images/apple_pay.png') }}"
                                                         alt="">
                                                </div>
                                            @endif
                                            <div class="pay-sl-content">
                                                <img src="{{ asset('icons/tabby-logo-en.svg') }}"
                                                     alt="">
                                            </div>
                                        </div>

                                        <div id="min_nights" class="text-center d-none">
                                            <p class="icon-rausch text-danger">
                                                Booking Nights must be greater than or equal to Min nights <span
                                                    id="min_night_message"></span>
                                            </p>

                                        </div>
                                        <div id="max_nights" class="text-center d-none">
                                            <p class="icon-rausch text-danger">
                                                Booking Nights must be less than or equal to Max nights <span
                                                    id="max_night_message"></span>
                                            </p>

                                        </div>

                                        <div id="minimum_disabled" class="text-center d-none">
                                            <p class="icon-rausch text-danger">
                                                {{ customTrans('property_single.you_have_book') }} <span
                                                    id="minimum_disabled_message"></span>
                                                {{ customTrans('property_single.night_dates') }}
                                            </p>
                                            <a href="{{ URL::to('/') }}/search?location={{ $result->property_address->city }}"
                                                class="btn btn-large btn-block text-14" id="view_other_listings_button">
                                                {{ customTrans('property_single.view_other_list') }}
                                            </a>
                                        </div>
                                        <!-- <small class="text-warning cardfields-note">{{ customTrans('payment.enter_card_detail_you_do_not_have') }}</small>
                                                        <div class="row cardfields-div mt-3">
                                                            <div class="col-md-12 mb-3">
                                                                <input type="text" name="name" id="name"
                                                                    placeholder="{{ customTrans('payment.card_name') }}"
                                                                    class="form-control cardfields">
                                                            </div>
                                                            <div class="col-md-8 mb-3">
                                                                <input type="number" name="number" amxlength="14" required
                                                                    id="card"
                                                                    placeholder="{{ customTrans('payment.card_number') }}"
                                                                    class="form-control cardfields card_number"
                                                                    onKeyDown="if(this.value.length==16 && event.keyCode!=8) return false;">
                                                            </div>
                                                            <div class="col-md-4 mb-3">
                                                                <input type="number" name="cvv" id="cvv"
                                                                    placeholder="{{ customTrans('payment.cvv') }}"
                                                                    class="form-control cardfields cvv"
                                                                    onKeyDown="if(this.value.length==4 && event.keyCode!=3) return false;">
                                                            </div>
                                                            <div class="col-md-12 mb-3">
                                                                <div class="row en-position">
                                                                    <div class="col-6">
                                                                        <input type="text" name="month" maxlength="2"
                                                                            pattern="" id="expiry_month"
                                                                            placeholder="{{ customTrans('payment.mm') }}"class="form-control cardfields cardMonth">
                                                                    </div>
                                                                    <div class="col-6">
                                                                        <input type="text" name="year" maxlength="2"
                                                                            pattern="" id="expiry_year"
                                                                            placeholder="{{ customTrans('payment.yy') }}"
                                                                            class="form-control cardfields year">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div> -->
                                        <div class="col-xl-12 mt-3">
                                            {{-- <table class="table table-bordered discountTable d-none">
                                                    <tr>
                                                        <th class="text-left">Discount Type: </th>
                                                        <td id="distype"></td>
                                                    </tr>
                                                    <tr>
                                                        <th class="text-left">Discount Value: </th>
                                                        <td id="disvalue"></td>
                                                    </tr>
                                                    <tr>
                                                        <th class="text-left">Amount after Discount: </th>
                                                        <td id="afteramount"></td>
                                                    </tr>
                                                </table> --}}

                                            <button type="button" class="theme-btn reserve" id="go_to_payment">
                                                {{ $result->booking_type != 'instant'
                                                    ? trans('messages.property_single.request_book')
                                                    : trans('messages.property_single.instant_book') }}
                                            </button>

                                            <button type="button" class="theme-btn reserve mt-1" style="background-color: #25D366; border-color: #25D366;" id="whatsapp_booking" onclick="openWhatsAppChat()">
                                                    <i class="fab fa-whatsapp" style="margin-right: 8px;"></i>
                                                    {{ $result->booking_type != 'instant'
                                                        ? customTrans('property_single.request_book')
                                                        : customTrans('property_single.instant_book') }}
                                                    
                                                </button>
                                        
                                        </div>
                                    </div>

                                    <p class="col-md-12 text-center mt-3">
                                        {{ customTrans('property_single.review_of_pay') }}</p>
                                    <div class="tabby">
                                        <a href="javascript:" class="btn-tabby" data-bs-toggle="modal"
                                            data-bs-target="#tabby">
                                            <span><img src="{{ asset('icons/tabby-logo-en.svg') }}"
                                                    alt=""></span><span>{{ customTrans('tabby.payin') }}</span>
                                        </a>
                                    </div>
                            </div>

                            <input id="hosting_id" name="hosting_id" type="hidden" value="{{ $result->host_id }}">
                            </form>
                        </div>
                    </div>
            </div>
            @endif
            {{-- user and calculator end --}}

            {{-- Amenities start --}}

            <div class="">
                <div class="amenities property-feature-list">
                    <h4 class="fw-400">{{ customTrans('property_single.amenity') }}</h4>
                    <ul class="d-flex flex-wrap m-0 p-0">
                        @foreach ($amenities as $all_amenities)
                            @if ($all_amenities->status != null)
                                <li>
                                    <div class="am-icon">
                                        <img src="{{ asset('icons/' . $all_amenities->icon_image) }}" alt="">
                                    </div>
                                    <p>{{ app()->getLocale() == 'ar' ? $all_amenities->title_ar : $all_amenities->title }}
                                    </p>
                                </li>
                            @endif
                        @endforeach
                    </ul>
                </div>
                @if ($safety_amenities_count > 0)
                    <div class="safety-feature property-feature-list">
                        <h4 class="fw-400">{{ customTrans('property_single.safety_feature') }}</h4>
                        <ul class="d-flex flex-wrap m-0 p-0">
                            @foreach ($safety_amenities as $row_safety)
                                @if ($row_safety->status != null)
                                    <li>
                                        <div class="am-icon">
                                            <img src="{{ asset('icons/' . $row_safety->icon_image) }}" alt="">
                                        </div>
                                        <p>{{ app()->getLocale() == 'ar' ? $row_safety->title_ar : $row_safety->title }}
                                        </p>
                                    </li>
                                @endif
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if ($result->custom_amenities)
                    <div class="amenities property-feature-list">
                        <h4 class="fw-400">Custom Amenities</h4>
                        <ul class="cs-amt">
                            @foreach (json_decode($result->custom_amenities) ?? [] as $amenity)
                                @if ($all_amenities->status != null)
                                    <li>
                                        <p>{{ app()->getLocale() == 'ar' ? $amenity : $amenity }}
                                        </p>
                                    </li>
                                @endif
                            @endforeach
                        </ul>
                    </div>
                @endif

                <div class="additional-info property-feature-list">
                    <h4 class="fw-400 mb-4">{{ customTrans('property_single.additional_information') }}</h4>
                    <div class="">
                        <div class="">
                            <h6 class="fw-400 mb-3">{{ customTrans('property_single.accommodation_rules') }}</h6>
                            <ul class="m-0 p-0">
                                <li>
                                    <div class="am-icon"><img src="{{ asset('icons/check-in.svg') }}" alt="">
                                    </div>
                                    <p>{{ customTrans('property_single.check_in') }}</p>
                                    <span class="mx-3 me-0">{{ date('h:i A', strtotime($result->checkinTime)) }}</span>
                                </li>
                                <li>
                                    <div class="am-icon"><img src="{{ asset('icons/check-out.svg') }}" alt="">
                                    </div>
                                    <p>{{ customTrans('property_single.check_out') }}</p>
                                    <span class="mx-3 me-0">{{ date('h:i A', strtotime($result->checkoutTime)) }}</span>
                                </li>

                                @if ($result->children_guest)
                                    <li class="align-items-baseline">
                                        <div class="am-icon"><img src="{{ asset('icons/for-child.svg') }}"
                                                alt="">
                                        </div>
                                        <p class="suitable">{{ customTrans('property_single.suitable_for_children') }}
                                        </p>
                                    </li>
                                @endif
                            </ul>
                        </div>
                        <div class="">
                            <h6 class="fw-400 mb-3">{{ customTrans('payment.house_rule') }}</h6>
                            <ul class="m-0 p-0">
                                {{-- <li>
                                    <div class="am-icon"><img src="{{ asset('icons/pets.svg') }}" alt="">
                                    </div>
                                    <p>{{ app()->getLocale() == 'ar' ? 'حيوانات أليفة غير مسموح' : 'Pets are allowed' }}
                                    </p>
                                </li>
                                <li>
                                    <div class="am-icon"><img src="{{ asset('icons/guest.svg') }}" alt="">
                                    </div>
                                    <p>{{ app()->getLocale() == 'ar' ? ' ضيوف غير مسموح' : 'Guests are not allowed' }}
                                    </p>
                                </li>
                                <li>
                                    <div class="am-icon"><img src="{{ asset('icons/smoking.svg') }}" alt="">
                                    </div>
                                    <p>{{ app()->getLocale() == 'ar' ? ' التدخين غير مسموح' : 'Smoking not allowed' }}
                                    </p>
                                </li> --}}
                                @forelse ($house_rule_amenities as $row_house_rule)
                                    @if ($row_house_rule->status != null)
                                        <li>
                                            <div class="am-icon">
                                                <img src="{{ asset('icons/' . $row_house_rule->icon_image) }}"
                                                    alt="">
                                            </div>
                                            <p>{{ app()->getLocale() == 'ar' ? $row_house_rule->title_ar : $row_house_rule->title }}
                                            </p>
                                        </li>
                                    @endif
                                @empty
                                    <p>{{ customTrans('property_single.no_house_rule_available') }}</p>
                                @endforelse
                            </ul>
                        </div>
                        <div class="">
                            <h6 class="fw-400 mb-3">{{ customTrans('property_single.cancellations') }}</h6>
                            <ul class="m-0 p-0">
                                <li>
                                    <div class="am-icon"><img src="{{ asset('icons/moderate.svg') }}" alt="">
                                    </div>
                                    <span>
                                        @if (app()->getLocale() == 'ar')
                                            @switch($result->cancellation)
                                                @case('Flexible')
                                                    {{ 'مرن' }}
                                                @break

                                                @case('Moderate')
                                                    {{ 'معتدل' }}
                                                @break

                                                @case('Firm')
                                                    {{ 'حازم' }}
                                                @break

                                                @case('Strict')
                                                    {{ 'حازم' }}
                                                @break

                                                @default
                                            @endswitch
                                        @else
                                            {{ $result->cancellation }}
                                        @endif
                                    </span>
                                    {{-- <span>{{ app()->getLocale() == 'ar' ? 'محدودة المدى' : 'Moderate' }}</span> --}}
                                </li>
                                <li>
                                    {{-- <p>{{ customTrans('property_single.cancel_upto_prior') }}</p> --}}
                                    <p>
                                        @if (app()->getLocale() == 'ar')
                                            @switch($result->cancellation)
                                                @case('Flexible')
                                                    {{ 'استرداد كامل المبلغ قبل يوم واحد من الوصول' }}
                                                @break

                                                @case('Moderate')
                                                    {{ 'رد المبلغ كاملاً قبل خمسة أيام من الوصول' }}
                                                @break

                                                @case('Firm')
                                                    {{ 'رد كامل للمبلغ المدفوع للإلغاءات التي تتم حتى قبل 30
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                يومًا من تاريخ الوصول. إذا تم الحجز قبل أقل من 30 يومًا
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                من تسجيل
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                الوصول، فسيتم استرداد المبلغ بالكامل للإلغاءات التي تتم
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                في غضون 48 ساعة بعد إتمام الحجز وقبل 14 يومًا على الأقل
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                من تسجيل
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                الوصول. وبعد ذلك، يتم رد 50% من المبلغ المدفوع إذا تم
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                الإلغاء حتى 7 أيام قبل تسجيل الوصول. ولن يتم رد المبالغ
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                المدفوعة
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                للإلغاءات التي تتم بعد ذلك.' }}
                                                @break

                                                @case('Strict')
                                                    {{ 'استرداد كامل للإلغاءات التي تمت في غضون 48 ساعة من الحجز ، إذا كان تاريخ تسجيل الوصول بعد 14 يومًا على الأقل. استرداد 50٪ للإلغاء الذي تم إجراؤه قبل 7 أيام على الأقل من تسجيل الوصول. لا يوجد استرداد لعمليات الإلغاء خلال 7 أيام من تسجيل الوصول' }}
                                                @break

                                                @default
                                            @endswitch
                                        @else
                                            @switch($result->cancellation)
                                                @case('Flexible')
                                                    {{ 'Full refund 1 day prior to arrival' }}
                                                @break

                                                @case('Moderate')
                                                    {{ 'Full refund 5 days prior to arrival' }}
                                                @break

                                                @case('Firm')
                                                    {{ 'Full refund for cancellations up to 30 days before check-in. If booked fewer than 30 days before check-in, a full refund for cancellations made within 48 hours of booking and at least 14 days before check-in. After that, a 50% refund up to 7 days before check-in. No refund after that.' }}
                                                @break

                                                @case('Strict')
                                                    {{ 'Full refund for cancellations made within 48 hours of booking, if the check-in date is at least 14 days away. 50% refund for cancellations made at least 7 days before check-in. No refunds for cancellations made within 7 days of check-in.' }}
                                                @break

                                                @default
                                            @endswitch
                                        @endif
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            {{-- Amenities end --}}
            <div class="property-location">
                <div class="row">
                    <div class="col-6">
                        <h5 class="fw-400 mb-4">{{ customTrans('property_single.location_on_map') }}</h5>

                    </div>
                    <div class="col-6 text-right-dir mb-4">
                        <button class="theme-btn" data-bs-toggle="modal" data-bs-target="#getdirections">
                            {{ customTrans('footer.get_directions') }}
                        </button>
                    </div>
                </div>
                <div id="room-detail-map" class="single-map-w">
                    <div class="mp-loader">
                        <div class="pin"></div>
                        <div class="pulse"></div>
                    </div>
                </div>

            </div>

            {{-- review start --}}
            <div class="review-main">
                {{-- star here --}}
                {{-- @if ($reviews_avg['total_reviews'])
                    <small>{{ $reviews_avg['total_reviews'] }}</small>
                @endif --}}
                <h3 class="fw-400 mb-4">{{ customTrans('property_single.review') }}</h3>
                @if (!$reviews_avg)
                    <div class="review-count">
                        <div class="row">
                            <div class="col-md-5">
                                <div class="review-progress mb-4">
                                    <div class="prog-cont">
                                        {{ customTrans('property_single.cleanliness') }}
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0"
                                            aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="review-progress-count">
                                        0
                                    </div>
                                </div>
                                <div class="review-progress mb-4">
                                    <div class="prog-cont">
                                        {{ customTrans('property_single.communication') }}
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0"
                                            aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="review-progress-count">
                                        0
                                    </div>
                                </div>
                                <div class="review-progress mb-4">
                                    <div class="prog-cont">
                                        {{ customTrans('property_single.accuracy') }}
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0"
                                            aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="review-progress-count">
                                        0
                                    </div>
                                </div>
                                <div class="review-progress mb-4">
                                    <div class="prog-cont">
                                        {{ customTrans('property_single.location') }}
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0"
                                            aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="review-progress-count">
                                        0
                                    </div>
                                </div>
                                <div class="review-progress mb-4 d-none">
                                    <div class="prog-cont">
                                        {{ customTrans('property_single.darent_service') }}
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0"
                                            aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="review-progress-count">
                                        0
                                    </div>
                                </div>
                                <div class="review-progress mb-4 d-none">
                                    <div class="prog-cont">
                                        {{ customTrans('property_single.darent_recomended') }}
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0"
                                            aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="review-progress-count">
                                        0
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="review-count">
                        <div class="row">
                            <div class="col-md-5">
                                <div class="review-progress mb-4">
                                    <div class="prog-cont">
                                        {{ customTrans('property_single.cleanliness') }}
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar"
                                            style="width: {{ ($reviews_avg['cleanliness'] * 100) / 5 }}%"
                                            aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="review-progress-count">
                                        {{ number_format($reviews_avg['cleanliness'], $reviews_avg['cleanliness'] != intval($reviews_avg['cleanliness']) ? 1 : 0) }}
                                    </div>
                                </div>
                                <div class="review-progress mb-4">
                                    <div class="prog-cont">
                                        {{ customTrans('property_single.communication') }}
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar"
                                            style="width: {{ ($reviews_avg['communication'] * 100) / 5 }}%"
                                            aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="review-progress-count">
                                        {{ number_format($reviews_avg['communication'], $reviews_avg['communication'] != intval($reviews_avg['communication']) ? 1 : 0) }}
                                    </div>
                                </div>
                                <div class="review-progress mb-4">
                                    <div class="prog-cont">
                                        {{ customTrans('property_single.accuracy') }}
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar"
                                            style="width: {{ ($reviews_avg['accuracy'] * 100) / 5 }}%" aria-valuenow="0"
                                            aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="review-progress-count">
                                        {{ number_format($reviews_avg['accuracy'], $reviews_avg['accuracy'] != intval($reviews_avg['accuracy']) ? 1 : 0) }}
                                    </div>
                                </div>
                                <div class="review-progress mb-4">
                                    <div class="prog-cont">
                                        {{ customTrans('property_single.location') }}
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar"
                                            style="width: {{ ($reviews_avg['location'] * 100) / 5 }}%" aria-valuenow="0"
                                            aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="review-progress-count">
                                        {{ number_format($reviews_avg['location'], $reviews_avg['location'] != intval($reviews_avg['location']) ? 1 : 0) }}
                                    </div>
                                </div>
                                <div class="review-progress mb-4 d-none">
                                    <div class="prog-cont">
                                        {{ customTrans('property_single.darent_service') }}
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar"
                                            style="width: {{ ($reviews_avg['darent_service'] * 100) / 5 }}%"
                                            aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="review-progress-count">
                                        {{ number_format($reviews_avg['darent_service'], $reviews_avg['darent_service'] != intval($reviews_avg['darent_service']) ? 1 : 0) }}
                                    </div>
                                </div>
                                <div class="review-progress mb-4 d-none">
                                    <div class="prog-cont">
                                        {{ customTrans('property_single.darent_recomended') }}
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar"
                                            style="width: {{ ($reviews_avg['darent_recomended'] * 100) / 5 }}%"
                                            aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="review-progress-count">
                                        {{ number_format($reviews_avg['darent_recomended'], $reviews_avg['darent_recomended'] != intval($reviews_avg['darent_recomended']) ? 1 : 0) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
                <div class="row">
                    @forelse ($reviews->take(5) as $review)
                        <div class="col-md-4 col-12">
                            <div class="dummy ">
                                <div class="row">
                                    <div class="col-md-2 col-2 revlsit">
                                        <div class="pr-img review-user">
                                            <img src="{{ '/' . $review['reviewer']['profile_image'] }}">
                                        </div>
                                    </div>
                                    <div class="col-md-10 col-10">
                                        <div class="review-content">
                                            <div class="ls-content rv-content">
                                                <div class="text-by">
                                                    <div class="mini-profile reviews">
                                                        <div class="pr-mini-detail">
                                                            <h4>{{ $review['reviewer']['name'] }}</h4>
                                                            @php
                                                                $maxStars = 5;
                                                                $rating = (float) $review['rating'];
                                                                $formattedDate = Carbon::parse(
                                                                    $review['review_date'],
                                                                )->format('m/d/Y');
                                                            @endphp
                                                            <h6 class="rev-date">{{ $formattedDate }}</h6>
                                                            <div class="raitng-start">
                                                                {{-- Render full stars --}}
                                                                @for ($i = 1; $i <= floor($rating); $i++)
                                                                    <i class="ri-star-fill"></i>
                                                                @endfor
                                                                @if ($rating - floor($rating) >= 0.01)
                                                                    <i class="ri-star-half-line"></i>
                                                                @endif
                                                                {{-- Render empty stars --}}
                                                                @for ($i = ceil($rating); $i < $maxStars; $i++)
                                                                    <i class="ri-star-line"></i>
                                                                @endfor
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {{-- <p class="product-rate text-success mb-0"><img
                                                        src="{{ asset('icons/green-rate.svg') }}" alt="">
                                                    {{ number_format($reviews->rating, $reviews->rating != intval($reviews->rating) ? 1 : 0) ?? 0 }}/<span>5</span>
                                                </p> --}}
                                                <p class="fs-18 fc-gray mb-2 limit-para ellipsis-fourline">
                                                    {{ $review['message'] }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <p>{{ customTrans('property_single.no_rev_yet') }}</p>
                    @endforelse
                    <button class="grey-btn show-all-review-btn" data-bs-toggle="modal" data-bs-target="#reviews-md"
                        data-id="">
                        {{ customTrans('property_single.show_all_reviews') }}
                    </button>
                    @if ($reviews_avg['total_reviews'] > 4)
                    @endif
                </div>
            </div>
            {{-- review end --}}


        </div>
    </div>
    {{-- Map Alert --}}
    @if (Route::currentRouteName() == 'property.single')
        <div class="modal fade dubai-ff modal-dr-bottom" id="getdirections" data-bs-backdrop="static"
            data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
            <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
                <div class="modal-content cm-simple-content">
                    <div class="modal-header cm-simple-header">
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        </button>
                    </div>
                    <div class="modal-body cm-simple-body">
                        <div class="alert-modal">
                            <img src="{{ asset('icons/info-alert.svg') }}" alt="" class="mb-3">
                            <h3 class="  mb-4 fw-600 ">{{ customTrans('footer.alert') }}</h3>
                            <p class="mb-4">{{ customTrans('footer.approximate_location') }}</p>
                            <a id="mapShow"
                                href="https://www.google.com/maps?q={{ $nearbyCoordinates['latitude'] }},{{ $nearbyCoordinates['longitude'] }}"
                                target="_blank">
                                <button class="theme-btn w-100" data-bs-dismiss="modal"
                                    aria-label="Close">{{ customTrans('host_reservation.confirm') }}</button>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
    <section class="services">
        <div class="container">
            <h4 class="fw-400 mt-5 mb-5">{{ customTrans('property_single.similar_listing') }}</h4>
            <div class="property">
                <div class="row">
                    @forelse ($similar as $property)
                        <div class="col-xl-3 col-lg-4 col-sm-4 col-xs-12">
                            {{-- <a href="Javascript:;" aria-label="{{ $property->name }}"> --}}
                            <div class="product">

                                <div class="image">
                                    @if ($property->property_discount)
                                        <div class="discount-tag">
                                            <span>{{ $property->property_discount->discount }}</span>%
                                        </div>
                                    @endif
                                    <div class="views">
                                        <span><i class="ri-eye-fill"></i></span>
                                        <span>{{ App\Http\Helpers\Common::formatProductViewCount((int) $property->userPropertyView->count() + 100) }}</span>
                                    </div>
                                    <div class="loadskull view-img">
                                        {{-- <div class="swiper host-slider mb-3">
                                            <div class="swiper-wrapper">

                                                @php $filteredPhotos = $property->property_photos->where('cover_photo', '!=', 1)->take(5); @endphp
                                                @foreach ($filteredPhotos as $filterphoto)
                                                    <div class="swiper-slide">
                                                        <a href="{{ route('property.single', ['slug' => $property->slug]) }}"
                                                            aria-label="{{ $property->name }}" class="">
                                                            <img src="{{ asset('images/loader.gif') }}" height="auto"
                                                                width="auto" class="product-img lazy"
                                                                data-src="{{ asset($filterphoto->photo) }}"
                                                                loading="lazy" alt="{{ $property->name }}">

                                                        </a>
                                                    </div>
                                                @endforeach

                                            </div>
                                            <div class="swiper-pagination"></div>
                                            <div class="swiper-button-hp-prev"></div>
                                            <div class="swiper-button-hp-next"></div>
                                        </div> --}}

                                        <swiper-container class="host-slider " pagination="true"
                                            pagination-dynamic-bullets="true" navigation="true">
                                            <swiper-slide>
                                                <a href="{{ route('property.single', ['slug' => $property->slug]) }}"
                                                    aria-label="{{ $property->name }}" class="">
                                                    <img src="{{ asset('images/loader.gif') }}" height="auto"
                                                        width="auto" class="product-img lazy"
                                                        data-src="{{ asset($property->cover_photo) }}" loading="lazy"
                                                        alt="{{ $property->name }}">
                                                </a>
                                            </swiper-slide>

                                            @php $filteredPhotos = $property->property_photos->where('cover_photo', '!=', 1)->take(5); @endphp
                                            @foreach ($filteredPhotos as $filterphoto)
                                                <swiper-slide>
                                                    <a href="{{ route('property.single', ['slug' => $property->slug]) }}"
                                                        aria-label="{{ $property->name }}" class="">
                                                        <img src="{{ asset('images/loader.gif') }}" height="auto"
                                                            width="auto" class="product-img lazy"
                                                            data-src="{{ asset($filterphoto->photo) }}" loading="lazy"
                                                            alt="{{ $property->name }}">
                                                    </a>
                                                </swiper-slide>
                                            @endforeach
                                        </swiper-container>
                                    </div>
                                    <div class="fav-icon " data-bs-toggle="modal"
                                        @auth
@if ($property->wishlist == true) id="toggleWishlist"
                                         @else
                                             data-bs-target="{{ isset($wishlistExist) ? '#whishlist-listing' : '#create-whishlist' }}"
                                         @endif @endauth
                                        @guest
id="guestWishlist" @endguest data-item-id="{{ $property->id }}">
                                        <div class="fav-in {{ $property->wishlist == true ? 'active' : '' }}  heart_icon"
                                            data-status="{{ $property->wishlist }}" data-id="{{ $property->id }}">
                                            <img src="{{ asset('icons/Path 125.svg') }}" height="auto"
                                                width="auto" alt="heart" class="fav-blank">
                                            <img src="{{ asset('icons/fill-heart.svg') }}" height="auto"
                                                width="auto" alt="heart" class="fav-fill">
                                        </div>
                                    </div>
                                </div>
                                <div class="product-detail">
                                    <a href="{{ route('property.single', ['slug' => $property->slug]) }}"
                                        aria-label="{{ $property->name }}" class="float-anc"></a>
                                    <div class="title">
                                        <a href="{{ route('property.single', ['slug' => $property->slug]) }}"
                                            aria-label="{{ $property->name }}">
                                            <h4 class="loadskull">
                                                {{ propertyTitle($property->id, $property->propertyType) }}
                                            </h4>
                                        </a>
                                        <p class="product-rate loadskull"><img src="{{ asset('icons/rate-2.svg') }}"
                                                height="auto" width="auto" alt="">
                                            @if ($property->guest_review)
                                                {{ digitsToArabic(number_format($property->rating_avg)) }}/
                                                <span>{{ digitsToArabic('5') }}</span>
                                                ({{ digitsToArabic($property->reviews_count) }})
                                            @else
                                                {{ digitsToArabic('0') }}/<span>{{ digitsToArabic('5') }}</span>
                                                ({{ digitsToArabic('0') }})
                                            @endif
                                        </p>
                                    </div>

                                    {{-- getDescription in helpers.php --}}
                                    @php $description = getDescription($property->property_description); @endphp
                                    <p class="product-content loadskull">
                                        {{-- shortString in helpers.php --}}
                                        {{ shortString($description, 70) }}
                                        @if (strlen($description) > 70)
                                            <span class="rd-more">{{ customTrans('property_single.read_more') }}</span>
                                        @endif
                                    </p>

                                    {{--                                    @if ($property->property_discount) --}}
                                    {{--                                        <p class="product-price loadskull"> <span --}}
                                    {{--                                                class="discount-cut">{{ digitsToArabic($property->property_price->price ?? '') . ' ' . trans('messages.utility.sar') }}</span> --}}
                                    {{--                                            {{ digitsToArabic($property->discountedAmount) . ' ' . trans('messages.utility.sar') . ' ' . trans('messages.property_single.per_night') }} --}}
                                    {{--                                        </p> --}}
                                    {{--                                        --}}{{-- <p class="unit-code loadskull">{{ customTrans('home.unit_code') }} --}}
                                    {{--                                            (<span>{{ $property->property_code }}</span>)</p> --}}
                                    {{--                                    @else --}}
                                    <div class="pr-code">
                                        <p class="product-price loadskull">
                                            @if ($property->before_discount > $property->total_price)
                                                <style>
                                                    .old-price {
                                                        text-decoration: line-through;
                                                        font-size: .9em;
                                                    }
                                                </style>
                                                <span class="old-price">
                                                    {{ number_format($property->before_discount / $property->number_of_days, 2, '.', '') }}
                                                </span>
                                            @endif
                                            {{ number_format($property->total_price / $property->number_of_days, 2, '.', '') }}
                                            <span class="sar-pr">{{ customTrans('utility.sar') }}</span>/ Night
                                        </p>
                                        {{-- <p class="unit-code loadskull">{{ customTrans('home.unit_code') }}
                                            (<span>{{ $property->property_code }}</span>)</p> --}}
                                    </div>
                                    {{--                                    @endif --}}
                                </div>

                            </div>
                            {{-- </a> --}}
                        </div>
                    @empty
                    @endforelse
                </div>
            </div>
        </div>
        {{-- Wishlits Listing Modal --}}
        <div class="modal fade dubai-ff modal-dr-bottom" id="whishlist-listing" data-bs-backdrop="static"
            data-bs-keyboard="false" tabindex="-1" aria-labelledby="whishlist-listing" aria-hidden="true">
            <div class="modal-dialog custom-modal-dialog modal-dialog-centered modal-dialog-scrollable bt-grey-scroller">
                <div class="modal-content cm-bd-content">
                    <div class="modal-header cm-bd-header">
                        <h5 class="w-100 text-center mb-0" id="whishlist-listing">
                            {{ customTrans('wishlist.your_wishlists') }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        </button>
                    </div>
                    <div class="modal-body cm-bd-body">
                        <form>
                            {{ csrf_field() }}
                            <input type="hidden" name="property_id" id="wishlist-property-id" value="">
                            {{-- {{dd($wishlistExist)}} --}}
                            @foreach ($wishlistExist as $wishlist)
                                <div class="wishlist-listing">
                                    @php
                                        $wishlistProperties = $wishlist->wishlistProperties;
                                    @endphp
                                    @if (count($wishlistProperties) > 0)
                                        <div class="wlisting-inner" id="existWishlist"
                                            data-wishlist-name-id="{{ $wishlist->id }}">
                                            <img src="{{ asset($wishlistProperties[0]->property->getCoverPhotoAttribute()) }}"
                                                alt="">
                                            <h5 class="mb-0 ellipsis-oneline">{{ $wishlist->name }}</h5>
                                        </div>
                                    @else
                                        <div class="wlisting-inner" id="existWishlist"
                                            data-wishlist-name-id="{{ $wishlist->id }}">
                                            <img src="{{ asset('images/default-image-not-exist.png') }}"
                                                alt="">
                                            <h5 class="mb-0 ellipsis-oneline">{{ $wishlist->name }}</h5>
                                        </div>
                                    @endif
                                </div>
                            @endforeach

                            <div class="modal-footer cm-bd-footer">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                                    data-bs-target="#create-whishlist">{{ customTrans('wishlist.create_new_wishlist') }}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {{-- Wishlits Listing Modal End --}}
    </section>
    {{-- {{dd(auth()->check() ? auth()->user()->id : 'not login')}} --}}
    <input type="hidden" name="" id="userlogin" value="{{ auth()->check() ? auth()->user()->id : '' }}">

    <div class="modal fade modal-dr-bottom" id="openapp" data-bs-backdrop="static" data-bs-keyboard="false"
        tabindex="-1" aria-labelledby="cancelLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog custom-small-modal-width modal-dialog-centered w-100">
            <div class="modal-content custom-modal-content">

                <div class="modal-body">
                    <div class="select-op">
                        <div class="row mb-3">
                            <div class="col-7">
                                <div class="app-op">
                                    <img src="{{ asset('images/sh-darnt-logo.png') }}" alt=""
                                        class="openapp-dlogo">
                                    <h3>{{ customTrans('property_single.darent_app') }}</h3>
                                </div>
                            </div>

                            <div class="col-5">
                                <a href="javascript:void(0)" id="openAppBtn">
                                    <button class="theme-btn w-100">
                                        {{ customTrans('property_single.open') }}
                                    </button>
                                </a>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-7">
                                <div class="app-op">
                                    <img src="{{ asset('images/safari-logo.png') }}" alt="">
                                    <h3>{{ customTrans('property_single.browser') }}</h3>
                                </div>
                            </div>

                            <div class="col-5">
                                <a href="javascript:void(0)">
                                    <button class="theme-btn w-100" id="continue_with_browser_button">
                                        {{ customTrans('property_single.continue') }}
                                    </button>
                                </a>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>




    {{-- @if (strtolower(env('APP_ENV')) == 'local')
        <div class="modal fade dubai-ff modal-dr-bottom" id="md-reviews" data-bs-backdrop="static" data-bs-keyboard="false"
            tabindex="-1" aria-labelledby="md-reviews" aria-hidden="true">
            <div class="modal-dialog custom-modal-dialog modal-dialog-centered modal-dialog-scrollable">
                <div class="modal-content cm-bd-content bt-grey-scroller ">
                    <div class="modal-header cm-bd-header">
                        <button type="button" class="btn-close"
                            data-bs-dismiss="modal" aria-label="Close">
                        </button>
                        <h5 class="w-100 text-center mb-0" id="md-all-review">Review</h5>
                    </div>
                    <div class="modal-body cm-bd-body">
                        <div class="show-review-main">
                            <div class="row">
                                <div class="col-lg-4">
                                    <div class="show-review-sidebar">
                                        <h1 class="sr-sidebar-title">
                                            <i class="fas fa-star"></i>
                                            4.60
                                        </h1>
                                        <div class="swiper sr-sidebar-slider">
                                            <ul class="sr-sidebar-rate swiper-wrapper">
                                                <li class="swiper-slide">
                                                    <div class="sr-sidebar-progress">
                                                        <h6 class="mb-2">Overall rating</h6>
                                                        <div class="review-progress">
                                                            <p class="mb-0">5</p>
                                                            <div class="progress">
                                                                <div class="progress-bar" role="progressbar"
                                                                    style="width: 90%" aria-valuenow="0"
                                                                    aria-valuemin="0" aria-valuemax="100">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="review-progress">
                                                            <p class="mb-0">4</p>
                                                            <div class="progress">
                                                                <div class="progress-bar" role="progressbar"
                                                                    style="width: 70%" aria-valuenow="0"
                                                                    aria-valuemin="0" aria-valuemax="100">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="review-progress">
                                                            <p class="mb-0">3</p>
                                                            <div class="progress">
                                                                <div class="progress-bar" role="progressbar"
                                                                    style="width: 50%" aria-valuenow="0"
                                                                    aria-valuemin="0" aria-valuemax="100">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="review-progress">
                                                            <p class="mb-0">2</p>
                                                            <div class="progress">
                                                                <div class="progress-bar" role="progressbar"
                                                                    style="width: 20%" aria-valuenow="0"
                                                                    aria-valuemin="0" aria-valuemax="100">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="review-progress">
                                                            <p class="mb-0">1</p>
                                                            <div class="progress">
                                                                <div class="progress-bar" role="progressbar"
                                                                    style="width: 10%" aria-valuenow="0"
                                                                    aria-valuemin="0" aria-valuemax="100">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li class="swiper-slide">
                                                    <div class="sr-sidebar-rate-inner df-align-center">
                                                        <div class="sr-sr-inner-left">
                                                            <i class="ri-hand-coin-line"></i>
                                                        </div>
                                                        <div class="sr-sr-inner-right df-align-center">
                                                            <p class="mb-0">Cleanliness</p>
                                                            <span class="mb-0">4.4</span>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li class="swiper-slide">
                                                    <div class="sr-sidebar-rate-inner df-align-center">
                                                        <div class="sr-sr-inner-left">
                                                            <i class="ri-checkbox-circle-line"></i>
                                                        </div>
                                                        <div class="sr-sr-inner-right df-align-center">
                                                            <p class="mb-0">Accuracy</p>
                                                            <span class="mb-0">4.4</span>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li class="swiper-slide">
                                                    <div class="sr-sidebar-rate-inner df-align-center">
                                                        <div class="sr-sr-inner-left ">
                                                            <i class="ri-key-2-line"></i>
                                                        </div>
                                                        <div class="sr-sr-inner-right df-align-center">
                                                            <p class="mb-0">Check-in</p>
                                                            <span class="mb-0">4.4</span>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li class="swiper-slide">
                                                    <div class="sr-sidebar-rate-inner df-align-center">
                                                        <div class="sr-sr-inner-left">
                                                            <i class="ri-chat-3-line"></i>
                                                        </div>
                                                        <div class="sr-sr-inner-right df-align-center">
                                                            <p class="mb-0">Communication</p>
                                                            <span class="mb-0">4.4</span>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li class="swiper-slide">
                                                    <div class="sr-sidebar-rate-inner df-align-center">
                                                        <div class="sr-sr-inner-left">
                                                            <i class="ri-map-2-line"></i>
                                                        </div>
                                                        <div class="sr-sr-inner-right df-align-center">
                                                            <p class="mb-0">Location</p>
                                                            <span class="mb-0">4.4</span>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li class="swiper-slide">
                                                    <div class="sr-sidebar-rate-inner df-align-center">
                                                        <div class="sr-sr-inner-left">
                                                            <i class="ri-price-tag-3-line"></i>
                                                        </div>
                                                        <div class="sr-sr-inner-right df-align-center">
                                                            <p class="mb-0">Value</p>
                                                            <span class="mb-0">4.4</span>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-8">
                                    <div class="show-review-content">
                                        <div class="sr-content-head">
                                            <div class="df-align-center justify-content-between">
                                                <h3 class="mb-0"><span>2</span> Reviews</h3>
                                                <select class="form-select form-control" aria-label="Default select example">
                                                    <option value="most_recent" selected>Most recent</option>
                                                    <option value="highest_rated">Highest rated</option>
                                                </select>
                                            </div>
                                            <div class="sr-content-search">
                                                <input type="text" name="" id="" class="form-control" placeholder="Search reviews">
                                                <i class="ri-search-line"></i>
                                            </div>
                                        </div>
                                        <div class="sr-content-body reviews mb-4">
                                            <div class="sr-comment">
                                                <div class="sr-comment-user">
                                                    <div class="sr-profile d-flex align-items-end">
                                                        <div class="sr-profile-img">
                                                            <img src="{{ asset('icons/user.svg') }}">
                                                        </div>
                                                        <div class="sr-profile-name">
                                                            <h6 class="4 mb-0 ellipsis-oneline">Jennifer</h6>
                                                            <p class="mb-0">{{ customTrans('property_single.joined_in') }}
                                                                October 2022</p>
                                                        </div>
                                                    </div>
                                                    <div class="sr-profile-comment mt-2">
                                                        <div class="sr-pc-star-rate">
                                                            <div class="star-rating">
                                                                <input id="star-5" type="radio" name="rating" value="5" disabled/>
                                                                <label for="star-5" title="5 stars">
                                                                    <i class="active ri-star-fill"></i>
                                                                </label>
                                                                <input id="star-4" type="radio" name="rating" value="4" disabled />
                                                                <label for="star-4" title="4 stars">
                                                                    <i class="active ri-star-fill"></i>
                                                                </label>
                                                                <input id="star-3" type="radio" name="rating" value="3" disabled checked/>
                                                                <label for="star-3" title="3 stars">
                                                                    <i class="active ri-star-fill"></i>
                                                                </label>
                                                                <input id="star-2" type="radio" name="rating" value="2" disabled/>
                                                                <label for="star-2" title="2 stars">
                                                                    <i class="active ri-star-fill"></i>
                                                                </label>
                                                                <input id="star-1" type="radio" name="rating" value="1" disabled/>
                                                                <label for="star-1" title="1 star">
                                                                    <i class="active ri-star-fill"></i>
                                                                </label>
                                                            </div>
                                                            <div class="sr-cb-time">
                                                                <p class="mb-0">
                                                                    <span>2</span>
                                                                    <span>Weeks ago</span>
                                                                </p>
                                                            </div>
                                                        </div>
                                                        <p class="sr-pc-content dark-grey mb-2">Lorem ipsum dolor sit amet,
                                                            consectetur adipisicing elit. Corrupti eveniet aliquam
                                                            repellendus cum repudiandae id hic tenetur consequatur
                                                            dolor ullam. Dolore, incidunt! Temporibus consequuntur
                                                            recusandae delectus laboriosam est ullam fuga.
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="sr-comment-host">
                                                    <div class="sr-profile d-flex align-items-end">
                                                        <div class="sr-profile-img">
                                                            <img src="{{ asset('icons/user.svg') }}">
                                                        </div>
                                                        <div class="sr-profile-name">
                                                            <h6 class="4 mb-0 ellipsis-oneline">Jennifer</h6>
                                                            <p class="mb-0">{{ customTrans('property_single.joined_in') }}
                                                                October 2022</p>
                                                        </div>
                                                    </div>
                                                    <div class="sr-profile-comment mt-2">
                                                        <p class="sr-pc-content dark-grey mb-0">Lorem ipsum dolor sit amet,
                                                            consectetur adipisicing elit
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="sr-comment">
                                                <div class="sr-comment-user">
                                                    <div class="sr-profile d-flex align-items-end">
                                                        <div class="sr-profile-img">
                                                            <img src="{{ asset('icons/user.svg') }}">
                                                        </div>
                                                        <div class="sr-profile-name">
                                                            <h6 class="4 mb-0 ellipsis-oneline">Jennifer</h6>
                                                            <p class="mb-0">{{ customTrans('property_single.joined_in') }}
                                                                October 2022</p>
                                                        </div>
                                                    </div>
                                                    <div class="sr-profile-comment mt-2">
                                                        <div class="sr-pc-star-rate">
                                                            <div class="star-rating">
                                                                <input id="star-5" type="radio" name="rating-2" value="5" disabled/>
                                                                <label for="star-5" title="5 stars">
                                                                    <i class="active ri-star-fill"></i>
                                                                </label>
                                                                <input id="star-4" type="radio" name="rating-2" value="4" disabled checked/>
                                                                <label for="star-4" title="4 stars">
                                                                    <i class="active ri-star-fill"></i>
                                                                </label>
                                                                <input id="star-3" type="radio" name="rating-2" value="3" disabled />
                                                                <label for="star-3" title="3 stars">
                                                                    <i class="active ri-star-fill"></i>
                                                                </label>
                                                                <input id="star-2" type="radio" name="rating-2" value="2" disabled/>
                                                                <label for="star-2" title="2 stars">
                                                                    <i class="active ri-star-fill"></i>
                                                                </label>
                                                                <input id="star-1" type="radio" name="rating-2" value="1" disabled/>
                                                                <label for="star-1" title="1 star">
                                                                    <i class="active ri-star-fill"></i>
                                                                </label>
                                                            </div>
                                                            <div class="sr-cb-time">
                                                                <p class="mb-0">
                                                                    <span>2</span>
                                                                    <span>Weeks ago</span>
                                                                </p>
                                                            </div>
                                                        </div>
                                                        <p class="sr-pc-content dark-grey mb-2">Lorem ipsum dolor sit amet,
                                                            consectetur adipisicing elit. Corrupti eveniet aliquam
                                                            repellendus cum repudiandae id hic tenetur consequatur
                                                            dolor ullam. Dolore, incidunt! Temporibus consequuntur
                                                            recusandae delectus laboriosam est ullam fuga.
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="sr-comment-host">
                                                    <div class="sr-profile d-flex align-items-end">
                                                        <div class="sr-profile-img">
                                                            <img src="{{ asset('icons/user.svg') }}">
                                                        </div>
                                                        <div class="sr-profile-name">
                                                            <h6 class="4 mb-0 ellipsis-oneline">Jennifer</h6>
                                                            <p class="mb-0">{{ customTrans('property_single.joined_in') }}
                                                                October 2022</p>
                                                        </div>
                                                    </div>
                                                    <div class="sr-profile-comment mt-2">
                                                        <p class="sr-pc-content dark-grey mb-0">Lorem ipsum dolor sit amet,
                                                            consectetur adipisicing elit
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @else --}}
    <!-- Error  Modal -->
    <div class="modal fade" id="error" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="cancelLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog custom-small-modal-width modal-dialog-centered">
            <div class="modal-content custom-modal-content">
                <div class="modal-header  custom-small-modal-header">

                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert-modal">
                        <img src="{{ asset('icons/cancel.svg') }}" alt="" class="mb-3">
                        <h3 class="text-danger mb-4 fw-600 ">{{ customTrans('property_single.error') }}</h3>
                        <p class="mb-4">{{ customTrans('property_single.please_fill_all_field') }}</p>

                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- new review Modal --}}
    <div class="modal fade modal-dr-bottom" id="reviews-md" data-bs-backdrop="static" data-bs-keyboard="false"
        tabindex="-1" aria-labelledby="reviewsmdLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content custom-modal-content">
                <div class="modal-header  custom-small-modal-header">

                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body  for-mb-rev">
                    <div class="review-md">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="review-count-sc">
                                    <div class="total-review">
                                        <div class="rv-img">
                                            <img src="{{ asset('icons/review-lft.svg') }}" alt=""
                                                class="lf-rv-img-en">
                                            <img src="{{ asset('icons/review-rgt.svg') }}" alt=""
                                                class="lf-rv-image-ar">
                                        </div>
                                        <div class="rv-cnt" dir="ltr">
                                        {{ $result->average_rating ?? 0 }}
                                            <!-- {{ ($reviews_avg['cleanliness'] + $reviews_avg['darent_service'] + $reviews_avg['darent_recomended'] + $reviews_avg['accuracy'] + $reviews_avg['communication']) / 5 ?? 0 }} -->
                                        </div>
                                        <div class="rv-img">
                                            <img src="{{ asset('icons/review-rgt.svg') }}" alt=""
                                                class="rt-rv-img-en">
                                            <img src="{{ asset('icons/review-lft.svg') }}" alt=""
                                                class="rt-rv-img-ar">
                                        </div>

                                    </div>

                                    <div class="revv-cont">
                                        <h4>{{ customTrans('withdraw.guest_favourite') }}</h4>
                                        <p>{{ customTrans('withdraw.guest_favourite_intro') }}</p>
                                    </div>
                                    <hr class="mb-3">
                                    <div class="revv-progress">
                                        <h6>{{ customTrans('withdraw.overall_rating') }}</h6>
                                        <div class="revv-pr">
                                            <span class="rv-num">5</span>
                                            <span class="rv-pr-bar">
                                                <div class="progress">
                                                    <div class="progress-bar" role="progressbar"
                                                        style="width: {{ $review_counts->total_reviews == 0 ? 0 : ($review_counts->five_star / $review_counts->total_reviews) * 100 }}%"
                                                        aria-valuenow="{{ $review_counts->total_reviews == 0 ? 0 : ($review_counts->five_star / $review_counts->total_reviews) * 100 }}"
                                                        aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </span>
                                        </div>
                                        <div class="revv-pr">
                                            <span class="rv-num">4</span>
                                            <span class="rv-pr-bar">
                                                <div class="progress">
                                                    <div class="progress-bar" role="progressbar"
                                                        style="width: {{ $review_counts->total_reviews == 0 ? 0 : ($review_counts->four_star / $review_counts->total_reviews) * 100 }}%"
                                                        aria-valuenow="{{ $review_counts->total_reviews == 0 ? 0 : ($review_counts->four_star / $review_counts->total_reviews) * 100 }}"
                                                        aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </span>
                                        </div>
                                        <div class="revv-pr">
                                            <span class="rv-num">3</span>
                                            <span class="rv-pr-bar">
                                                <div class="progress">
                                                    <div class="progress-bar" role="progressbar"
                                                        style="width: {{ $review_counts->total_reviews == 0 ? 0 : ($review_counts->three_star / $review_counts->total_reviews) * 100 }}%"
                                                        aria-valuenow="{{ $review_counts->total_reviews == 0 ? 0 : ($review_counts->three_star / $review_counts->total_reviews) * 100 }}"
                                                        aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </span>
                                        </div>
                                        <div class="revv-pr">
                                            <span class="rv-num">2</span>
                                            <span class="rv-pr-bar">
                                                <div class="progress">
                                                    <div class="progress-bar" role="progressbar"
                                                        style="width: {{ $review_counts->total_reviews == 0 ? 0 : ($review_counts->two_star / $review_counts->total_reviews) * 100 }}%"
                                                        aria-valuenow="{{ $review_counts->total_reviews == 0 ? 0 : ($review_counts->two_star / $review_counts->total_reviews) * 100 }}"
                                                        aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </span>
                                        </div>
                                        <div class="revv-pr">
                                            <span class="rv-num">1</span>
                                            <span class="rv-pr-bar">
                                                <div class="progress">
                                                    <div class="progress-bar" role="progressbar"
                                                        style="width: {{ $review_counts->total_reviews == 0 ? 0 : ($review_counts->one_star / $review_counts->total_reviews) * 100 }}%"
                                                        aria-valuenow="{{ $review_counts->total_reviews == 0 ? 0 : ($review_counts->one_star / $review_counts->total_reviews) * 100 }}"
                                                        aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </span>
                                        </div>
                                    </div>
                                    <hr class="mb-3 mt-3">
                                    <div class="rev-points">
                                        @if (!$reviews_avg)
                                            <div class="ament-rev">
                                                <ul>
                                                    <li>
                                                        <div class="amn-rv-left">
                                                            <span class="amn-img">
                                                                <img src="{{ asset('icons/clean.svg') }}"
                                                                    alt="">
                                                            </span>
                                                            <span class="amn-cont">
                                                                {{ customTrans('property_single.cleanliness') }}
                                                            </span>
                                                        </div>
                                                        <div class="amn-count">
                                                            0
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="amn-rv-left">
                                                            <span class="amn-img">
                                                                <img src="{{ asset('icons/communication.svg') }}"
                                                                    alt="">
                                                            </span>
                                                            <span class="amn-cont">
                                                                {{ customTrans('property_single.communication') }}
                                                            </span>
                                                        </div>
                                                        <div class="amn-count">
                                                            0
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="amn-rv-left">
                                                            <span class="amn-img">
                                                                <img src="{{ asset('icons/accuracy.svg') }}"
                                                                    alt="">
                                                            </span>
                                                            <span class="amn-cont">
                                                                {{ customTrans('property_single.accuracy') }}
                                                            </span>
                                                        </div>
                                                        <div class="amn-count">
                                                            0
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="amn-rv-left">
                                                            <span class="amn-img">
                                                                <img src="{{ asset('icons/location.svg') }}"
                                                                     alt="">
                                                            </span>
                                                            <span class="amn-cont">
                                                                Location
                                                            </span>
                                                        </div>
                                                        <div class="amn-count">
                                                            0
                                                        </div>
                                                    </li>
                                                    <li class="d-none">
                                                        <div class="amn-rv-left">
                                                            <span class="amn-img">
                                                                <img src="{{ asset('images/sh-darnt-logo.png') }}"
                                                                    alt="">
                                                            </span>
                                                            <span class="amn-cont">
                                                                {{ customTrans('property_single.darent_service') }}
                                                            </span>
                                                        </div>
                                                        <div class="amn-count">
                                                            0
                                                        </div>
                                                    </li>
                                                    <li class="d-none">
                                                        <div class="amn-rv-left">
                                                            <span class="amn-img">
                                                                <img src="{{ asset('icons/recommended.png') }}"
                                                                    alt="">
                                                            </span>
                                                            <span class="amn-cont">
                                                                {{ customTrans('property_single.darent_recomended') }}
                                                            </span>
                                                        </div>
                                                        <div class="amn-count">
                                                            0
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        @else
                                            <div class="ament-rev">
                                                <ul>
                                                    <li>
                                                        <div class="amn-rv-left">
                                                            <span class="amn-img">
                                                                <img src="{{ asset('icons/clean.svg') }}"
                                                                    alt="">
                                                            </span>
                                                            <span class="amn-cont">
                                                                {{ customTrans('property_single.cleanliness') }}
                                                            </span>
                                                        </div>
                                                        <div class="amn-count">
                                                            {{ number_format($reviews_avg['cleanliness'], $reviews_avg['cleanliness'] != intval($reviews_avg['cleanliness']) ? 1 : 0) }}
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="amn-rv-left">
                                                            <span class="amn-img">
                                                                <img src="{{ asset('icons/communication.svg') }}"
                                                                    alt="">
                                                            </span>
                                                            <span class="amn-cont">
                                                                {{ customTrans('property_single.communication') }}
                                                            </span>
                                                        </div>
                                                        <div class="amn-count">
                                                            {{ number_format($reviews_avg['communication'], $reviews_avg['communication'] != intval($reviews_avg['communication']) ? 1 : 0) }}
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="amn-rv-left">
                                                            <span class="amn-img">
                                                                <img src="{{ asset('icons/accuracy.svg') }}"
                                                                    alt="">
                                                            </span>
                                                            <span class="amn-cont">
                                                                {{ customTrans('property_single.accuracy') }}
                                                            </span>
                                                        </div>
                                                        <div class="amn-count">
                                                            {{ number_format($reviews_avg['accuracy'], $reviews_avg['accuracy'] != intval($reviews_avg['accuracy']) ? 1 : 0) }}
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="amn-rv-left">
                                                            <span class="amn-img">
                                                                <img src="{{ asset('icons/location.svg') }}"
                                                                    alt="">
                                                            </span>
                                                            <span class="amn-cont">
                                                                Location
                                                            </span>
                                                        </div>
                                                        <div class="amn-count">
                                                            {{ number_format($reviews_avg['location'], $reviews_avg['location'] != intval($reviews_avg['location']) ? 1 : 0) }}
                                                        </div>
                                                    </li>
                                                    <li class="d-none">
                                                        <div class="amn-rv-left">
                                                            <span class="amn-img">
                                                                <img src="{{ asset('images/sh-darnt-logo.png') }}"
                                                                    alt="">
                                                            </span>
                                                            <span class="amn-cont">
                                                                {{ customTrans('property_single.darent_service') }}
                                                            </span>
                                                        </div>
                                                        <div class="amn-count">
                                                            {{ number_format($reviews_avg['darent_service'], $reviews_avg['darent_service'] != intval($reviews_avg['darent_service']) ? 1 : 0) }}
                                                        </div>
                                                    </li>
                                                    <li class="d-none">
                                                        <div class="amn-rv-left">
                                                            <span class="amn-img">
                                                                <img src="{{ asset('icons/recommended.png') }}"
                                                                    alt="">
                                                            </span>
                                                            <span class="amn-cont">
                                                                {{ customTrans('property_single.darent_recomended') }}
                                                            </span>
                                                        </div>
                                                        <div class="amn-count">
                                                            {{ number_format($reviews_avg['darent_recomended'], $reviews_avg['darent_recomended'] != intval($reviews_avg['darent_recomended']) ? 1 : 0) }}
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="review-view-sc">
                                    <div class="rev-head">
                                        <div class="row">
                                            <div class="col-md-9">
                                                <h3 class="mb-0"><span>{{ $reviews_avg['total_reviews'] }}</span>
                                                    {{ customTrans('sidenav.reviews') }}
                                                </h3>
                                            </div>
                                            <div class="col-md-3">
                                                <select name="" id="filter" class="rev-slt">
                                                    <option value="recent">Most recent</option>
                                                    <option value="rated-desc">Highest rated</option>
                                                    <option value="rated-asc">Lowest rated</option>

                                                </select>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="rev-search">
                                                    <i class="ri-search-line"></i>
                                                    <input type="Search" name="search" id="search"
                                                        class="form-control">
                                                    <a href="javascript:" id="clear"><i
                                                            class="ri-close-fill"></i></a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="rev-views-ppl" id="review-display">
                                    </div>
                                    <span id="loader_spinner"
                                        class="testClass d-none">{{ customTrans('withdraw.loading') }}...</span>
                                    @if (count($reviews) > 5)
                                        <div>
                                            <a href="javacript:;"
                                                id="loadMore">{{ customTrans('utility.load_more') }}</a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- new review modal end --}}
    {{-- @endif --}}
@stop
@push('css')
    <!-- slick slider -->
    <link rel="stylesheet" type="text/css" href="{{ asset('cdns/css/slick.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('cdns/css/slick-theme.min.css') }}">
    <!-- slick slider end-->
@endpush
@push('scripts')
    <script type="text/javascript" src="{{ asset('js/sweetalert.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/locationpicker.jquery.min.js') }}"></script>
    <script src="{{ asset('js/fancy-box.js') }}"></script>

    <script type="text/javascript">
        let currentUrlRefer = window.location.href;
        let newBaseUrl = "https://static.darent.com";
        let urlObj = new URL(currentUrlRefer);
        let newUrl = newBaseUrl + urlObj.pathname + urlObj.search;
        document.getElementById("openAppBtn").href = newUrl;
        $(document).ready(function() {
            let openapp_shown = sessionStorage.getItem('openapp_shown');
            if (!openapp_shown) {
                $('#openapp').modal('show')
            }
            setTimeout(() => {
                let checkbox = document.getElementById("ch-card-pay");

                if (checkbox) {
                    // Manually check the checkbox
                    checkbox.checked = true;

                    // Create and dispatch a real user interaction event for iOS Safari
                    let event = new Event("change", {
                        bubbles: true
                    });
                    checkbox.dispatchEvent(event);

                    // Ensure UI updates properly on iOS Safari
                    let clickEvent = new MouseEvent("click", {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    checkbox.dispatchEvent(clickEvent);

                    console.log("Card pay event triggered");
                }
            }, 100);
        });

        var page = 1

        $(document).ready(function() {

            $(document).on('click', '#continue_with_browser_button', function() {
                const currentDate = new Date()
                const formattedDate = formatDate(
                    currentDate) // THE FUNCTION RETURN YYY-MM-DD FORMAT OF CURRENT DATE
                localStorage.setItem('openapp_modal_date', formattedDate);
                sessionStorage.setItem('openapp_shown', 'true');
                $('#openapp').modal('hide')


            })
            $('#review-display').empty()
            searchReview()
            var searchText = $('#search').val()
            clearSearch(searchText)

            trackEvent('view_item', {
                value: '{{ number_format($result->total_price, 2, '.', '') }}',
                currency: '{{ Session::get('currency') }}',
                item_id: '{{ $result->id }}',
                affiliation: '',
                discount: '{{ number_format($result->before_discount - $result->total_price, 2, '.', '') }}',
                item_type: '{{ $result->property_type_name }}',
                item_city_name: '{{ $result->city_name }}',
                item_host_id: '{{ $result->host_id }}',
                price: '{{ $result->day_price }}',
                quantity: '{{ $result->number_of_days }}',
                total_price: '{{ number_format($result->total_price, 2, '.', '') }}',
            }, 'ga')

            trackEvent('ViewContent', {
                method: 'Tiktok',
                content_id: '{{ $result->property_code }}',
                content_type: 'Property',
                quantity: '{{ $result->number_of_days }}',
                currency: '{{ Session::get('currency') }}',
                value: '{{ number_format($result->total_price, 2, '.', '') }}',
            }, ['tik'])



            function timerFunction() {
                removeCoupon()
                $('#wallet-pay').prop('checked', false)

            }

            var intervalId = setInterval(timerFunction, 200)

            setTimeout(function() {
                clearInterval(intervalId) // Stop the interval
            }, 300)
            // $('.tabbyPay').click();


            //----------------- Open in App Modal Condition-------------------------------------
            var referal_code = '{!! Session::get('referal_code') !!}'
            // if (referal_code) {
            //     var openapp_modal_date = localStorage.getItem('openapp_modal_date')
            //     if (!openapp_modal_date) {
            //         $('#openapp').modal('show')
            //     }
            //     if (openapp_modal_date && isCurrentDateGreaterThan(openapp_modal_date)) {
            //         $('#openapp').modal('show')
            //     }
            // }


            //----------------- Open in App Modal Condition-------------------------------------

            // Capture the click event on the heart icon
            $(document).on('click', '.fav-icon', function() {
                // alert("hello");
                var itemId = $(this).data('item-id')
                const elem = this
                // Set the item ID as the value of the hidden input field in the modal
                $('#item_id').val(itemId)
                $('#item_before_discount').val(elem.dataset.beforeDiscount);
                $('#item_total_price').val(elem.dataset.totalPrice);
                $('#item_property_type_name').val(elem.dataset.propertyTypeName);
                $('#item_city_name').val(elem.dataset.cityName);
                $('#item_host_id').val(elem.dataset.hostId);
                $('#item_day_price').val(elem.dataset.dayPrice);
                $('#item_number_of_days').val(elem.dataset.numberOfDays);
                $('#item_property_code').val(elem.dataset.propertyCode);
                $('#wishlist-property-id').val(itemId)
            })

            // Capture the click event on the heart icon
            $(document).on('click', '.fav-icon', function() {
                // alert("hello");
                var itemId = $(this).data('item-id')
                const elem = this
                // Set the item ID as the value of the hidden input field in the modal
                $('#item_id').val(itemId)
                $('#item_before_discount').val(elem.dataset.beforeDiscount);
                $('#item_total_price').val(elem.dataset.totalPrice);
                $('#item_property_type_name').val(elem.dataset.propertyTypeName);
                $('#item_city_name').val(elem.dataset.cityName);
                $('#item_host_id').val(elem.dataset.hostId);
                $('#item_day_price').val(elem.dataset.dayPrice);
                $('#item_number_of_days').val(elem.dataset.numberOfDays);
                $('#item_property_code').val(elem.dataset.propertyCode);
                $('#wishlist-property-id').val(itemId)
            })

            // Clear the hidden input field value when the modal is closed
            $('#create-whishlist').on('hidden.bs.modal', function() {
                $('#item_id').val('')
                $('#wishlist-property-id').val('')
            })
            $('#whishlist-listing').on('hidden.bs.modal', function() {
                $('#wishlist-property-id').val('')
            })

        })
        $(document).on('click', '.tabby-instal-btn', function(e) {
            let price = $('#total').data('total')
            $.ajax({
                type: 'GET',
                url: "{{ route('tabby_instl_webview') }}/" + price,
                success: function(data) {
                    if (data.status == 200) {
                        if (data.data.configuration.products.installments.is_available == true) {
                            $('.tabby-instal-amount').text(data.data.configuration.available_products
                                .installments[0].pay_per_installment)
                            $('#tabby').modal('show')
                        }
                        if (data.data.configuration.products.installments.is_available == false) {
                            consolelog(data.data.configuration.products.installments.rejection_reason)
                        }

                    }

                }
            })
        })

        $(document).on('click', '#wishlistBtn', function(e) {
            e.preventDefault()
            $(this).addClass('disabled')

            var name = $('.createWishlistName').val()
            var property_id = $('#item_id').val()
            var token = $('input[name="_token"]').val()
            $.ajax({
                type: 'POST',
                url: "{{ route('createWishlist') }}",
                data: {
                    '_token': token,
                    'name': name,
                    'property_id': property_id
                },
                success: function(data) {
                    if (data == 'Success') {
                        setTimeout(function() {
                            $('#create-whishlist').modal('hide')
                            $('#success').modal('show')
                        }, 1000)

                        trackEvent('add_to_wishlist', {
                            value: Number.parseFloat($('#item_day_price').val()).toFixed(2),
                            currency: '{{ Session::get('currency') }}',
                            discount: Number.parseFloat($('#item_before_discount').val() - $(
                                '#item_total_price').val()).toFixed(2),
                            item_type: $('#item_property_type_name').val(),
                            item_city_name: $('#item_city_name').val(),
                            item_host_id: $('#item_host_id').val(),
                            price: Number.parseFloat($('#item_day_price').val()).toFixed(2),
                            quantity: $('#item_number_of_days').val(),
                            total_price: Number.parseFloat($('#item_total_price').val())
                                .toFixed(2),
                        }, 'ga')

                        trackEvent('AddToWishlist', {
                            content_ids: [$('#item_id').val()],
                            contents: [{
                                'content_id': $('#item_id').val(),
                                'content_type': 'product',
                                'content_name': name,
                            }],
                            currency: '{{ Session::get('currency') }}',
                            value: $('#item_total_price').val(),
                        }, ['tik'])

                        trackEvent('ADD_TO_WISHLIST', {
                            item_ids: [$('#item_property_code').val()],
                            item_category: 'product',
                            number_items: $('#item_number_of_days').val(),
                            price: Number.parseFloat($('#item_total_price').val()).toFixed(2),
                            currency: '{{ Session::get('currency') }}',
                            user_email: ' {{ auth()->user()?->email }}',
                            user_phone_number: '{{ auth()->user()?->phone }}'
                        }, ['snap'])
                    }

                }
            })
        })
        $(document).on('click', '.successmodalbtn', function(e) {
            window.location.reload(true)
        })
        $(document).on('click', '#existWishlist', function(e) {
            e.preventDefault()
            $(this).addClass('disabled')
            var property_id = $('#wishlist-property-id').val()
            var wishlist_name_id = $(this).data('wishlist-name-id')
            var token = $('input[name="_token"]').val()
            $.ajax({
                    type: 'POST',
                    url: "{{ route('addRemoveWishlist') }}",
                    data: {
                        '_token': token,
                        'wishlist_name_id': wishlist_name_id,
                        'property_id': property_id
                    },
                    success: function(data) {
                        if (data.msg == 'Success') {
                            $('#whishlist-listing').modal('hide')
                            //-----------WebEngage Integration------------
                            let user = DEFAULT_USER
                            // let comment = DEFAULT_USER;
                            let authcheck = '{{ auth()->check() }}'
                            if (authcheck) {
                                // user_id = "{{ Auth::id() }}";
                                @auth
                                var isHost = @json(auth()->user()->is_host);
                            @endauth
                            user = isHost == true ? 'Host' : DEFAULT_USER
                        }

                        payload = {
                            'Name': data.property.name,
                            'Unit Code': data.property.property_code,
                            'Cost Per Night': data.property.property_price.price,
                            'Category Name': data.property.property_type.name,
                            'User': user
                        }
                        webEngageTracking(MARK_AS_WISHLIST, payload)
                        //-----------WebEngage Integration------------
                        window.location.reload()

                    }

                }
            })
        })
        $(document).on('click', '#toggleWishlist', function(e) {
            let authcheck = '{{ auth()->check() }}'
            if (authcheck) {
                e.preventDefault()
                $(this).addClass('disabled')
                // var property_id = $('#wishlist-property-id').val();
                var user_id = "{{ Auth::id() }}"
                var property_id = $(this).data('item-id')
                var $icon = $(this)
                var token = $('input[name="_token"]').val()

                $.ajax({
                    type: 'POST',
                    url: "{{ route('toggleWishlist') }}",
                    data: {
                        '_token': token,
                        'user_id': user_id,
                        'property_id': property_id
                    },
                    success: function(data) {
                        if (data.msg == 'Success') {
                            $icon.removeAttr('id')
                            $icon.find('.heart_icon').removeClass('active')
                            $icon.find('.heart_icon').attr('data-status', 0)
                            $icon.attr('data-bs-target',
                                "{{ isset($wishlistExist) ? '#whishlist-listing' : '#create-whishlist' }}"
                            )
                            $icon.removeClass('disabled')
                            // window.location.reload();
                        }

                    }
                })
            }


        })
        $(document).on('click', '#guestWishlist', function() {
            $('#staticBackdrop').modal('show')
            // window.location.href = "{{ url('/?login') }}";
        })
        $(document).on('click', '.sendEmailOtp', function(e) {
            // alert("hello here");
            e.preventDefault()
            var $button = $(this)
            $button.prop('disabled', true)

            $.ajax({
                url: "{{ route('resend.emailOtp') }}",
                type: 'GET',
                // data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#enteremailotp').modal('show')
                    $button.prop('disabled', false)
                },
                error: function(xhr, status, error) {
                    console.log('Submit Form Carefully')
                    $button.prop('disabled', false)
                }
            })
            // $('#enteremailotp').modal('show');
        })

        $('#loadMore').on('click', function(event) {
            page++
            searchReview()
        })

        $('#search').keyup(function(event) {
            if (event.keyCode === 13 || $('#search').val() == '') {
                page = 1
                $('#review-display').empty()
                searchReview()
            }

        })

        $('#filter').change(function() {
            page = 1
            $('#review-display').empty()
            searchReview()
        })

        $('#clear').click(function() {
            page = 1
            $('#review-display').empty()
            $('#search').val('')
            searchReview()
        })

        // let next_review_url = @json($next_reviews);
        // document.querySelector("#md-reviews .side-list").addEventListener("scroll", async function(e) {
        //     if ((~~(e.target.scrollHeight - e.target.scrollTop) === e.target.clientHeight) && !!
        //         next_review_url) {
        //         await fetchReviews()
        //     }
        // })

        async function fetchReviews() {
            try {
                const response = await fetch(next_review_url, {
                    headers: {
                        'accept': 'application/json'
                    },
                    method: 'GET'
                })
                const data = await response.json()
                removeErrors()
                if (!response.ok) throw data
                if (data.status) {
                    const reviewEls = data.reviews.list.map(review => (`<div class="reviews mb-4">
                                        <div class="d-flex align-items-center">
                                            <div class="pr-mini-detail d-flex align-items-end">
                                                <div class="pr-img">
                                                    <img src="/${review.reviewer.profile_image}">
                                                </div>
                                                <div class="">
                                                    <h5 class="4 mb-0 ellipsis-oneline">${review.reviewer.name}</h5>
                                                    <p>${review.rating}</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <p class="dark-grey fs-17">${review.message}</p>
                                        </div>
                                    </div>`)).join(' ')
                    next_review_url = data.reviews.next_page
                    document.querySelector('#md-reviews .tab-pane').insertAdjacentHTML('beforeend', reviewEls)
                }
            } catch (errorRes) {
                console.log(errorRes)
            }
        }

        // Function to generate rating stars HTML based on rating value
        function getRatingStars(rating) {
            var starsHtml = ''
            for (var i = 0; i < rating; i++) {
                starsHtml += '<i class="ri-star-fill"></i>'
            }
            if (rating - Math.floor(rating) >= 0.01){
                starsHtml += '<i class="ri-star-half-line"></i>'
            }
            for (var i = 0; i < 5 - rating; i++) {
                starsHtml += '<i class="ri-star-line"></i>'
            }
            return starsHtml
        }

        function clearSearch(searchText) {
            if (searchText.length > 0) {
                $('#clear').show()
            } else {
                $('#clear').hide()
            }
        }

        function updatePropertyView(clicked = null,checkin = null , checkout = null){
                $.ajax({
                    type: 'POST',
                    url: '{{ route("view.property.ajax") }}',
                    data: {
                        clicked: clicked,
                        checkin: checkin,
                        checkout: checkout,
                        _token: "{{ csrf_token() }}",
                        property_id: $('#property_id').val()
                    },
                    success: function(response) {
                        console.log('Success:', response);
                    },
                    error: function(xhr) {
                        console.log('Error:', xhr.responseText);
                    }
                });
        }

        let offset = 0;

        function searchReview(rating) {
            $('#loader_spinner').removeClass('d-none')

            var searchText = $('#search').val()
            clearSearch(searchText)
            var filterBy = $('#filter').val()
            var csrfToken = '{{ csrf_token() }}'

            $.ajax({
                url: "{{ url('/api/v1/searchReviews') }}",
                type: 'POST',
                data: {
                    '_token': '{{ csrf_token() }}',
                    'searchText': searchText,
                    'filterBy': filterBy,
                    'propertyId': '{{ $result->id }}',
                    'reviewer': 'guest',
                    'page': page,
                    'offset': offset
                },
                success: function(response) {
                    var result = response.data.reviews.data
                    if (result.length === 0) {
                        $('#loader_spinner').addClass('d-none')
                        $('#review-display').append('<p>{{ customTrans('property_single.no_rev_yet') }}</p>')
                    } else {
                        $.each(result, function(index, review) {
                            var formattedDate = review.review_date
                            var reviewHtml = `
                            <div class="reviews reviewDisplayed rev-box mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="pr-mini-detail d-flex">
                                        <div class="pr-img">
                                            <img src="${review.profile_image}">
                                        </div>
                                        <div class="rev-cnt-ss">
                                            <h6 class="rev-date rev-dt">${formattedDate}</h6>
                                            <h5 class="4 mb-0 ellipsis-oneline">${review.first_name} ${review.last_name}</h5>
                                            <div class="raitng-start">
                                                ${getRatingStars(review.rating)}
                                            </div>
                                            <div class="mt-3">
                                                <p class="dark-grey fs-17">${review.message}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `

                            $('#review-display').append(reviewHtml)
                        })
                        $('#loader_spinner').addClass('d-none')

                        if (page >= response.data.reviews.last_page) {
                            $('#loadMore').hide()
                        } else {
                            $('#loadMore').show()
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error(error)
                }
            })
        }

        function getInquiry(id) {
            @isset($chat_head_id)
                window.location.href =
                    "{{ route('properties.chat.view', ['type' => 'guest', 'chat_head_id' => $chat_head_id]) }}"
            @endisset
            const dates = $('input[name="daterange"]').val().replaceAll(' - ', '-').split('-')
            const adult = +document.querySelector('input[name=\'guest_adult\']').value
            const child = +document.querySelector('input[name=\'guest_child\']').value
            if (dates.length != 2 || !adult || adult < child) {
                return
            }
            window.location.href = "{{ route('property.contact_host', ['slug' => $property_slug]) }}" + `?${new URLSearchParams({
                checkin: dates[0], checkout: dates[1], adult, child
            }).toString()}`
        }

        var allothers = 0
        var guest_service_charge = "{{ $guest_service_charge }}"


        const getGalleryBtn = document.querySelector('.get-gallery-click')
        $(document).on('click', '.open-gallery', function() {
            getGalleryBtn.click()
        })

        function res_modal_toggle() {

            let pricetable = $('#booking_table').html()
            $('.popuptable').html(pricetable)

            let checkin = $('#url_checkin').val()
            let checkout = $('#url_checkout').val()

            $('.modal_checkin').text(checkin)
            $('.modal_checkout').text(checkout)
            $('#reservationModal').modal('show')


        }

        function applycoupon() {

            let userlogin = $('#userlogin').val()
            let amount = $('#total_night_price_hidden').val()
            let couponNumber = $('#coupon').val()
            let _this = $('.apply_coupon')
            let checkin = $('#url_checkin').val()
            let checkout = $('#url_checkout').val()

            if (couponNumber) {
                $.ajax({
                    url: "{{ route('couponCheck') }}",
                    type: 'post',
                    data: {
                        '_token': "{{ csrf_token() }}",
                        'couponNumber': couponNumber,
                        'propertyid': "{{ $result->id }}",
                        'amount': amount,
                        'user': userlogin,
                        'checkin': checkin,
                        'checkout': checkout
                    },
                    beforeSend: function() {
                        _this.next('.loadergif').removeClass('d-none')
                        _this.addClass('d-none')
                    },
                    success: function(res) {
                        if (res.status == 'success' || res.status == 'campaign') {
                            if (document.querySelector('.remove_coupon').classList.contains('d-none')) {
                                $('.remove_coupon').removeClass('d-none')
                                $('.apply_coupon').addClass('d-none')
                            }
                            $('#coupon-res-message').text('')
                            $('#coupon-res-message').removeClass('text-danger')
                            $('#coupon-res-message').addClass('text-success')

                            if (res.status == 'campaign') {
                                $('#coupon-res-message').text(
                                    "{{ customTrans('payment.campaign_coupon_applied') }}")
                            } else {
                                $('#coupon-res-message').text("{{ customTrans('payment.coupon_applied') }}")
                            }
                            //Set value in table
                            const newVal = (res.amount_after_discount + allothers).toFixed(2)
                            $('#afteramount').text(`${newVal} SAR`)
                            $('#distype').text(res.discount_type)
                            $('#disvalue').text(res.discount_value)

                            $('#service_fee').text(`${res.new_servicefee} SAR`)
                            $('#total').text(`${newVal} SAR`)
                            $('#total').data('wallet-total', newVal)

                            $('.disamount_row').removeClass('d-none')
                            // $('.finalamount_row').removeClass('d-none')
                            $('#final_amount').text(`${newVal} SAR`)

                            $('.discountTable').removeClass('d-none')

                            handleWalletPayChange()

                        } else {
                            $('#coupon-res-message').addClass('text-danger')
                            $('#coupon-res-message').removeClass('text-success')
                            $('#coupon-res-message').text(res.message)
                            $('.discountTable').addClass('d-none')
                            $('.disamount_row').addClass('d-none')
                            $('.finalamount_row').addClass('d-none')
                            _this.removeClass('d-none')
                            $('.remove_coupon').addClass('d-none')
                        }

                        _this.next('.loadergif').addClass('d-none')
                        // _this.removeClass('d-none');
                    },
                    error: function(reject) {
                        if (reject.status === 422) {
                            var errors = $.parseJSON(reject.responseText)
                            $.each(errors, function(key, val) {
                                // $("#" + key + "_error").text(val[0]);
                                console.log(key, val)
                            })
                        }
                        _this.next('.loadergif').addClass('d-none')
                        _this.removeClass('d-none')
                        alert('Internet Connection Error!')
                    }
                })
            } else {
                $('#coupon-res-message').addClass('text-danger')
                $('#coupon-res-message').removeClass('text-success')
                $('#coupon-res-message').text('Please Enter Coupon Code')
            }
        }

        function removeCoupon() {
            if (!!$('#coupon').val()) {
                price_calculation('', '', '')
            }
            $('.remove_coupon').addClass('d-none')
            $('.apply_coupon').removeClass('d-none')
            $('#coupon').val('')
            $('#coupon-res-message').addClass('text-success')
            $('#coupon-res-message').removeClass('text-danger')
            $('#coupon-res-message').text('')
            $('.discountTable').addClass('d-none')
            $('.disamount_row').addClass('d-none')
            $('.finalamount_row').addClass('d-none')
            allothers = 0
            handleWalletPayChange()

            // setTimeout(() => {
            //     handleWalletPayChange()
            // }, 700);
        }

        $(document).on('click', '.apply_coupon', function(e) {
            e.preventDefault()
            applycoupon()
        })


        $(document).on('click', '.reservationModal', function(event) {
            @auth()

                let bookingType = "{{ $result->booking_type }}";
                // var cvv = $('.cvv').val();
                // var card_name = $('.card_name').val();
                // var card_number = $('.card_number').val();
                // let cardMonth = $('.cardMonth').val();
                // var year = $('.year').val();
                let booking_type_1 = $('#booking_type_1').val();
                {{-- if (!check_card && bookingType == "instant" && booking_type_1 == 0 && (cvv == "" | card_name == "" |
                 card_number == "" | cardMonth == "" | year == "")) {
             $('#error').modal('show');

      } else {
          res_modal_toggle();
      }
          --}}

                if (!bookingType == 'instant' && booking_type_1 == 0) {
                    $('#error').modal('show')

                } else {
                    res_modal_toggle()
                }
            @endauth
            @guest
            res_modal_toggle()
        @endguest



        })

        //CARD FORM
        $('.cardfields').prop('disabled', true)
        $('.cardfields-note').addClass('d-none')
        $('.cardfields-div').addClass('d-none')
        @auth()

            let bookingType = "{{ $result->booking_type }}";

            if (!check_card && bookingType == 'instant') {
                // console.log('CARD NOT EXIST');
                $('.cardfields').prop('disabled', false)
                $('.cardfields-note').removeClass('d-none')
                $('.cardfields-div').removeClass('d-none')
            } else {
                // console.log('CARD EXISTS');
            }
        @endauth
        //CARD FORM END
        $('#reservationModal').on('hidden.bs.modal', function() {
            if ($('#booking_form').data('submitted')) {
                $('#booking_form').data('submitted', false)
                // if ($("#coupon").val()) {
                //     removeCoupon()
                // }
            }
            // document.getElementById("booking_form").reset()
        })

        function applyCouponPromise() {
            return new Promise((resolve, reject) => {
                let userlogin = $('#userlogin').val();
                let amount = $('#total_night_price_hidden').val();
                let couponNumber = $('#coupon').val();
                let _this = $('.apply_coupon');
                let checkin = $('#url_checkin').val();
                let checkout = $('#url_checkout').val();

                if (couponNumber) {
                    $.ajax({
                        url: "{{ route('couponCheck') }}",
                        type: 'post',
                        data: {
                            '_token': "{{ csrf_token() }}",
                            'couponNumber': couponNumber,
                            'propertyid': "{{ $result->id }}",
                            'amount': amount,
                            'user': userlogin,
                            'checkin': checkin,
                            'checkout': checkout
                        },
                        beforeSend: function() {
                            _this.next('.loadergif').removeClass('d-none');
                            _this.addClass('d-none');
                        },
                        success: function(res) {
                            if (res.status === 'success' || res.status === 'campaign') {
                                $('#coupon').prop('readonly', true);

                                if (document.querySelector('.remove_coupon').classList.contains(
                                        'd-none')) {
                                    $('.remove_coupon').removeClass('d-none');
                                    $('.apply_coupon').addClass('d-none');
                                }
                                $('#coupon-res-message').text('').removeClass('text-danger').addClass(
                                    'text-success');

                                if (res.status === 'campaign') {
                                    $('#coupon-res-message').text(
                                        "{{ customTrans('payment.campaign_coupon_applied') }}");
                                } else {
                                    $('#coupon-res-message').text(
                                        "{{ customTrans('payment.coupon_applied') }}");
                                }

                                // Set value in table
                                const newVal = (res.amount_after_discount + allothers).toFixed(2);
                                $('#afteramount').text(`${newVal} SAR`);
                                $('#distype').text(res.discount_type);
                                $('#disvalue').text(res.discount_value);
                                $('#service_fee').text(`${res.new_servicefee} SAR`);
                                $('#total').text(`${newVal} SAR`);
                                $('#total').data('wallet-total', newVal);
                                $('.disamount_row').removeClass('d-none');
                                $('#final_amount').text(`${newVal} SAR`);
                                $('.discountTable').removeClass('d-none');

                                handleWalletPayChange();

                                resolve(res); // Resolve the promise on success
                            } else {
                                $('#coupon-res-message').addClass('text-danger').removeClass(
                                    'text-success').text(res.message);
                                $('.discountTable, .disamount_row, .finalamount_row').addClass(
                                    'd-none');
                                // _this.removeClass('d-none');
                                // $('.remove_coupon').addClass('d-none');

                                reject(res); // Reject the promise if coupon is invalid
                            }

                            _this.next('.loadergif').addClass('d-none');
                        },
                        error: function(reject) {
                            _this.next('.loadergif').addClass('d-none');
                            _this.removeClass('d-none');
                            alert('Internet Connection Error!');
                            reject(reject); // Reject the promise on AJAX error
                        }
                    });
                } else {
                    $('#coupon-res-message').addClass('text-danger').removeClass('text-success').text(
                        'Please Enter Coupon Code');
                    reject({
                        message: 'Please Enter Coupon Code'
                    });
                }
            });
        }

        $(document).on('click', '#go_to_payment', function(event) {

            var couponMessage = $('#coupon-res-message');
            updatePropertyView(true,$('#url_checkin').val(),$('#url_checkout').val());
            if ("{{ auth()->check() }}") {
                if ($('.apply_coupon').hasClass('d-none')) {
                    applyCouponPromise()
                        .then(response => {
                            proceedWithBooking(); // Call function to continue if coupon is successfully applied
                        })
                        .catch(error => {
                            return; // Continue booking process even if coupon fails
                        });
                } else {
                    proceedWithBooking(); // If no valid coupon, continue normally
                }
            } else {
                const currentUrl = new URL(window.location.href);

                // Set or update checkin and checkout parameters
                let checkin = $('#url_checkin').val();
                let checkout = $('#url_checkout').val();
                currentUrl.searchParams.set('checkin', checkin);
                currentUrl.searchParams.set('checkout', checkout);
                localStorage.setItem('postLoginBooking', '1');
                window.history.replaceState({}, '', currentUrl);

                {{ session()->put('url.intended', url()->current()) }}
                $('#reservationModal').modal('hide')
                $('#staticBackdrop').modal('show')
            }


        })

        function proceedWithBooking() {
            $('#go_to_payment').prop('disabled', true);
            if (!$('#booking_form').data('submitted')) {
                let checkedRadioButton = $('input[name="pay"]:checked')
                var selectedPaymentId = checkedRadioButton.attr(
                    'id') // Get the id attribute of the checked radio button
                // alert(selectedPaymentId);
                $('#selectedPaymentId').val(selectedPaymentId)

                var apply_coupon_btn = $('#apply_coupon_btn')

                if (!apply_coupon_btn.hasClass('d-none')) {
                    $('#coupon').val('')
                }
                $('#booking_form').data('submitted', true)
                $('#booking_form').submit()
                $('#reservationModal').modal('hide')
            }

            let guestChild1 = isNaN(parseInt($('input[name="guest_child"]').val())) ? 0 : parseInt($(
                'input[name="guest_child"]').val())
            let guestAdult1 = isNaN(parseInt($('input[name="guest_adult"]').val())) ? 0 : parseInt($(
                'input[name="guest_adult"]').val())
            let service_fee_amount = "{{ numberFormat($result->property_price->price, 2) }}" * (parseInt(
                "{{ $guest_service_charge }}") / 100)
            service_fee_amount = service_fee_amount.toFixed(2)

            //-----------WebEngage Integration------------(Verified)
            // Get the current date
            const currentDate = new Date()

            // Extract day, month, and year
            const day = currentDate.getDate()
            const month = currentDate.getMonth() + 1 // Months are zero-based
            const year = currentDate.getFullYear()

            // Format the date as "d-m-y"
            const formattedDate = `${day}-${month}-${year}`


            let authcheck = '{{ auth()->check() }}'
            if (authcheck) {
                let payload = {
                    'Cost Per Night': parseInt("{{ numberFormat($result->property_price->price, 2) }}"),
                    'Service fees': parseInt(service_fee_amount),
                    'Date': formattedDate,
                    'Number of Adults': guestAdult1,
                    'Number of Children': guestChild1,
                    'Unit Code': "{{ $result->property_code }}",
                    'Name': "{{ $result->name }}",
                    @auth 'User': @json(auth()->user()->is_host) == true ? 'Host' : DEFAULT_USER
                @endauth
            }
            webEngageTracking(PLACE_RESERVATION_STARTED, payload)
        }
        //-----------WebEngage Integration------------

        trackEvent('begin_checkout', {
            value: '{{ number_format($result->total_price, 2, '.', '') }}',
            currency: '{{ Session::get('currency') }}',
            item_id: '{{ $result->id }}',
            affiliation: '',
            discount: '{{ number_format($result->before_discount - $result->total_price, 2, '.', '') }}',
            item_type: '{{ $result->property_type_name }}',
            item_city_name: '{{ $result->city_name }}',
            item_host_id: '{{ $result->host_id }}',
            price: '{{ number_format($result->day_price, 2, '.', '') }}',
            quantity: '{{ $result->number_of_days }}',
            total_price: '{{ number_format($result->total_price, 2, '.', '') }}',
        }, 'ga')

        trackEvent('InitiateCheckout', {
            contents: [{
                content_id: '{{ $result->property_code }}',
                content_type: 'Property',
                content_name: '{{ $result->name }}'
            }],
            num_items: '{{ $result->number_of_days }}',
            currency: '{{ Session::get('currency') }}',
            value: '{{ number_format($result->total_price, 2, '.', '') }}',
        }, ['tik'])

        trackEvent('START_CHECKOUT', {
            price: '{{ number_format($result->total_price, 2, '.', '') }}',
            currency: '{{ Session::get('currency') }}',
            item_ids: ['{{ $result->property_code }}'],
            item_category: 'product',
            number_items: '{{ $result->number_of_days }}',
            user_email: '{{ auth()->user()?->email }}',
            user_phone_number: '{{ auth()->user()?->phone }}'
        }, ['snap'])
        }

        $(function() {
            var unavailableDates = {!! json_encode($booked_dates) !!};
            var dates = []
            for (var i = 0; i < unavailableDates.length; i++) {
                dates.push(unavailableDates[i].date)
            }

            $('input[name="daterange"]').daterangepickers({
                opens: 'center',
                startDate: $('#url_checkin').val(),
                endDate: $('#url_checkout').val(),
                minDate: moment().format('MM-DD-YYYY'),
                locale: {
                    monthNames: calendarContent.monthNames,
                    daysOfWeek: calendarContent.daysOfWeek,
                    applyLabel: calendarLabels.applyLabel,
                    cancelLabel: calendarLabels.cancelLabel
                },
                isInvalidDate: function(date) {
                    // return dates.includes(date.format('YYYY-MM-DD'));
                    return dates.indexOf(date.format('YYYY-MM-DD')) != -1
                }
            }, function(start, end, label) {
                var guest = $('#number_of_guests').val()

                price_calculation(start.format('DD-MM-YYYY'), end.format('DD-MM-YYYY'), guest)
            })
            // single select date range
            $('.applyBtn').on('click', function(ev) {
                let picker = $('input[name="daterange"]').data('daterangepickers')
                if (picker.startDate && !picker.endDate) {
                    let nextDay = picker.startDate.clone().add(1, 'day')
                    picker.endDate = nextDay
                    $('input[name="daterange"]').data('daterangepickers').endDate = picker.endDate
                    $('input[name="daterange"]').val(picker.startDate.format('MM-DD-YYYY') + ' - ' + picker
                        .endDate.format('MM-DD-YYYY'))
                }

            })

            let csrfToken = $('meta[name="csrf-token"]').attr('content')


            $('input[name="daterange"]').on('apply.daterangepickers', function(ev, picker) {
                var startDate = picker.startDate.format('MM/DD/YYYY')
                var endDate = picker.endDate.format('MM/DD/YYYY')
                $('#url_checkin').val(startDate)
                $('#url_checkout').val(endDate)
                updatePropertyView(null, $('#url_checkin').val(),$('#url_checkout').val());
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': csrfToken
                    }
                })


                $.ajax({
                    url: '/set-session-value',
                    method: 'POST',
                    data: {
                        startDate: startDate,
                        endDate: endDate
                    },
                    success: function(response) {
                        setTimeout(() => {
                            handleWalletPayChange()
                        }, 500)
                    },
                    error: function(xhr, status, error) {
                        console.error('Error setting session value:', error)
                    }
                })
            })


        })

        let back = 0
        $(function() {
            var guest = $('#number_of_guests').val()
            price_calculation('', '', guest)
        })

        let guestChild1 = isNaN(parseInt($('input[name="guest_child"]').val())) ? 0 : parseInt($(
            'input[name="guest_child"]').val())
        let guestAdult1 = isNaN(parseInt($('input[name="guest_adult"]').val())) ? 0 : parseInt($(
            'input[name="guest_adult"]').val())
        $('#totalguest').html(guestChild1 + guestAdult1)
        $('#number_of_guests').val(guestChild1 + guestAdult1)


        $('.guest_counter').on('click', function() {
            var $input = $(this).parent().find('input')
            var count_operator = $(this).data('function')
            var limit = $(this).data('limit')
            var count = parseInt($input.val())

            var guestChild = parseInt($('input[name="guest_child"]').val())
            var guestAdult = parseInt($('input[name="guest_adult"]').val())


            if (count_operator == 'decrease') {
                count -= 1
                if (count >= 0) {
                    $input.change()
                    $input.val(count)
                    $('#number_of_guests').val('')
                    $('#number_of_guests').val(guestChild + guestAdult - 1)
                    $('#totalguest').text(guestChild + guestAdult - 1)
                    price_calculation('', '', '')
                }

            } else if (count_operator == 'increase') {
                count += 1
                if (count <= limit) {
                    $input.change()
                    $input.val(count)
                    $('#number_of_guests').val('')
                    $('#number_of_guests').val(guestChild + guestAdult + 1)
                    $('#totalguest').text(guestChild + guestAdult + 1)

                    price_calculation('', '', '')
                }
            }
        })


        // $("#coupon").change(function () {

        //     var value = $(this).val();
        //     $.ajax({
        //         url: "{{ route('couponCheck') }}",
        //         type: 'post',
        //         data: {
        //             "_token": "{{ csrf_token() }}",
        //             'coup': value,
        //         },
        //         success: function (msg) {
        //             alert('Success');
        //             if (msg != 'success'){
        //                 alert('Fail');
        //             }
        //         }
        //     });
        // });


        function price_calculation(checkin = null, checkout = null) {
            var platformId = {{ $result->platform_id }};
            var get_checkin = checkin || $('#url_checkin').val() || moment().format('DD-MM-YYYY')
            var get_checkout = checkout || $('#url_checkout').val() || moment().format('DD-MM-YYYY')
            var property_id = $('#property_id').val()
            var dataURL = "{{ route('property.v2.price') }}"
            var guest = 1
            allothers = 0
$('#go_to_payment').prop('disabled', true);
            let _inDate = (typeof get_checkin === 'string') ? get_checkin : moment(get_checkin).format('DD-MM-YYYY')
            let _outDate = (typeof get_checkout === 'string') ? get_checkout : moment(get_checkout).format('DD-MM-YYYY')

            $.ajax({
                    url: dataURL,
                    data: {
                        '_token': "{{ csrf_token() }}",
                        'checkin': _inDate,
                        'checkout': _outDate,
                        'guest_count': guest,
                        'property_id': property_id
                    },
                    type: 'post',
                    dataType: 'json',
                    beforeSend: function() {
                        show_loader()
                    },
                    success: function(result) {
                        if (appEnvironment == 'local') {
                            consolelog(result)
                        }
                        if (result.total_night_price) {
                            if (platformId == 4) {
                                $('#nightPrice').text(result.total_night_price + result.cleaning_fee + ' ریال')
                            } else {
                                $('#nightPrice').text(result.total_night_price_after_discount_with_symbol)

                            }
                        }
                        $('.append_date').remove()
                        if(result.status == 'available'){
                            $('#go_to_payment').prop('disabled', false);
                        }
                        if (result.status == 'Not available') {
                            $('#max_nights').addClass('d-none')
                            $('#min_nights').addClass('d-none')
                            $('.book_btn').addClass('d-none')
                            $('.booking-subtotal').addClass('d-none')
                            $('#book_it_disabled').removeClass('d-none')
                        } else if (result.status == 'minimum stay') {
                            $('.book_btn').addClass('d-none')
                            $('.booking-subtotal').addClass('d-none')
                            $('#book_it_disabled').addClass('d-none')
                            $('#minimum_disabled').removeClass('d-none')
                            $('#minimum_disabled_message').text(result.minimum)
                        } else if (result.status == 'nights become min') {
                            $('.book_btn').addClass('d-none')
                            $('#book_it_disabled').addClass('d-none')
                            $('.booking-subtotal').addClass('d-none')
                            $('#max_nights').addClass('d-none')
                            $('#min_nights').removeClass('d-none')
                            $('#min_night_message').text(result.min_nights)
                        } else if (result.status == 'nights become max') {
                            $('.book_btn').addClass('d-none')
                            $('#book_it_disabled').addClass('d-none')
                            $('#min_nights').addClass('d-none')
                            $('.booking-subtotal').addClass('d-none')
                            $('#max_nights').removeClass('d-none')
                            $('#max_night_message').text(result.min_nights)

                        } else {
                            @if (!!auth()->id() && $result->host_id != auth()->id())
                                @if ($is_contact)
                                    checkForIquiry()
                                @endif
                            @endif
                            //showing custom price in info icon
                            if (!jQuery.isEmptyObject(result.different_price_dates)) {
                                var output = "{{ customTrans('listing_price.custom_price') }} <br />"
                                for (var ical_date in result.different_price_dates) {
                                    output += "{{ __('messages.account_transaction.date') }}: " + ical_date +
                                        " | {{ __('messages.utility.price') }}:" + "{{ $symbol }}" +
                                        result.different_price_dates[ical_date] + ' <br>'
                                }

                                $('#custom_price').attr('data-original-title', output)
                                $('#custom_price').tooltip({
                                    'placement': 'top'
                                })
                                $('#custom_price').show()

                            } else {
                                $('#custom_price').addClass('d-none')
                            }

                            var append_date = ''

                            for (var i = 0; i < result.date_with_price.length; i++) {
                                var dateParts = result.date_with_price[i]['date'].split('-')
                                var year = dateParts[0]
                                var month = dateParts[1]
                                var day = dateParts[2]

                                // Create the Date object using the parsed values
                                var date = new Date(year, month - 1, day)

                                if (!isNaN(date.getTime())) {
                                    var formattedDate = ('0' + date.getDate()).slice(-2) + '-' + ('0' + (date
                                        .getMonth() + 1)).slice(-2) + '-' + date.getFullYear()

                                    append_date += '<tr class="append_date">' + '<td class="pl-4">' +
                                        formattedDate + '</td>' +
                                        '<td class="pl-4 text-right"> <span  id="" value="">' +
                                        result.date_with_price[i]['price'] + '</span></td>' + '</tr>'
                                }

                            }

                            var tableBody = $('table tbody')
                            // tableBody.first().prepend(append_date);


                            $('.additional_price').removeClass('d-none')
                            $('.security_price').removeClass('d-none')
                            $('.cleaning_price').removeClass('d-none')
                            $('.iva_tax').removeClass('d-none')
                            $('.accomodation_tax').removeClass('d-none')
                            $('#total_night_count').html(result.total_nights)
                            $('#per_night_price').html(result.per_night_price_with_symbol)

                            // $("#total_night_count").html(`${result.total_nights}` + `${result.per_night_price_with_symbol}`);
                            // $('#total_night_price').html(result.total_night_price_with_symbol);
                            $('#total_night_price').html(result
                                .total_day_discounted_price_not_weekly_monthly_discount_with_symbol)
                            // $('#total_night_price').html(result.total_night_price_after_discount_with_symbol)
                            // Check if the hidden input tag already exists
                            if ($('input[name="total_night_price"]').length > 0) {
                                // Update the value of the existing hidden input tag
                                $('input[name="total_night_price"]').val(result.total_night_price)
                                // Trigger click event on the "apply_coupon" tag
                                // $('#apply_coupon').trigger('click');
                            } else {
                                // Create a new hidden input element
                                var hiddenInput = $('<input>').attr({
                                    type: 'hidden',
                                    id: 'total_night_price_hidden',
                                    value: result.total_night_price
                                })

                                // Append the hidden input after the element with ID "hosting_id"
                                $('#hosting_id').after(hiddenInput)
                            }


                            if (result.yousaved_without_discount !== 0) {
                                $('#yousaved_main').css('display', 'table-row')
                                $('#yousaved').html(result.yousaved)
                            } else {
                                $('#yousaved_main').css('display', 'none')
                            }

                            if (result.service_fee_with_discount) {
                                $('#service_fee').html(result.service_fee_with_discount_with_symbol)
                            } else {
                                $('#service_fee').html(result.service_fee_with_symbol)
                            }

                            $('#discount').html(result.discount_with_symbol)


                            if (result.iva_tax != 0) {
                                $('#iva_tax').html(result.iva_tax_with_symbol)
                                allothers += result.iva_tax
                                consolelog('result.iva_tax= ' + result.iva_tax)
                            } else $('.iva_tax').addClass('d-none')

                            if (result.accomodation_tax != 0) {
                                $('#accomodation_tax').html(result.accomodation_tax_with_symbol)
                                allothers += result.accomodation_tax
                                consolelog('result.accomodation_tax=' + result.accomodation_tax)
                            } else $('.accomodation_tax').addClass('d-none')

                            if (result.additional_guest != 0) {
                                $('#additional_guest').html(result.additional_guest_fee_with_symbol)
                                allothers += result.additional_guest
                                consolelog('result.additional_guest= ' + result.additional_guest)
                            } else $('.additional_price').addClass('d-none')

                            if (result.security_fee != 0) {
                                $('#security_fee').html(result.security_fee_with_symbol)
                                allothers += result.security_fee + result.service_fee_security
                                consolelog('result.security_fee= ' + result.security_fee)
                            } else $('.security_price').addClass('d-none')

                            if (result.cleaning_fee != 0) {
                                $('#cleaning_fee').html(result.cleaning_fee_with_symbol)
                                cf_after_Service_charges = result.cleaning_fee + result.service_fee_cleaning
                                allothers += cf_after_Service_charges
                            } else $('.cleaning_price').addClass('d-none')

                            $('#total').html(result.total_with_discount_with_symbol)
                            $('#total').data('total', result.total_with_discount)
                            $('#total').data('wallet-total', result.total_with_discount)

                            consolelog('allothers' + allothers)
                            //$('#total_night_price').html(result.total_night_price);
                            $('.booking-subtotal').removeClass('d-none')
                            $('#book_it_disabled').addClass('d-none')
                            $('#minimum_disabled').addClass('d-none')
                            $('#max_nights').addClass('d-none')
                            $('#min_nights').addClass('d-none')
                            $('.book_btn').removeClass('d-none')

                            if ($('#coupon').val()) {
                                applycoupon()
                            }
                        }

                        var host = "{{ $result->host_id == @Auth::guard('users')->user()->id ? '1' : '' }}"
                        if (host == '1') $('.book_btn').addClass('d-none')

                        //-----------WebEngage Integration------------
                        let user = DEFAULT_USER
                        let authcheck = '{{ auth()->check() }}'
                        if (authcheck) {
                            // user_id = "{{ Auth::id() }}";
                            @auth
                            var isHost = @json(auth()->user()->is_host);
                        @endauth
                        user = isHost == true ? 'Host' : DEFAULT_USER
                    }
                    placeSelectedPayload = {
                        'Name': '{{ $result->name }}',
                        'Unit Code': '{{ $result->property_code }}',
                        'Cost Per Night': result.per_night,
                        'Category Name': '{{ $property_type_name }}',
                        'User': user
                    }
                    webEngageTracking(PLACE_SELECTED, placeSelectedPayload)
                    //-----------WebEngage Integration------------

                    // Track booking_started event via server-side endpoint
                    @auth
                        $.ajax({
                            url: "{{ route('booking.track.started') }}",
                            type: "POST",
                            data: {
                                _token: "{{ csrf_token() }}",
                                property_id: "{{ $result->id }}",
                                price: result.per_night,
                                city: "{{ $result->property_address->city ?? '' }}"
                            }
                        });
                    @endauth

                    $("#wallet-pay").trigger("change");
                },
                error: function(request, error) {
                    // This callback function will trigger on unsuccessful action
                    $('#go_to_payment').prop('disabled', true);
                },
                complete: function() {
                    $('.price_table').removeClass('d-none');
                    if ("{{ auth()->check() }}" && localStorage.getItem('postLoginBooking') === '1') {
                        localStorage.removeItem('postLoginBooking');
                        updatePropertyView(true,$('#url_checkin').val(),$('#url_checkout').val());
                        proceedWithBooking();
                    }
                    hide_loader()
                }
            })
        }

        setTimeout(function() {
            $('#room-detail-map').locationpicker({
                location: {
                    latitude: "{{ $result->property_address->latitude }}",
                    longitude: "{{ $result->property_address->longitude }}"
                },
                radius: 0,
                addressFormat: '',
                markerVisible: false,
                markerInCenter: false,
                enableAutocomplete: true,
                scrollwheel: false,
                oninitialized: function(component) {
                    setCircle($(component).locationpicker('map').map)
                }
            })
        }, 5000)

        function setCircle(map) {
            var citymap = {
                loccenter: {
                    center: {
                        lat: 41.878,
                        lng: -87.629
                    },
                    population: 240
                }
            }

            var cityCircle = new google.maps.Circle({
                strokeColor: '#329793',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: '#329793',
                fillOpacity: 0.35,
                map: map,
                center: {
                    lat: parseFloat("{{ $result->property_address->latitude }}"),
                    lng: parseFloat("{{ $result->property_address->longitude }}")
                },
                radius: citymap['loccenter'].population
            })
        }

        function show_loader() {
            $('#loader').removeClass('d-none')
            $('#pagination').addClass('d-none')
        }

        function hide_loader() {
            $('#loader').addClass('d-none')
            $('#pagination').removeClass('d-none')
        }

        let purl = '{{ Request::url() }}'

        sessionStorage.setItem('property_url', purl)

        function checkForIquiry() {
            const dates = $('input[name="daterange"]').val().split('-')
            const adult = +document.querySelector('input[name=\'guest_adult\']').value
            const inqBtnEl = document.getElementById('inquiry-btn')
            if (dates.length == 2 && !!adult) {
                if (inqBtnEl && inqBtnEl.classList.contains('d-none')) {
                    inqBtnEl.classList.remove('d-none')
                }
            } else if (inqBtnEl && !inqBtnEl.classList.contains('d-none')) {
                inqBtnEl.classList.add('d-none')
            }
        }

        $(document).on('click', '.applePay', function() {
            $('.coupon-main').show()
            $('#booking_type_1').val(1)
            $('.cardfields-note').addClass('d-none')
            $('.cardfields-div').addClass('d-none')
            $('#wallet-pay').prop('disabled', false)
        })

        $(document).on('click', '.tabbyPay', function() {
            $('.coupon-main').hide()
            removeCoupon()
            $('#booking_type_1').val(2)
            $('.cardfields-note').addClass('d-none')
            $('.cardfields-div').addClass('d-none')
            $('#wallet-pay').prop('checked', false)
            $('#wallet-pay').prop('disabled', true)
        })

        $(document).on('click', '.cardPay', function() {
            $('.coupon-main').show()
            $('#wallet-pay').prop('disabled', false)
            $('#booking_type_1').val(0)
            if (!check_card && bookingType == 'instant') {
                $('.cardfields-note').removeClass('d-none')
                $('.cardfields-div').removeClass('d-none')
            }
        })

        $(document).on('click', '.stcPay', function() {
            $('#wallet-pay').prop('disabled', false)
            $('.coupon-main').show()
            $('#booking_type_1').val(0)
            $('#paymentMethodId').val(12)
            if (!check_card && bookingType == 'instant') {
                $('.cardfields-note').removeClass('d-none')
                $('.cardfields-div').removeClass('d-none')
            }
        })
    </script>
    <script>
        // document.getElementById('goBack').addEventListener('click', function (event) {
        //     event.preventDefault() // Prevent the default link behavior
        //     // history.back(); // Navigate back in the browser's history
        //     window.top.close()

        // })

        // Back Button Function
       function customGoBack() {
            const referrer = document.referrer;
            const currentHost = window.location.host;

            if (referrer && new URL(referrer).host === currentHost) {
                window.history.back();
            } else {
                window.location.href = '/';
            }
        }
        $(document).ready(function() {
            $('.mb-slid2').slick({
                dots: 1,
                arrows: 1,
                autoplay: true,
                infinite: false,
                slidesToShow: 1,
                slidesToScroll: 1,
                pauseOnHover: 1,
                draggable: 0,
                // Check for the data-locale attribute and set rtl if it's 'ar'
                rtl: document.getElementById('pr-sld').getAttribute('data-locale') === 'ar'
            })

        })
        var i = $('.pagingInfo')
        $('.mb-slid2').on('init reInit afterChange', function(t, n, a, e) {
            i.text((a || 0) + 1 + '/' + n.slideCount)
        })
        /**** modal review js ****/

        var i = $('.pagingInfo')
        $('.mb-slid2').on('init reInit afterChange', function(t, n, a, e) {
            i.text((a || 0) + 1 + '/' + n.slideCount)
        })
        /**** modal review js ****/
    </script>

<script>
   function openWhatsAppChat() {
                                                    // startDate: $('#url_checkin').val(),
                                                    // endDate: $('#url_checkout').val(),
                                                    const propertyId = '{{ $result->id }}';
                                                    const propertyCode = '{{ $result->property_code }}';
                                                    const propertyName = '{{ $result->name }}';
                                                    const checkIn =  $('#url_checkin').val() ;//document.getElementById('startDate').value;
                                                    const checkOut = $('#url_checkout').val();//document.getElementById('endDate').value;
                                                    
                                                    const message = `Hi Darent Team:\nI want to book this property\nProperty Details:\nID: ${propertyId}\nCode: ${propertyCode}\nName: ${propertyName}\nCheck-in: ${checkIn}\nCheck-out: ${checkOut}`;
                                                    
                                                    const whatsappUrl = `https://wa.me/+966920033870?text=${encodeURIComponent(message)}`;
                                                    window.open(whatsappUrl, '_blank');
                                                }
 </script>
@endpush
