// Global Config for Providers
const EventProviders = {
    'ga': {
        'enabled': typeof enable_google_analytics !== 'undefined',
        'handler': function(event, data) {
            window['gtag']('event', event, data);
        },
    },
    'pix': {
        'enabled': typeof enable_meta_pixel !== 'undefined',
        'handler': function(event, data) {
            window['fbq']('track', event, data);
        }
    },
    'snap': {
        'enabled': typeof enable_snapchat !== 'undefined',
        'handler': function(event, data) {
            window['snaptr']('track', event, data);
        }
    },
    'tik': {
        'enabled': typeof enable_tiktok !== 'undefined',
        'handler': function(event, data) {
            window['ttq'].track(event, data);
        }
    }

}

function trackEvent(event, data, provider = null) {
    // if there is a specific provider
    // then send to that specific provider
    if (!provider) {
        // send to all enabled providers
        for (const provider of EventProviders) {
            if (provider.enabled) {
                provider.handler(event, data);
            }
        }

        return
    }

    // provider exists but is maybe an array or string
    if (!Array.isArray(provider)) {
        provider = [provider]
    }

    for (let pro of provider) {
        pro = EventProviders[pro]

        if (pro.enabled) {
            pro.handler(event, data);
        }
    }
}
