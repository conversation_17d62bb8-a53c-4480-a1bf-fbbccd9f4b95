APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:dppw3V4M5HeDBEuTH9ovfN1NB09PB8E9MiU7RHD7hGU=
APP_DEBUG=true
APP_URL=http://127.0.0.1:8000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=athar
DB_USERNAME=root
DB_PASSWORD=

DB_CONNECTION=mysql
DB_HOST=one-studio.cotendcccjls.us-east-1.rds.amazonaws.com
DB_PORT=3306
DB_DATABASE=athar
DB_USERNAME=admin
DB_PASSWORD="vsam2Y6F!jGSS#f"

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=public
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false
AWS_SNS_REGION=eu-central-1
AWS_SNS_ACCESS_KEY=
AWS_SNS_SECRET_ACCESS_KEY=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Unified Event Tracking Configuration
EVENT_TRACKING_ENABLE=true
EVENT_TRACKING_QUEUE=default
EVENT_TRACKING_DEBUG=true

# MoEngage Configuration
MOENGAGE_ENABLE=false
MOENGAGE_APP_ID=
MOENGAGE_CAMPAIGN_EVENTS_KEY=
MOENGAGE_API_BASE_URL=https://api-02.moengage.com

# AppsFlyer Configuration
APPSFLYER_ENABLE=false
APPSFLYER_DEV_KEY=
APPSFLYER_APP_ID=
APPSFLYER_API_BASE_URL=https://api2.appsflyer.com

# Google reCAPTCHA v3 Configuration
RECAPTCHA_ENABLE=true
RECAPTCHA_V3_SITE_KEY=
RECAPTCHA_V3_SECRET_KEY=
RECAPTCHA_V3_SC0RE=0.5
RECAPTCHA_V3_ALLOW=true
